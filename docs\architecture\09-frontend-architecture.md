# IntelliFin Architecture - Frontend Architecture

## 9. Frontend Architecture

This section provides detailed technical guidance for the Next.js frontend implementation, ensuring consistency, maintainability, and optimal performance for IntelliFin's sophisticated conversational interface.

### 9.1 Project Structure & Organization

The frontend application will be organized within the monorepo structure as `apps/frontend-nextjs/` with the following directory organization:

```
apps/frontend-nextjs/
├── src/
│   ├── app/                    # Next.js 14 App Router
│   │   ├── (auth)/            # Authentication routes (grouped)
│   │   │   ├── login/
│   │   │   └── register/
│   │   ├── dashboard/         # Main conversational workspace
│   │   ├── settings/          # User settings and account management
│   │   ├── globals.css        # Global styles and Tailwind imports
│   │   ├── layout.tsx         # Root layout with providers
│   │   └── page.tsx           # Landing page
│   ├── components/            # Reusable UI components
│   │   ├── ui/               # Shadcn/ui components (copied and customized)
│   │   │   ├── button.tsx
│   │   │   ├── input.tsx
│   │   │   ├── card.tsx
│   │   │   └── ...
│   │   ├── conversation/     # Conversational interface components
│   │   │   ├── CommandBar.tsx
│   │   │   ├── ChatHistory.tsx
│   │   │   ├── MessageBubble.tsx
│   │   │   └── ...
│   │   ├── financial/        # Financial data display components
│   │   │   ├── TransactionList.tsx
│   │   │   ├── InvoiceDraft.tsx
│   │   │   ├── FinancialSummary.tsx
│   │   │   └── ...
│   │   └── layout/           # Layout and navigation components
│   │       ├── Header.tsx
│   │       ├── Sidebar.tsx
│   │       └── ...
│   ├── hooks/                # Custom React hooks
│   │   ├── useWebSocket.ts   # WebSocket connection management
│   │   ├── useConversation.ts # Conversational state management
│   │   ├── useAuth.ts        # Authentication state
│   │   └── ...
│   ├── stores/               # Zustand state stores
│   │   ├── authStore.ts      # Authentication state
│   │   ├── conversationStore.ts # Conversational state
│   │   ├── financialStore.ts # Financial data state
│   │   └── ...
│   ├── services/             # API and external service integrations
│   │   ├── api/             # REST API client
│   │   │   ├── client.ts    # Axios instance and interceptors
│   │   │   ├── auth.ts      # Authentication API calls
│   │   │   ├── transactions.ts # Transaction API calls
│   │   │   └── ...
│   │   ├── websocket.ts     # WebSocket service for conversational gateway
│   │   └── ...
│   ├── types/                # TypeScript type definitions
│   │   ├── api.ts           # API response types
│   │   ├── conversation.ts  # Conversational interface types
│   │   ├── financial.ts     # Financial data types
│   │   └── ...
│   ├── utils/                # Utility functions
│   │   ├── formatting.ts    # Date, currency, number formatting
│   │   ├── validation.ts    # Form validation utilities
│   │   ├── constants.ts     # Application constants
│   │   └── ...
│   └── lib/                  # Third-party library configurations
│       ├── tailwind.ts      # Tailwind CSS configuration
│       ├── utils.ts         # Shadcn/ui utilities
│       └── ...
├── public/                   # Static assets
│   ├── icons/               # PWA icons
│   ├── manifest.json        # PWA manifest
│   └── ...
├── next.config.js           # Next.js configuration
├── tailwind.config.js       # Tailwind CSS configuration
├── tsconfig.json            # TypeScript configuration
└── package.json             # Dependencies and scripts
```

### 9.2 Component Architecture & Patterns

#### 9.2.1 Component Organization Principles

*   **Atomic Design Methodology:** Components will follow atomic design principles (atoms, molecules, organisms, templates, pages) for consistent composition and reusability.
*   **Single Responsibility:** Each component will have a single, well-defined purpose and responsibility.
*   **Props Interface:** All components will have explicit TypeScript interfaces for props, ensuring type safety and clear contracts.
*   **Composition over Inheritance:** Prefer component composition and prop drilling over complex inheritance hierarchies.

#### 9.2.2 Core Component Patterns

**Conversational Interface Components:**
```typescript
// Example: CommandBar component pattern
interface CommandBarProps {
  onCommandSubmit: (command: string) => void;
  isLoading?: boolean;
  placeholder?: string;
}

const CommandBar: React.FC<CommandBarProps> = ({
  onCommandSubmit,
  isLoading = false,
  placeholder = "Ask me anything about your finances..."
}) => {
  // Implementation
};
```

**Financial Data Components:**
```typescript
// Example: TransactionList component pattern
interface TransactionListProps {
  transactions: Transaction[];
  onTransactionUpdate: (id: string, updates: Partial<Transaction>) => void;
  isLoading?: boolean;
  error?: string;
}
```

### 9.3 State Management Strategy

#### 9.3.1 Zustand Store Architecture

**Authentication Store:**
```typescript
interface AuthStore {
  user: User | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  login: (email: string, password: string) => Promise<void>;
  logout: () => void;
  refreshToken: () => Promise<void>;
}
```

**Conversation Store:**
```typescript
interface ConversationStore {
  messages: Message[];
  isConnected: boolean;
  isLoading: boolean;
  sendMessage: (command: string) => Promise<void>;
  clearHistory: () => void;
}
```

**Financial Store:**
```typescript
interface FinancialStore {
  transactions: Transaction[];
  categories: Category[];
  accounts: FinancialAccount[];
  summary: FinancialSummary | null;
  fetchTransactions: () => Promise<void>;
  updateTransaction: (id: string, updates: Partial<Transaction>) => Promise<void>;
}
```

### 9.4 WebSocket Integration

#### 9.4.1 WebSocket Service

```typescript
class WebSocketService {
  private socket: WebSocket | null = null;
  private reconnectAttempts = 0;
  private maxReconnectAttempts = 5;

  connect(token: string): Promise<void> {
    // Implementation for secure WebSocket connection
  }

  sendCommand(command: string): void {
    // Implementation for sending commands
  }

  onMessage(callback: (data: any) => void): void {
    // Implementation for message handling
  }

  disconnect(): void {
    // Implementation for clean disconnection
  }
}
```

#### 9.4.2 WebSocket Hook

```typescript
const useWebSocket = () => {
  const [isConnected, setIsConnected] = useState(false);
  const [lastMessage, setLastMessage] = useState<any>(null);

  const sendCommand = useCallback((command: string) => {
    // Implementation
  }, []);

  return { isConnected, lastMessage, sendCommand };
};
```

### 9.5 API Integration

#### 9.5.1 REST API Client

```typescript
// Axios instance with interceptors
const apiClient = axios.create({
  baseURL: process.env.NEXT_PUBLIC_API_URL,
  timeout: 10000,
});

// Request interceptor for JWT
apiClient.interceptors.request.use((config) => {
  const token = getToken();
  if (token) {
    config.headers.Authorization = `Bearer ${token}`;
  }
  return config;
});

// Response interceptor for error handling
apiClient.interceptors.response.use(
  (response) => response,
  (error) => {
    if (error.response?.status === 401) {
      // Handle token refresh or logout
    }
    return Promise.reject(error);
  }
);
```

### 9.6 Performance Optimization

#### 9.6.1 Code Splitting & Lazy Loading

*   **Route-based Code Splitting:** Next.js automatic code splitting for pages
*   **Component Lazy Loading:** Lazy load heavy components using `React.lazy()`
*   **Dynamic Imports:** Use dynamic imports for conditional component loading

#### 9.6.2 Caching Strategy

*   **SWR for Data Fetching:** Implement SWR for intelligent caching and revalidation
*   **Service Worker:** PWA service worker for offline functionality and caching
*   **Browser Caching:** Proper cache headers for static assets

#### 9.6.3 Bundle Optimization

*   **Tree Shaking:** Ensure unused code is eliminated from the bundle
*   **Image Optimization:** Next.js Image component for optimized image delivery
*   **Font Optimization:** Optimize font loading with `next/font`

### 9.7 Progressive Web App (PWA) Features

#### 9.7.1 PWA Configuration

```json
// public/manifest.json
{
  "name": "IntelliFin",
  "short_name": "IntelliFin",
  "description": "AI-powered financial management for Zambian SMEs",
  "start_url": "/",
  "display": "standalone",
  "background_color": "#ffffff",
  "theme_color": "#000000",
  "icons": [
    {
      "src": "/icons/icon-192x192.png",
      "sizes": "192x192",
      "type": "image/png"
    }
  ]
}
```

#### 9.7.2 Service Worker

*   **Offline Support:** Cache essential resources for offline functionality
*   **Background Sync:** Queue actions when offline and sync when online
*   **Push Notifications:** Real-time notifications for important financial events

### 9.8 Error Handling & Monitoring

#### 9.8.1 Error Boundaries

```typescript
class ErrorBoundary extends React.Component<Props, State> {
  constructor(props: Props) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(error: Error): State {
    return { hasError: true };
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    // Log error to monitoring service
    console.error('Error caught by boundary:', error, errorInfo);
  }

  render() {
    if (this.state.hasError) {
      return <ErrorFallback onReset={() => this.setState({ hasError: false })} />;
    }

    return this.props.children;
  }
}
```

#### 9.8.2 Error Monitoring

*   **Sentry Integration:** Real-time error tracking and performance monitoring
*   **User Feedback:** In-app error reporting with user context
*   **Error Recovery:** Graceful degradation and recovery mechanisms

### 9.9 Testing Strategy

#### 9.9.1 Testing Pyramid

*   **Unit Tests:** Jest + React Testing Library for component testing
*   **Integration Tests:** Testing component interactions and API calls
*   **E2E Tests:** Playwright for critical user journeys
*   **Visual Regression Tests:** Storybook for component visual testing

#### 9.9.2 Test Examples

```typescript
// Component test example
describe('CommandBar', () => {
  it('should submit command when user presses enter', async () => {
    const mockSubmit = jest.fn();
    render(<CommandBar onCommandSubmit={mockSubmit} />);
    
    const input = screen.getByPlaceholderText(/ask me anything/i);
    fireEvent.change(input, { target: { value: 'show profit' } });
    fireEvent.keyPress(input, { key: 'Enter', code: 'Enter' });
    
    expect(mockSubmit).toHaveBeenCalledWith('show profit');
  });
});
```

### 9.10 Accessibility (WCAG AA Compliance)

#### 9.10.1 Accessibility Features

*   **Keyboard Navigation:** Full keyboard accessibility for all interactive elements
*   **Screen Reader Support:** Proper ARIA labels and semantic HTML
*   **Color Contrast:** WCAG AA compliant color contrast ratios
*   **Focus Management:** Clear focus indicators and logical tab order

#### 9.10.2 Accessibility Testing

*   **Automated Testing:** axe-core for automated accessibility testing
*   **Manual Testing:** Keyboard navigation and screen reader testing
*   **User Testing:** Testing with users who rely on assistive technologies

---

**Previous Section:** [Core Financial Engine & Accounting Logic](08-core-financial-engine.md)  
**Back to:** [Architecture Overview](../architecture.md) 