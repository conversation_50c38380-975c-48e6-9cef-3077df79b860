package com.intellifin.repository;

import com.intellifin.model.Conversation;
import com.intellifin.model.User;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

/**
 * Repository interface for managing Conversation entities.
 * Provides methods for CRUD operations and custom queries related to user conversations.
 */
@Repository
public interface ConversationRepository extends JpaRepository<Conversation, UUID> {

    /**
     * Finds a conversation by its session ID.
     *
     * @param sessionId The unique session identifier
     * @return An Optional containing the conversation if found, empty otherwise
     */
    Optional<Conversation> findBySessionId(String sessionId);

    /**
     * Finds an active conversation by its session ID.
     *
     * @param sessionId The unique session identifier
     * @return An Optional containing the active conversation if found, empty otherwise
     */
    @Query("SELECT c FROM Conversation c WHERE c.sessionId = :sessionId AND c.status = 'ACTIVE'")
    Optional<Conversation> findActiveBySessionId(@Param("sessionId") String sessionId);

    /**
     * Finds all conversations for a specific user, ordered by most recent activity.
     *
     * @param user The user whose conversations to retrieve
     * @return A list of conversations ordered by last activity time (descending)
     */
    List<Conversation> findByUserOrderByLastActivityAtDesc(User user);

    /**
     * Finds all active conversations for a specific user, ordered by most recent activity.
     *
     * @param user The user whose active conversations to retrieve
     * @return A list of active conversations ordered by last activity time (descending)
     */
    @Query("SELECT c FROM Conversation c WHERE c.user = :user AND c.status = 'ACTIVE' ORDER BY c.lastActivityAt DESC")
    List<Conversation> findActiveByUser(@Param("user") User user);

    /**
     * Finds conversations for a specific user with pagination support.
     *
     * @param user The user whose conversations to retrieve
     * @param pageable Pagination information (page number, size, sorting)
     * @return A page of conversations ordered by last activity time (descending)
     */
    Page<Conversation> findByUserOrderByLastActivityAtDesc(User user, Pageable pageable);

    /**
     * Finds conversations for a specific user with a specific status.
     *
     * @param user The user whose conversations to retrieve
     * @param status The conversation status to filter by (ACTIVE, ARCHIVED, etc.)
     * @return A list of conversations matching the criteria, ordered by last activity time (descending)
     */
    List<Conversation> findByUserAndStatusOrderByLastActivityAtDesc(User user, Conversation.ConversationStatus status);

    /**
     * Finds recent conversations for a specific user that have activity after a specified time.
     *
     * @param user The user whose conversations to retrieve
     * @param since The datetime threshold for considering conversations as recent
     * @return A list of recent conversations ordered by last activity time (descending)
     */
    @Query("SELECT c FROM Conversation c WHERE c.user = :user AND c.lastActivityAt >= :since ORDER BY c.lastActivityAt DESC")
    List<Conversation> findRecentByUser(@Param("user") User user, @Param("since") LocalDateTime since);

    /**
     * Finds all conversations across all users with activity after a specified time.
     *
     * @param since The datetime threshold for filtering conversations
     * @return A list of conversations with recent activity, ordered by last activity time (descending)
     */
    @Query("SELECT c FROM Conversation c WHERE c.lastActivityAt >= :since ORDER BY c.lastActivityAt DESC")
    List<Conversation> findWithActivitySince(@Param("since") LocalDateTime since);

    /**
     * Finds conversations marked as active but with no recent activity (for cleanup purposes).
     *
     * @param cutoff The datetime threshold for considering a conversation as inactive
     * @return A list of inactive conversations that are candidates for cleanup
     */
    @Query("SELECT c FROM Conversation c WHERE c.status = 'ACTIVE' AND c.lastActivityAt < :cutoff")
    List<Conversation> findInactiveConversations(@Param("cutoff") LocalDateTime cutoff);

    /**
     * Counts the total number of conversations for a specific user.
     *
     * @param user The user whose conversations to count
     * @return The total count of conversations
     */
    long countByUser(User user);

    /**
     * Counts the number of active conversations for a specific user.
     *
     * @param user The user whose active conversations to count
     * @return The count of active conversations
     */
    @Query("SELECT COUNT(c) FROM Conversation c WHERE c.user = :user AND c.status = 'ACTIVE'")
    long countActiveByUser(@Param("user") User user);

    /**
     * Updates the status of a specific conversation.
     *
     * @param conversationId The UUID of the conversation to update
     * @param status The new status to set
     */
    @Modifying
    @Query("UPDATE Conversation c SET c.status = :status WHERE c.id = :conversationId")
    void updateStatus(@Param("conversationId") UUID conversationId, @Param("status") Conversation.ConversationStatus status);

    /**
     * Updates the last activity timestamp for a specific conversation.
     *
     * @param conversationId The UUID of the conversation to update
     * @param lastActivity The new last activity timestamp
     */
    @Modifying
    @Query("UPDATE Conversation c SET c.lastActivityAt = :lastActivity WHERE c.id = :conversationId")
    void updateLastActivity(@Param("conversationId") UUID conversationId, @Param("lastActivity") LocalDateTime lastActivity);

    /**
     * Archives old conversations that have been inactive for a period of time.
     *
     * @param cutoff The datetime threshold for considering a conversation as old
     * @return The number of conversations that were archived
     */
    @Modifying
    @Query("UPDATE Conversation c SET c.status = 'ARCHIVED' WHERE c.status = 'ACTIVE' AND c.lastActivityAt < :cutoff")
    int archiveOldConversations(@Param("cutoff") LocalDateTime cutoff);

    /**
     * Deletes conversations with a specific status that were last updated before a specified time.
     *
     * @param status The status of conversations to delete
     * @param cutoff The datetime threshold for deletion
     * @return The number of conversations that were deleted
     */
    @Modifying
    @Query("DELETE FROM Conversation c WHERE c.status = :status AND c.updatedAt < :cutoff")
    int deleteByStatusAndUpdatedBefore(@Param("status") Conversation.ConversationStatus status, @Param("cutoff") LocalDateTime cutoff);

    /**
     * Searches for conversations by title for a specific user.
     *
     * @param user The user whose conversations to search
     * @param searchTerm The search term to match against conversation titles (case-insensitive)
     * @return A list of matching conversations ordered by last activity time (descending)
     */
    @Query("SELECT c FROM Conversation c WHERE c.user = :user AND LOWER(c.title) LIKE LOWER(CONCAT('%', :searchTerm, '%')) ORDER BY c.lastActivityAt DESC")
    List<Conversation> findByUserAndTitleContaining(@Param("user") User user, @Param("searchTerm") String searchTerm);

    /**
     * Retrieves conversation statistics for a specific user.
     *
     * @param user The user whose conversation statistics to retrieve
     * @return An Object array containing:
     *         - totalConversations: Total number of conversations
     *         - activeConversations: Number of active conversations
     *         - archivedConversations: Number of archived conversations
     *         - lastActivity: Timestamp of the most recent conversation activity
     */
    @Query("SELECT " +
           "COUNT(c) as totalConversations, " +
           "COUNT(CASE WHEN c.status = 'ACTIVE' THEN 1 END) as activeConversations, " +
           "COUNT(CASE WHEN c.status = 'ARCHIVED' THEN 1 END) as archivedConversations, " +
           "MAX(c.lastActivityAt) as lastActivity " +
           "FROM Conversation c WHERE c.user = :user")
    Object[] getConversationStats(@Param("user") User user);
}
