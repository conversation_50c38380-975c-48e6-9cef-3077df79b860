import { apiClient } from './client';
import {
  Invoice,
  CreateInvoiceRequest,
  UpdateInvoiceRequest,
  InvoiceQueryParams,
  PaginatedResponse,
  Client,
  CreateClientRequest,
  UpdateClientRequest
} from '@/types/api';

/**
 * Invoices API Service
 * Handles all invoice-related API calls
 */
export class InvoicesService {
  /**
   * Get paginated list of invoices
   */
  static async getInvoices(params?: InvoiceQueryParams): Promise<PaginatedResponse<Invoice>> {
    const queryString = params ? new URLSearchParams(params as any).toString() : '';
    const url = `/api/v1/invoices${queryString ? `?${queryString}` : ''}`;
    
    const response = await apiClient.get<PaginatedResponse<Invoice>>(url);
    return response.data;
  }

  /**
   * Get single invoice by ID
   */
  static async getInvoice(id: string): Promise<Invoice> {
    const response = await apiClient.get<Invoice>(`/api/v1/invoices/${id}`);
    return response.data;
  }

  /**
   * Create new invoice
   */
  static async createInvoice(invoice: CreateInvoiceRequest): Promise<Invoice> {
    const response = await apiClient.post<Invoice>('/api/v1/invoices', invoice);
    return response.data;
  }

  /**
   * Update existing invoice
   */
  static async updateInvoice(id: string, updates: UpdateInvoiceRequest): Promise<Invoice> {
    const response = await apiClient.put<Invoice>(`/api/v1/invoices/${id}`, updates);
    return response.data;
  }

  /**
   * Delete invoice (only if draft)
   */
  static async deleteInvoice(id: string): Promise<void> {
    await apiClient.delete(`/api/v1/invoices/${id}`);
  }

  /**
   * Submit invoice to ZRA
   */
  static async submitToZRA(id: string): Promise<{
    status: 'SUCCESS' | 'PENDING' | 'FAILED';
    zraInvoiceId?: string;
    message: string;
    submissionDate: string;
  }> {
    const response = await apiClient.post(`/api/v1/invoices/${id}/submit-zra`);
    return response.data;
  }

  /**
   * Check ZRA submission status
   */
  static async checkZRAStatus(id: string): Promise<{
    status: 'PENDING' | 'SUBMITTED' | 'FAILED';
    zraInvoiceId?: string;
    lastChecked: string;
    errorMessage?: string;
  }> {
    const response = await apiClient.get(`/api/v1/invoices/${id}/zra-status`);
    return response.data;
  }

  /**
   * Mark invoice as paid
   */
  static async markAsPaid(id: string, paymentDetails?: {
    paymentDate?: string;
    paymentMethod?: string;
    reference?: string;
    notes?: string;
  }): Promise<Invoice> {
    const response = await apiClient.post<Invoice>(`/api/v1/invoices/${id}/mark-paid`, paymentDetails);
    return response.data;
  }

  /**
   * Send invoice to client via email
   */
  static async sendInvoice(id: string, emailData?: {
    to?: string;
    subject?: string;
    message?: string;
  }): Promise<{
    status: 'SENT' | 'FAILED';
    message: string;
    sentAt: string;
  }> {
    const response = await apiClient.post(`/api/v1/invoices/${id}/send`, emailData);
    return response.data;
  }

  /**
   * Generate invoice PDF
   */
  static async generatePDF(id: string): Promise<Blob> {
    const response = await apiClient.axios.get(`/api/v1/invoices/${id}/pdf`, {
      responseType: 'blob'
    });
    return response.data;
  }

  /**
   * Duplicate invoice
   */
  static async duplicateInvoice(id: string): Promise<Invoice> {
    const response = await apiClient.post<Invoice>(`/api/v1/invoices/${id}/duplicate`);
    return response.data;
  }

  /**
   * Get invoice statistics
   */
  static async getInvoiceStats(params?: {
    startDate?: string;
    endDate?: string;
  }): Promise<{
    totalInvoices: number;
    totalValue: number;
    paidInvoices: number;
    paidValue: number;
    overdueInvoices: number;
    overdueValue: number;
    averageValue: number;
    averagePaymentDays: number;
  }> {
    const queryString = params ? new URLSearchParams(params as any).toString() : '';
    const url = `/api/v1/invoices/stats${queryString ? `?${queryString}` : ''}`;
    
    const response = await apiClient.get(url);
    return response.data;
  }

  /**
   * Get overdue invoices
   */
  static async getOverdueInvoices(): Promise<Invoice[]> {
    const response = await apiClient.get<Invoice[]>('/api/v1/invoices/overdue');
    return response.data;
  }

  /**
   * Get recent invoices
   */
  static async getRecentInvoices(limit: number = 10): Promise<Invoice[]> {
    const response = await apiClient.get<Invoice[]>(`/api/v1/invoices/recent?limit=${limit}`);
    return response.data;
  }

  /**
   * Export invoices to CSV/Excel
   */
  static async exportInvoices(params?: InvoiceQueryParams & {
    format?: 'csv' | 'excel';
  }): Promise<Blob> {
    const queryString = params ? new URLSearchParams(params as any).toString() : '';
    const url = `/api/v1/invoices/export${queryString ? `?${queryString}` : ''}`;
    
    const response = await apiClient.axios.get(url, {
      responseType: 'blob'
    });
    return response.data;
  }

  /**
   * Get next invoice number
   */
  static async getNextInvoiceNumber(): Promise<{ invoiceNumber: string }> {
    const response = await apiClient.get<{ invoiceNumber: string }>('/api/v1/invoices/next-number');
    return response.data;
  }
}

/**
 * Clients API Service
 * Handles client management for invoicing
 */
export class ClientsService {
  /**
   * Get all clients
   */
  static async getClients(): Promise<Client[]> {
    const response = await apiClient.get<Client[]>('/api/v1/clients');
    return response.data;
  }

  /**
   * Get single client by ID
   */
  static async getClient(id: string): Promise<Client> {
    const response = await apiClient.get<Client>(`/api/v1/clients/${id}`);
    return response.data;
  }

  /**
   * Create new client
   */
  static async createClient(client: CreateClientRequest): Promise<Client> {
    const response = await apiClient.post<Client>('/api/v1/clients', client);
    return response.data;
  }

  /**
   * Update existing client
   */
  static async updateClient(id: string, updates: UpdateClientRequest): Promise<Client> {
    const response = await apiClient.put<Client>(`/api/v1/clients/${id}`, updates);
    return response.data;
  }

  /**
   * Delete client
   */
  static async deleteClient(id: string): Promise<void> {
    await apiClient.delete(`/api/v1/clients/${id}`);
  }

  /**
   * Search clients
   */
  static async searchClients(query: string): Promise<Client[]> {
    const response = await apiClient.get<Client[]>(`/api/v1/clients/search?q=${encodeURIComponent(query)}`);
    return response.data;
  }

  /**
   * Get client invoices
   */
  static async getClientInvoices(clientId: string): Promise<Invoice[]> {
    const response = await apiClient.get<Invoice[]>(`/api/v1/clients/${clientId}/invoices`);
    return response.data;
  }

  /**
   * Get client statistics
   */
  static async getClientStats(clientId: string): Promise<{
    totalInvoices: number;
    totalValue: number;
    paidValue: number;
    outstandingValue: number;
    averagePaymentDays: number;
    lastInvoiceDate: string;
  }> {
    const response = await apiClient.get(`/api/v1/clients/${clientId}/stats`);
    return response.data;
  }
}

// Export default instances
export const invoicesService = InvoicesService;
export const clientsService = ClientsService;
