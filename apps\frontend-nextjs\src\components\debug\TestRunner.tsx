"use client"

import React, { useState } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Check<PERSON>ircle, XCircle, Clock, Play } from 'lucide-react';
import { useAuthStore, useConversationStore, useFinancialStore } from '@/stores';

interface TestResult {
  name: string;
  status: 'pending' | 'running' | 'passed' | 'failed';
  message?: string;
  duration?: number;
}

export function TestRunner() {
  const [tests, setTests] = useState<TestResult[]>([
    { name: 'Auth Store Initialization', status: 'pending' },
    { name: 'Conversation Store Functionality', status: 'pending' },
    { name: 'Financial Store State', status: 'pending' },
    { name: 'Route Protection Logic', status: 'pending' },
    { name: 'UI Component Rendering', status: 'pending' },
  ]);

  const authStore = useAuthStore();
  const conversationStore = useConversationStore();
  const financialStore = useFinancialStore();

  const updateTest = (index: number, updates: Partial<TestResult>) => {
    setTests(prev => prev.map((test, i) => 
      i === index ? { ...test, ...updates } : test
    ));
  };

  const runTest = async (index: number, testFn: () => Promise<void>) => {
    const startTime = Date.now();
    updateTest(index, { status: 'running' });
    
    try {
      await testFn();
      const duration = Date.now() - startTime;
      updateTest(index, { 
        status: 'passed', 
        message: 'Test completed successfully',
        duration 
      });
    } catch (error) {
      const duration = Date.now() - startTime;
      updateTest(index, { 
        status: 'failed', 
        message: error instanceof Error ? error.message : 'Unknown error',
        duration 
      });
    }
  };

  const testAuthStore = async () => {
    // Test auth store functionality
    if (typeof authStore.initialize !== 'function') {
      throw new Error('Auth store missing initialize method');
    }
    
    if (typeof authStore.login !== 'function') {
      throw new Error('Auth store missing login method');
    }
    
    if (typeof authStore.logout !== 'function') {
      throw new Error('Auth store missing logout method');
    }

    // Test initial state
    if (authStore.isLoading === undefined) {
      throw new Error('Auth store missing isLoading state');
    }

    console.log('✅ Auth Store Test Passed:', {
      hasInitialize: typeof authStore.initialize === 'function',
      hasLogin: typeof authStore.login === 'function',
      hasLogout: typeof authStore.logout === 'function',
      currentState: {
        isAuthenticated: authStore.isAuthenticated,
        isLoading: authStore.isLoading,
        user: authStore.user ? 'Present' : 'Null',
      }
    });
  };

  const testConversationStore = async () => {
    // Test conversation store functionality
    if (typeof conversationStore.addUserMessage !== 'function') {
      throw new Error('Conversation store missing addUserMessage method');
    }

    if (typeof conversationStore.getCommandSuggestions !== 'function') {
      throw new Error('Conversation store missing getCommandSuggestions method');
    }

    // Test adding a message
    const initialMessageCount = conversationStore.messages.length;
    conversationStore.addUserMessage('Test message');
    
    if (conversationStore.messages.length !== initialMessageCount + 1) {
      throw new Error('Message was not added to conversation store');
    }

    // Test suggestions
    const suggestions = conversationStore.getCommandSuggestions();
    if (!Array.isArray(suggestions) || suggestions.length === 0) {
      throw new Error('Command suggestions not working properly');
    }

    console.log('✅ Conversation Store Test Passed:', {
      messageCount: conversationStore.messages.length,
      suggestionsCount: suggestions.length,
      connectionState: conversationStore.connectionState,
    });
  };

  const testFinancialStore = async () => {
    // Test financial store functionality
    if (typeof financialStore.fetchDashboardData !== 'function') {
      throw new Error('Financial store missing fetchDashboardData method');
    }

    if (typeof financialStore.setLoading !== 'function') {
      throw new Error('Financial store missing setLoading method');
    }

    // Test loading state management
    financialStore.setLoading('dashboard', true);
    if (!financialStore.isLoading.dashboard) {
      throw new Error('Loading state not updated correctly');
    }

    financialStore.setLoading('dashboard', false);
    if (financialStore.isLoading.dashboard) {
      throw new Error('Loading state not cleared correctly');
    }

    console.log('✅ Financial Store Test Passed:', {
      hasData: {
        transactions: financialStore.transactions.length,
        accounts: financialStore.accounts.length,
        invoices: financialStore.invoices.length,
      },
      loadingStates: financialStore.isLoading,
    });
  };

  const testRouteProtection = async () => {
    // Test route protection logic
    const isAuthenticated = authStore.isAuthenticated;
    const user = authStore.user;

    // Basic checks
    if (isAuthenticated && !user) {
      throw new Error('User is authenticated but user object is missing');
    }

    if (!isAuthenticated && user) {
      throw new Error('User object exists but not authenticated');
    }

    console.log('✅ Route Protection Test Passed:', {
      isAuthenticated,
      hasUser: !!user,
      onboardingStatus: user?.onboardingStatus || 'N/A',
    });
  };

  const testUIComponents = async () => {
    // Test UI component rendering
    const dashboardElement = document.querySelector('[data-testid="dashboard"]');
    const conversationElement = document.querySelector('[data-testid="conversation"]');
    
    // Check if main elements exist
    const headerExists = document.querySelector('header') !== null;
    const mainExists = document.querySelector('main') !== null;
    
    if (!headerExists) {
      throw new Error('Dashboard header not found');
    }

    if (!mainExists) {
      throw new Error('Main content area not found');
    }

    console.log('✅ UI Components Test Passed:', {
      headerExists,
      mainExists,
      elementsFound: {
        dashboard: !!dashboardElement,
        conversation: !!conversationElement,
      }
    });
  };

  const runAllTests = async () => {
    const testFunctions = [
      testAuthStore,
      testConversationStore,
      testFinancialStore,
      testRouteProtection,
      testUIComponents,
    ];

    for (let i = 0; i < testFunctions.length; i++) {
      await runTest(i, testFunctions[i]);
      // Small delay between tests
      await new Promise(resolve => setTimeout(resolve, 500));
    }
  };

  const getStatusIcon = (status: TestResult['status']) => {
    switch (status) {
      case 'passed':
        return <CheckCircle className="w-4 h-4 text-green-500" />;
      case 'failed':
        return <XCircle className="w-4 h-4 text-red-500" />;
      case 'running':
        return <Clock className="w-4 h-4 text-yellow-500 animate-spin" />;
      default:
        return <div className="w-4 h-4 rounded-full bg-gray-300" />;
    }
  };

  const getStatusBadge = (status: TestResult['status']) => {
    const variants = {
      pending: 'secondary',
      running: 'default',
      passed: 'default',
      failed: 'destructive',
    } as const;

    const colors = {
      pending: 'bg-gray-100 text-gray-700',
      running: 'bg-yellow-100 text-yellow-700',
      passed: 'bg-green-100 text-green-700',
      failed: 'bg-red-100 text-red-700',
    };

    return (
      <Badge variant={variants[status]} className={colors[status]}>
        {status.toUpperCase()}
      </Badge>
    );
  };

  if (process.env.NODE_ENV !== 'development') {
    return null;
  }

  return (
    <Card className="fixed top-4 right-4 w-96 z-50 bg-blue-50 border-blue-200 max-h-[80vh] overflow-y-auto">
      <CardHeader className="pb-3">
        <CardTitle className="text-sm text-blue-800 flex items-center justify-between">
          🧪 Test Runner
          <Button
            size="sm"
            onClick={runAllTests}
            className="bg-blue-600 hover:bg-blue-700 text-white"
          >
            <Play className="w-3 h-3 mr-1" />
            Run All
          </Button>
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-3">
        {tests.map((test, index) => (
          <div key={test.name} className="flex items-center justify-between p-2 bg-white rounded border">
            <div className="flex items-center space-x-2">
              {getStatusIcon(test.status)}
              <div>
                <div className="text-sm font-medium">{test.name}</div>
                {test.message && (
                  <div className="text-xs text-gray-500 truncate max-w-48">
                    {test.message}
                  </div>
                )}
                {test.duration && (
                  <div className="text-xs text-gray-400">
                    {test.duration}ms
                  </div>
                )}
              </div>
            </div>
            {getStatusBadge(test.status)}
          </div>
        ))}
        
        <div className="text-xs text-blue-700 pt-2 border-t">
          Open browser console for detailed test output
        </div>
      </CardContent>
    </Card>
  );
}
