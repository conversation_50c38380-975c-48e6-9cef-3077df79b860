# Messaging Abstraction Layer Architecture

## Overview

IntelliFin implements a hybrid messaging strategy that enables zero-cost local development with RabbitMQ while providing enterprise-grade messaging in production with Azure Service Bus. This is achieved through abstraction layers that decouple business logic from specific message broker implementations.

## Core Principles

1. **Environment Independence**: Business logic remains identical across local and cloud environments
2. **Broker Abstraction**: Services interact with messaging through abstraction layers, not directly with brokers
3. **Configuration-Driven**: Environment switching is achieved through configuration profiles, not code changes
4. **Consistent Error Handling**: Dead-letter queue processing works uniformly across environments
5. **Zero-Cost Development**: Local development incurs no cloud messaging costs

## Architecture Components

### Java Services - Spring Cloud Stream

Spring Cloud Stream provides the abstraction layer for Java services, enabling broker-agnostic messaging through binder implementations.

#### Configuration Structure

```yaml
# application-local.yml (Local Development)
spring:
  cloud:
    stream:
      binders:
        rabbit:
          type: rabbit
          environment:
            spring:
              rabbitmq:
                host: localhost
                port: 5672
                username: guest
                password: guest
      bindings:
        transactionEvents-out-0:
          destination: transaction.events
          binder: rabbit
        invoiceEvents-out-0:
          destination: invoice.events
          binder: rabbit

# application-cloud.yml (Production/Staging)
spring:
  cloud:
    stream:
      binders:
        servicebus:
          type: servicebus
          environment:
            spring:
              cloud:
                azure:
                  servicebus:
                    connection-string: ${AZURE_SERVICEBUS_CONNECTION_STRING}
      bindings:
        transactionEvents-out-0:
          destination: transaction.events
          binder: servicebus
        invoiceEvents-out-0:
          destination: invoice.events
          binder: servicebus
```

#### Implementation Pattern

```java
@Component
public class TransactionEventPublisher {
    
    @Autowired
    private StreamBridge streamBridge;
    
    public void publishTransactionReceived(TransactionReceivedEvent event) {
        streamBridge.send("transactionEvents-out-0", event);
    }
    
    @EventListener
    public void handleTransactionCategorized(TransactionCategorizedEvent event) {
        streamBridge.send("transactionEvents-out-0", event);
    }
}

@Component
public class TransactionEventConsumer {
    
    @Bean
    public Consumer<TransactionReceivedEvent> handleTransactionReceived() {
        return event -> {
            // Process transaction received event
            log.info("Processing transaction: {}", event.getTransactionId());
            // Business logic here
        };
    }
    
    @Bean
    public Consumer<TransactionCategorizedEvent> handleTransactionCategorized() {
        return event -> {
            // Process categorization event
            log.info("Transaction categorized: {}", event.getTransactionId());
            // Business logic here
        };
    }
}
```

### Python Services - Custom MessagingService

Python services use a custom MessagingService wrapper that provides a unified interface across different broker implementations.

#### Interface Definition

```python
from abc import ABC, abstractmethod
from typing import Any, Callable, Dict, Optional
from dataclasses import dataclass

@dataclass
class MessageEvent:
    event_type: str
    payload: Dict[str, Any]
    correlation_id: Optional[str] = None
    timestamp: Optional[str] = None

class MessagingService(ABC):
    
    @abstractmethod
    async def publish_event(self, topic: str, event: MessageEvent) -> bool:
        """Publish an event to the specified topic"""
        pass
    
    @abstractmethod
    async def subscribe(self, topic: str, handler: Callable[[MessageEvent], None]) -> None:
        """Subscribe to events on the specified topic"""
        pass
    
    @abstractmethod
    async def start(self) -> None:
        """Start the messaging service"""
        pass
    
    @abstractmethod
    async def stop(self) -> None:
        """Stop the messaging service"""
        pass
```

#### RabbitMQ Implementation

```python
import aio_pika
import json
from typing import Dict, Callable
from .messaging_service import MessagingService, MessageEvent

class RabbitMQMessagingService(MessagingService):
    
    def __init__(self, connection_url: str = "amqp://guest:guest@localhost:5672/"):
        self.connection_url = connection_url
        self.connection = None
        self.channel = None
        self.subscribers: Dict[str, Callable] = {}
    
    async def start(self):
        self.connection = await aio_pika.connect_robust(self.connection_url)
        self.channel = await self.connection.channel()
        await self.channel.set_qos(prefetch_count=10)
    
    async def publish_event(self, topic: str, event: MessageEvent) -> bool:
        try:
            exchange = await self.channel.declare_exchange(
                topic, aio_pika.ExchangeType.TOPIC, durable=True
            )
            
            message = aio_pika.Message(
                json.dumps(event.__dict__).encode(),
                content_type="application/json",
                correlation_id=event.correlation_id
            )
            
            await exchange.publish(message, routing_key=event.event_type)
            return True
        except Exception as e:
            # Log error and handle dead-letter queue
            await self._handle_publish_error(topic, event, e)
            return False
    
    async def subscribe(self, topic: str, handler: Callable[[MessageEvent], None]):
        exchange = await self.channel.declare_exchange(
            topic, aio_pika.ExchangeType.TOPIC, durable=True
        )
        
        queue = await self.channel.declare_queue(
            f"{topic}.consumer", durable=True
        )
        await queue.bind(exchange, "#")
        
        async def message_handler(message: aio_pika.IncomingMessage):
            try:
                async with message.process():
                    event_data = json.loads(message.body.decode())
                    event = MessageEvent(**event_data)
                    await handler(event)
            except Exception as e:
                # Handle processing error and dead-letter queue
                await self._handle_processing_error(message, e)
        
        await queue.consume(message_handler)
```

#### Azure Service Bus Implementation

```python
from azure.servicebus.aio import ServiceBusClient, ServiceBusMessage
from .messaging_service import MessagingService, MessageEvent

class AzureServiceBusMessagingService(MessagingService):
    
    def __init__(self, connection_string: str):
        self.connection_string = connection_string
        self.client = None
    
    async def start(self):
        self.client = ServiceBusClient.from_connection_string(
            self.connection_string
        )
    
    async def publish_event(self, topic: str, event: MessageEvent) -> bool:
        try:
            async with self.client.get_topic_sender(topic) as sender:
                message = ServiceBusMessage(
                    json.dumps(event.__dict__),
                    content_type="application/json",
                    correlation_id=event.correlation_id,
                    subject=event.event_type
                )
                await sender.send_messages(message)
                return True
        except Exception as e:
            # Log error and handle dead-letter queue
            await self._handle_publish_error(topic, event, e)
            return False
    
    async def subscribe(self, topic: str, handler: Callable[[MessageEvent], None]):
        subscription_name = f"{topic}-subscription"
        
        async with self.client.get_subscription_receiver(
            topic, subscription_name
        ) as receiver:
            async for message in receiver:
                try:
                    event_data = json.loads(str(message))
                    event = MessageEvent(**event_data)
                    await handler(event)
                    await receiver.complete_message(message)
                except Exception as e:
                    # Handle processing error and dead-letter queue
                    await self._handle_processing_error(message, e)
                    await receiver.dead_letter_message(message)
```

#### Factory Pattern for Environment Selection

```python
import os
from .rabbitmq_messaging_service import RabbitMQMessagingService
from .azure_servicebus_messaging_service import AzureServiceBusMessagingService

class MessagingServiceFactory:
    
    @staticmethod
    def create_messaging_service() -> MessagingService:
        environment = os.getenv("MESSAGING_ENVIRONMENT", "local")
        
        if environment == "local":
            connection_url = os.getenv(
                "RABBITMQ_URL", 
                "amqp://guest:guest@localhost:5672/"
            )
            return RabbitMQMessagingService(connection_url)
        
        elif environment in ["production", "staging"]:
            connection_string = os.getenv("AZURE_SERVICEBUS_CONNECTION_STRING")
            if not connection_string:
                raise ValueError("Azure Service Bus connection string not provided")
            return AzureServiceBusMessagingService(connection_string)
        
        else:
            raise ValueError(f"Unknown messaging environment: {environment}")
```

## Event Schema Definitions

### Common Event Types

```typescript
// Shared event schemas across all services
interface BaseEvent {
  eventId: string;
  eventType: string;
  timestamp: string;
  correlationId?: string;
  userId: string;
}

interface TransactionReceivedEvent extends BaseEvent {
  eventType: "TransactionReceived";
  transactionId: string;
  accountId: string;
  amount: number;
  description: string;
  source: string;
}

interface TransactionCategorizedEvent extends BaseEvent {
  eventType: "TransactionCategorized";
  transactionId: string;
  categoryId: string;
  confidence: number;
  explanation: string;
}

interface InvoiceSubmissionCompletedEvent extends BaseEvent {
  eventType: "InvoiceSubmissionCompleted";
  invoiceId: string;
  zraInvoiceId: string;
  status: "SUBMITTED" | "FAILED";
}
```

## Error Handling and Dead Letter Queues

### RabbitMQ Dead Letter Exchange

```yaml
# RabbitMQ Configuration
rabbitmq:
  exchanges:
    - name: "transaction.events"
      type: "topic"
      durable: true
      arguments:
        x-dead-letter-exchange: "transaction.events.dlx"
    - name: "transaction.events.dlx"
      type: "topic"
      durable: true
  queues:
    - name: "transaction.events.dlq"
      durable: true
      exchange: "transaction.events.dlx"
      routing_key: "#"
```

### Azure Service Bus Dead Letter Queue

Azure Service Bus automatically provides dead-letter queue functionality. Failed messages are automatically moved to the dead-letter queue after max delivery attempts.

## Configuration Management

### Environment Variables

```bash
# Local Development
MESSAGING_ENVIRONMENT=local
RABBITMQ_URL=amqp://guest:guest@localhost:5672/

# Production/Staging
MESSAGING_ENVIRONMENT=production
AZURE_SERVICEBUS_CONNECTION_STRING=Endpoint=sb://...
```

### Spring Profiles

```yaml
# application.yml
spring:
  profiles:
    active: ${SPRING_PROFILES_ACTIVE:local}

---
spring:
  config:
    activate:
      on-profile: local
  cloud:
    stream:
      default-binder: rabbit

---
spring:
  config:
    activate:
      on-profile: production
  cloud:
    stream:
      default-binder: servicebus
```

## Testing Strategy

### Local Development Testing

1. **Unit Tests**: Mock the messaging abstraction layer
2. **Integration Tests**: Use embedded RabbitMQ (rabbitmq-test)
3. **Contract Tests**: Verify event schema compatibility

### Production Parity Testing

1. **Docker Compose**: Full stack with RabbitMQ for CI environments
2. **Azure Service Bus Emulator**: When available for testing
3. **Staging Environment**: Full Azure Service Bus testing

## Monitoring and Observability

### Metrics Collection

Both implementations should expose consistent metrics:
- Message publish success/failure rates
- Message processing latency
- Dead-letter queue message counts
- Connection health status

### Logging Standards

```json
{
  "timestamp": "2024-07-27T10:30:00Z",
  "level": "INFO",
  "service": "transaction-service",
  "event_type": "message_published",
  "topic": "transaction.events",
  "message_id": "msg-123",
  "correlation_id": "corr-456",
  "broker": "rabbitmq|servicebus"
}
```

## Migration and Deployment

### Zero-Downtime Deployment

1. **Blue-Green Deployment**: Switch messaging configuration during deployment
2. **Message Replay**: Capability to replay messages from dead-letter queues
3. **Graceful Shutdown**: Ensure all in-flight messages are processed

### Rollback Strategy

1. **Configuration Rollback**: Revert to previous messaging configuration
2. **Message Recovery**: Recover messages from dead-letter queues
3. **State Consistency**: Ensure data consistency across rollback

This messaging abstraction layer ensures that IntelliFin can develop cost-effectively locally while maintaining production-grade reliability and scalability in cloud environments.
