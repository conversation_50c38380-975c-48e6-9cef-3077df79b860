name: IntelliFin Deployment Pipeline

on:
  push:
    branches: [ master, main ]
    tags: [ 'v*' ]
  workflow_dispatch:
    inputs:
      environment:
        description: 'Environment to deploy to'
        required: true
        default: 'staging'
        type: choice
        options:
          - staging
          - production

env:
  REGISTRY: ghcr.io
  IMAGE_NAME: ${{ github.repository }}

jobs:
  # Deploy to Staging
  deploy-staging:
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main' || (github.event_name == 'workflow_dispatch' && github.event.inputs.environment == 'staging')
    environment: staging
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
      
      - name: Setup Google Cloud CLI
        uses: google-github-actions/setup-gcloud@v1
        with:
          service_account_key: ${{ secrets.GCP_SA_KEY_STAGING }}
          project_id: ${{ secrets.GCP_PROJECT_ID_STAGING }}
      
      - name: Configure Docker for GCR
        run: gcloud auth configure-docker
      
      - name: Get GKE credentials
        run: |
          gcloud container clusters get-credentials intellifin-staging-cluster \
            --zone ${{ secrets.GCP_ZONE }} \
            --project ${{ secrets.GCP_PROJECT_ID_STAGING }}
      
      - name: Deploy to staging
        run: |
          # Update image tags in Kubernetes manifests
          sed -i "s|IMAGE_TAG|${{ github.sha }}|g" k8s/staging/*.yaml
          
          # Apply Kubernetes manifests
          kubectl apply -f k8s/staging/
          
          # Wait for rollout to complete
          kubectl rollout status deployment/intellifin-backend-core -n staging
          kubectl rollout status deployment/intellifin-ai-service -n staging
          kubectl rollout status deployment/intellifin-zra-service -n staging
          kubectl rollout status deployment/intellifin-frontend -n staging
      
      - name: Run smoke tests
        run: |
          # Wait for services to be ready
          kubectl wait --for=condition=ready pod -l app=intellifin-backend-core -n staging --timeout=300s
          
          # Get service URL
          BACKEND_URL=$(kubectl get service intellifin-backend-core-service -n staging -o jsonpath='{.status.loadBalancer.ingress[0].ip}')
          
          # Run basic health checks
          curl -f http://$BACKEND_URL/actuator/health || exit 1
          
          echo "Staging deployment successful!"

  # Deploy to Production
  deploy-production:
    runs-on: ubuntu-latest
    if: startsWith(github.ref, 'refs/tags/v') || (github.event_name == 'workflow_dispatch' && github.event.inputs.environment == 'production')
    environment: production
    needs: [deploy-staging]
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
      
      - name: Setup Google Cloud CLI
        uses: google-github-actions/setup-gcloud@v1
        with:
          service_account_key: ${{ secrets.GCP_SA_KEY_PRODUCTION }}
          project_id: ${{ secrets.GCP_PROJECT_ID_PRODUCTION }}
      
      - name: Configure Docker for GCR
        run: gcloud auth configure-docker
      
      - name: Get GKE credentials
        run: |
          gcloud container clusters get-credentials intellifin-production-cluster \
            --zone ${{ secrets.GCP_ZONE }} \
            --project ${{ secrets.GCP_PROJECT_ID_PRODUCTION }}
      
      - name: Create backup
        run: |
          # Create database backup before deployment
          gcloud sql export sql intellifin-prod-db gs://intellifin-backups/pre-deploy-$(date +%Y%m%d-%H%M%S).sql \
            --database=intellifin_prod \
            --project=${{ secrets.GCP_PROJECT_ID_PRODUCTION }}
      
      - name: Deploy to production
        run: |
          # Update image tags in Kubernetes manifests
          sed -i "s|IMAGE_TAG|${{ github.sha }}|g" k8s/production/*.yaml
          
          # Apply Kubernetes manifests with rolling update
          kubectl apply -f k8s/production/
          
          # Wait for rollout to complete
          kubectl rollout status deployment/intellifin-backend-core -n production --timeout=600s
          kubectl rollout status deployment/intellifin-ai-service -n production --timeout=600s
          kubectl rollout status deployment/intellifin-zra-service -n production --timeout=600s
          kubectl rollout status deployment/intellifin-frontend -n production --timeout=600s
      
      - name: Run production health checks
        run: |
          # Wait for services to be ready
          kubectl wait --for=condition=ready pod -l app=intellifin-backend-core -n production --timeout=300s
          
          # Get service URL
          BACKEND_URL=$(kubectl get service intellifin-backend-core-service -n production -o jsonpath='{.status.loadBalancer.ingress[0].ip}')
          
          # Run comprehensive health checks
          curl -f https://api.intellifin.com/actuator/health || exit 1
          curl -f https://api.intellifin.com/actuator/info || exit 1
          
          echo "Production deployment successful!"
      
      - name: Notify deployment success
        uses: 8398a7/action-slack@v3
        with:
          status: success
          text: "🚀 IntelliFin ${{ github.ref_name }} deployed to production successfully!"
        env:
          SLACK_WEBHOOK_URL: ${{ secrets.SLACK_WEBHOOK_URL }}
        if: success()
      
      - name: Notify deployment failure
        uses: 8398a7/action-slack@v3
        with:
          status: failure
          text: "❌ IntelliFin ${{ github.ref_name }} deployment to production failed!"
        env:
          SLACK_WEBHOOK_URL: ${{ secrets.SLACK_WEBHOOK_URL }}
        if: failure()

  # Rollback on failure
  rollback-production:
    runs-on: ubuntu-latest
    if: failure() && (startsWith(github.ref, 'refs/tags/v') || (github.event_name == 'workflow_dispatch' && github.event.inputs.environment == 'production'))
    needs: [deploy-production]
    environment: production
    
    steps:
      - name: Setup Google Cloud CLI
        uses: google-github-actions/setup-gcloud@v1
        with:
          service_account_key: ${{ secrets.GCP_SA_KEY_PRODUCTION }}
          project_id: ${{ secrets.GCP_PROJECT_ID_PRODUCTION }}
      
      - name: Get GKE credentials
        run: |
          gcloud container clusters get-credentials intellifin-production-cluster \
            --zone ${{ secrets.GCP_ZONE }} \
            --project ${{ secrets.GCP_PROJECT_ID_PRODUCTION }}
      
      - name: Rollback deployments
        run: |
          kubectl rollout undo deployment/intellifin-backend-core -n production
          kubectl rollout undo deployment/intellifin-ai-service -n production
          kubectl rollout undo deployment/intellifin-zra-service -n production
          kubectl rollout undo deployment/intellifin-frontend -n production
          
          # Wait for rollback to complete
          kubectl rollout status deployment/intellifin-backend-core -n production
          kubectl rollout status deployment/intellifin-ai-service -n production
          kubectl rollout status deployment/intellifin-zra-service -n production
          kubectl rollout status deployment/intellifin-frontend -n production
      
      - name: Notify rollback
        uses: 8398a7/action-slack@v3
        with:
          status: warning
          text: "⚠️ IntelliFin production deployment rolled back due to failure!"
        env:
          SLACK_WEBHOOK_URL: ${{ secrets.SLACK_WEBHOOK_URL }}
