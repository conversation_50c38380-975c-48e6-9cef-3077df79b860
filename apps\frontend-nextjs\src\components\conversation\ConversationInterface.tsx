"use client"

import React, { useState, useRef, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  Send, 
  Mic, 
  MessageSquare, 
  Loader2, 
  AlertCircle, 
  Wifi, 
  WifiOff,
  <PERSON><PERSON><PERSON>,
  User
} from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { useAuthStore, useConversationStore } from '@/stores';
import { formatRelativeTime } from '@/utils';

export function ConversationInterface() {
  const { user } = useAuthStore();
  const { 
    messages,
    isConnected,
    isConnecting,
    connectionError,
    isTyping,
    awaitingResponse,
    addUserMessage,
    getCommandSuggestions,
    currentSessionId
  } = useConversationStore();
  
  const [input, setInput] = useState('');
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLInputElement>(null);

  // Auto-scroll to bottom when new messages arrive
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [messages]);

  // Focus input on mount
  useEffect(() => {
    inputRef.current?.focus();
  }, []);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!input.trim() || awaitingResponse || !isConnected) {
      return;
    }

    // Add user message to conversation
    addUserMessage(input.trim());
    
    // TODO: Send message via WebSocket
    // This will be implemented when we integrate the WebSocket service
    console.log('Sending message:', input.trim());
    
    setInput('');
  };

  const handleSuggestionClick = (suggestion: string) => {
    if (!awaitingResponse && isConnected) {
      addUserMessage(suggestion);
      // TODO: Send via WebSocket
      console.log('Sending suggestion:', suggestion);
    }
  };

  const suggestions = getCommandSuggestions();
  const firstName = user?.firstName || 'there';

  return (
    <Card className="h-[600px] flex flex-col">
      {/* Header */}
      <CardHeader className="pb-3">
        <CardTitle className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <div className="w-10 h-10 bg-gradient-primary rounded-full flex items-center justify-center">
              <Sparkles className="w-5 h-5 text-white" />
            </div>
            <div>
              <h3 className="font-semibold text-gray-900">AI Assistant</h3>
              <div className="flex items-center space-x-2">
                {isConnected ? (
                  <>
                    <Wifi className="w-3 h-3 text-green-500" />
                    <span className="text-xs text-green-600">Online</span>
                  </>
                ) : isConnecting ? (
                  <>
                    <Loader2 className="w-3 h-3 text-yellow-500 animate-spin" />
                    <span className="text-xs text-yellow-600">Connecting...</span>
                  </>
                ) : (
                  <>
                    <WifiOff className="w-3 h-3 text-red-500" />
                    <span className="text-xs text-red-600">Offline</span>
                  </>
                )}
              </div>
            </div>
          </div>
          <div className="text-xs text-gray-500">
            Session: {currentSessionId?.slice(-8)}
          </div>
        </CardTitle>
      </CardHeader>

      {/* Messages Area */}
      <CardContent className="flex-1 overflow-y-auto space-y-4 pb-4">
        {connectionError && (
          <motion.div
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            className="bg-red-50 border border-red-200 rounded-lg p-3"
          >
            <div className="flex items-center space-x-2">
              <AlertCircle className="w-4 h-4 text-red-500" />
              <span className="text-sm text-red-700">Connection Error: {connectionError}</span>
            </div>
          </motion.div>
        )}

        {messages.length === 0 && !connectionError && (
          <motion.div
            initial={{ opacity: 0, scale: 0.95 }}
            animate={{ opacity: 1, scale: 1 }}
            className="text-center py-8"
          >
            <div className="w-16 h-16 bg-gradient-primary/10 rounded-full flex items-center justify-center mx-auto mb-4">
              <MessageSquare className="w-8 h-8 text-primary" />
            </div>
            <h4 className="text-lg font-semibold text-gray-900 mb-2">
              Hi {firstName}! 👋
            </h4>
            <p className="text-gray-600 mb-6">
              I'm your AI accounting assistant. Ask me anything about your business finances!
            </p>
            
            {/* Command Suggestions */}
            <div className="grid grid-cols-1 gap-2 max-w-md mx-auto">
              {suggestions.slice(0, 4).map((suggestion, index) => (
                <motion.button
                  key={suggestion.text}
                  initial={{ opacity: 0, y: 10 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: index * 0.1 }}
                  onClick={() => handleSuggestionClick(suggestion.text)}
                  disabled={!isConnected || awaitingResponse}
                  className="text-left p-3 bg-gray-50 hover:bg-gray-100 rounded-lg border border-gray-200 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  <div className="flex items-center space-x-2">
                    <span className="text-lg">{suggestion.icon}</span>
                    <div>
                      <p className="text-sm font-medium text-gray-900">{suggestion.text}</p>
                      <p className="text-xs text-gray-500">{suggestion.description}</p>
                    </div>
                  </div>
                </motion.button>
              ))}
            </div>
          </motion.div>
        )}

        <AnimatePresence>
          {messages.map((message, index) => (
            <motion.div
              key={message.id}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
              transition={{ duration: 0.3 }}
              className={`flex items-start space-x-3 ${
                message.role === 'USER' ? 'flex-row-reverse space-x-reverse' : ''
              }`}
            >
              <div className={`w-8 h-8 rounded-full flex items-center justify-center flex-shrink-0 ${
                message.role === 'USER' 
                  ? 'bg-primary text-white' 
                  : 'bg-gradient-primary text-white'
              }`}>
                {message.role === 'USER' ? (
                  <User className="w-4 w-4" />
                ) : (
                  <Sparkles className="w-4 h-4" />
                )}
              </div>
              
              <div className={`max-w-md rounded-lg px-4 py-3 ${
                message.role === 'USER'
                  ? 'bg-primary text-white'
                  : 'bg-gray-100 text-gray-900'
              }`}>
                <div className="text-sm whitespace-pre-wrap">
                  {message.content}
                </div>
                
                {message.intentRecognized && (
                  <div className={`text-xs mt-2 opacity-75 ${
                    message.role === 'USER' ? 'text-blue-100' : 'text-gray-500'
                  }`}>
                    Intent: {message.intentRecognized}
                    {message.confidenceScore && ` (${Math.round(message.confidenceScore * 100)}%)`}
                  </div>
                )}
                
                <div className={`text-xs mt-1 opacity-75 ${
                  message.role === 'USER' ? 'text-blue-100' : 'text-gray-500'
                }`}>
                  {formatRelativeTime(message.timestamp)}
                </div>
              </div>
            </motion.div>
          ))}
        </AnimatePresence>

        {(isTyping || awaitingResponse) && (
          <motion.div
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            className="flex items-center space-x-3"
          >
            <div className="w-8 h-8 bg-gradient-primary rounded-full flex items-center justify-center">
              <Sparkles className="w-4 h-4 text-white" />
            </div>
            <div className="bg-gray-100 rounded-lg px-4 py-3">
              <div className="flex items-center space-x-2">
                <div className="flex space-x-1">
                  <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" />
                  <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0.1s' }} />
                  <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0.2s' }} />
                </div>
                <span className="text-sm text-gray-600">AI is thinking...</span>
              </div>
            </div>
          </motion.div>
        )}

        <div ref={messagesEndRef} />
      </CardContent>

      {/* Input Area */}
      <div className="p-4 border-t border-gray-200">
        <form onSubmit={handleSubmit} className="flex items-center space-x-3">
          <div className="flex-1 relative">
            <Input
              ref={inputRef}
              type="text"
              value={input}
              onChange={(e) => setInput(e.target.value)}
              placeholder={isConnected ? "Ask me anything about your business..." : "Connecting..."}
              disabled={!isConnected || awaitingResponse}
              className="pr-12"
            />
            <Button
              type="button"
              variant="ghost"
              size="sm"
              className="absolute right-2 top-1/2 transform -translate-y-1/2 h-8 w-8 p-0"
              disabled={!isConnected}
            >
              <Mic className="h-4 w-4" />
            </Button>
          </div>
          <Button
            type="submit"
            disabled={!input.trim() || !isConnected || awaitingResponse}
            className="bg-gradient-primary hover:opacity-90"
          >
            {awaitingResponse ? (
              <Loader2 className="h-4 w-4 animate-spin" />
            ) : (
              <Send className="h-4 w-4" />
            )}
          </Button>
        </form>
        
        {!isConnected && (
          <div className="text-center mt-2">
            <span className="text-sm text-red-600">
              {isConnecting ? 'Connecting to AI assistant...' : 'Connection lost. Attempting to reconnect...'}
            </span>
          </div>
        )}
      </div>
    </Card>
  );
}
