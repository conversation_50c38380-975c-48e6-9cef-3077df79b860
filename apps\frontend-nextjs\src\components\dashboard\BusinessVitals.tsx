"use client"

import React from 'react';
import { motion } from 'framer-motion';
import { 
  TrendingUp, 
  TrendingDown, 
  Wallet, 
  AlertCircle, 
  CheckCircle,
  Clock,
  DollarSign
} from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { DashboardData } from '@/types/financial';
import { formatCurrency, formatPercentage } from '@/utils';

interface BusinessVitalsProps {
  data: DashboardData | null;
  isLoading: boolean;
}

interface VitalCard {
  title: string;
  value: string;
  change?: {
    value: number;
    isPositive: boolean;
    period: string;
  };
  icon: React.ComponentType<{ className?: string }>;
  color: 'green' | 'red' | 'blue' | 'yellow' | 'gray';
}

export function BusinessVitals({ data, isLoading }: BusinessVitalsProps) {
  if (isLoading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">Business Vitals</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {[1, 2, 3, 4].map((i) => (
              <div key={i} className="animate-pulse">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    <div className="w-8 h-8 bg-gray-200 rounded-full" />
                    <div className="w-24 h-4 bg-gray-200 rounded" />
                  </div>
                  <div className="w-16 h-4 bg-gray-200 rounded" />
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    );
  }

  if (!data) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">Business Vitals</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center py-8">
            <AlertCircle className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <p className="text-gray-500">No data available</p>
            <p className="text-sm text-gray-400 mt-1">
              Connect your accounts to see business vitals
            </p>
          </div>
        </CardContent>
      </Card>
    );
  }

  // Calculate vitals from dashboard data
  const totalBalance = data.accountBalances.reduce((sum, account) => sum + account.balance, 0);
  const totalIncome = data.summary.totalIncome;
  const totalExpenses = data.summary.totalExpenses;
  const netProfit = totalIncome - totalExpenses;
  const pendingInvoicesValue = data.pendingInvoices.reduce((sum, invoice) => sum + invoice.totalAmount, 0);

  // Mock previous period data for change calculations (in real app, this would come from API)
  const previousIncome = totalIncome * 0.85; // Simulate 15% growth
  const previousExpenses = totalExpenses * 1.1; // Simulate 10% increase in expenses
  const previousBalance = totalBalance * 0.95; // Simulate 5% growth

  const vitals: VitalCard[] = [
    {
      title: 'Total Balance',
      value: formatCurrency(totalBalance),
      change: {
        value: ((totalBalance - previousBalance) / previousBalance) * 100,
        isPositive: totalBalance > previousBalance,
        period: 'vs last month',
      },
      icon: Wallet,
      color: 'blue',
    },
    {
      title: 'Monthly Income',
      value: formatCurrency(totalIncome),
      change: {
        value: ((totalIncome - previousIncome) / previousIncome) * 100,
        isPositive: totalIncome > previousIncome,
        period: 'vs last month',
      },
      icon: TrendingUp,
      color: 'green',
    },
    {
      title: 'Monthly Expenses',
      value: formatCurrency(totalExpenses),
      change: {
        value: ((totalExpenses - previousExpenses) / previousExpenses) * 100,
        isPositive: totalExpenses < previousExpenses, // Lower expenses are positive
        period: 'vs last month',
      },
      icon: DollarSign,
      color: totalExpenses > previousExpenses ? 'red' : 'green',
    },
    {
      title: 'Net Profit',
      value: formatCurrency(netProfit),
      change: {
        value: netProfit > 0 ? 15.2 : -8.5, // Mock data
        isPositive: netProfit > 0,
        period: 'vs last month',
      },
      icon: netProfit > 0 ? TrendingUp : TrendingDown,
      color: netProfit > 0 ? 'green' : 'red',
    },
  ];

  const getIconColor = (color: string) => {
    const colors = {
      green: 'text-green-500 bg-green-100',
      red: 'text-red-500 bg-red-100',
      blue: 'text-blue-500 bg-blue-100',
      yellow: 'text-yellow-500 bg-yellow-100',
      gray: 'text-gray-500 bg-gray-100',
    };
    return colors[color as keyof typeof colors] || colors.gray;
  };

  const getChangeColor = (isPositive: boolean) => {
    return isPositive ? 'text-green-600' : 'text-red-600';
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="text-lg flex items-center justify-between">
          Business Vitals
          <div className="flex items-center space-x-1">
            <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse" />
            <span className="text-xs text-gray-500">Live</span>
          </div>
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {vitals.map((vital, index) => (
            <motion.div
              key={vital.title}
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.3, delay: index * 0.1 }}
              className="flex items-center justify-between p-3 rounded-lg hover:bg-gray-50 transition-colors"
            >
              <div className="flex items-center space-x-3">
                <div className={`w-10 h-10 rounded-full flex items-center justify-center ${getIconColor(vital.color)}`}>
                  <vital.icon className="h-5 w-5" />
                </div>
                <div>
                  <p className="text-sm font-medium text-gray-900">{vital.title}</p>
                  <p className="text-lg font-bold text-gray-900">{vital.value}</p>
                  {vital.change && (
                    <div className="flex items-center space-x-1">
                      {vital.change.isPositive ? (
                        <TrendingUp className="h-3 w-3 text-green-500" />
                      ) : (
                        <TrendingDown className="h-3 w-3 text-red-500" />
                      )}
                      <span className={`text-xs font-medium ${getChangeColor(vital.change.isPositive)}`}>
                        {vital.change.isPositive ? '+' : ''}{formatPercentage(vital.change.value)}
                      </span>
                      <span className="text-xs text-gray-500">{vital.change.period}</span>
                    </div>
                  )}
                </div>
              </div>
            </motion.div>
          ))}
        </div>

        {/* Additional Info */}
        {data.pendingInvoices.length > 0 && (
          <div className="mt-6 pt-4 border-t border-gray-200">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-2">
                <Clock className="h-4 w-4 text-yellow-500" />
                <span className="text-sm text-gray-600">Pending Invoices</span>
              </div>
              <div className="text-right">
                <p className="text-sm font-semibold text-gray-900">
                  {formatCurrency(pendingInvoicesValue)}
                </p>
                <p className="text-xs text-gray-500">
                  {data.pendingInvoices.length} invoice{data.pendingInvoices.length !== 1 ? 's' : ''}
                </p>
              </div>
            </div>
          </div>
        )}

        {/* Alerts Summary */}
        {data.alerts.length > 0 && (
          <div className="mt-4 pt-4 border-t border-gray-200">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-2">
                <AlertCircle className="h-4 w-4 text-red-500" />
                <span className="text-sm text-gray-600">Active Alerts</span>
              </div>
              <div className="text-right">
                <p className="text-sm font-semibold text-red-600">
                  {data.alerts.filter(alert => 
                    alert.severity === 'HIGH' || alert.severity === 'CRITICAL'
                  ).length}
                </p>
                <p className="text-xs text-gray-500">
                  {data.alerts.length} total
                </p>
              </div>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
