package com.intellifin.controller;

import com.intellifin.dto.conversation.ConversationCommandRequest;
import com.intellifin.dto.conversation.ConversationResponse;
import com.intellifin.model.Conversation;
import com.intellifin.model.ConversationMessage;
import com.intellifin.security.UserPrincipal;
import com.intellifin.service.ConversationService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.UUID;

@RestController
@RequestMapping("/api/v1/conversations")
@RequiredArgsConstructor
@Slf4j
@Tag(name = "Conversations", description = "Conversational interface and chat management")
public class ConversationController {

    private final ConversationService conversationService;

    @PostMapping("/command")
    @Operation(summary = "Process conversational command", description = "Process a natural language command via REST API")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Command processed successfully"),
        @ApiResponse(responseCode = "400", description = "Invalid command format"),
        @ApiResponse(responseCode = "401", description = "Authentication required")
    })
    public ResponseEntity<ConversationResponse> processCommand(
            @Valid @RequestBody ConversationCommandRequest request,
            @AuthenticationPrincipal UserPrincipal userPrincipal) {
        
        log.info("REST API command from user: {}, command: {}", 
                userPrincipal.getId(), request.getCommand());
        
        ConversationResponse response = conversationService.processCommand(request, userPrincipal.getId());
        
        return ResponseEntity.ok(response);
    }

    @GetMapping
    @Operation(summary = "Get user conversations", description = "Retrieve all conversations for the authenticated user")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Conversations retrieved successfully"),
        @ApiResponse(responseCode = "401", description = "Authentication required")
    })
    public ResponseEntity<List<ConversationDto>> getUserConversations(
            @AuthenticationPrincipal UserPrincipal userPrincipal) {
        
        List<Conversation> conversations = conversationService.getUserConversations(userPrincipal.getId());
        
        List<ConversationDto> conversationDtos = conversations.stream()
                .map(this::convertToDto)
                .toList();
        
        return ResponseEntity.ok(conversationDtos);
    }

    @GetMapping("/{conversationId}")
    @Operation(summary = "Get conversation details", description = "Retrieve a specific conversation with its messages")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Conversation retrieved successfully"),
        @ApiResponse(responseCode = "404", description = "Conversation not found"),
        @ApiResponse(responseCode = "401", description = "Authentication required")
    })
    public ResponseEntity<ConversationDetailDto> getConversation(
            @PathVariable UUID conversationId,
            @AuthenticationPrincipal UserPrincipal userPrincipal) {
        
        Optional<Conversation> conversation = conversationService.getConversation(conversationId, userPrincipal.getId());
        
        if (conversation.isEmpty()) {
            return ResponseEntity.notFound().build();
        }
        
        List<ConversationMessage> messages = conversationService.getConversationMessages(conversationId, userPrincipal.getId());
        
        ConversationDetailDto detailDto = convertToDetailDto(conversation.get(), messages);
        
        return ResponseEntity.ok(detailDto);
    }

    @PutMapping("/{conversationId}/archive")
    @Operation(summary = "Archive conversation", description = "Archive a conversation to hide it from active list")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Conversation archived successfully"),
        @ApiResponse(responseCode = "404", description = "Conversation not found"),
        @ApiResponse(responseCode = "401", description = "Authentication required")
    })
    public ResponseEntity<Map<String, String>> archiveConversation(
            @PathVariable UUID conversationId,
            @AuthenticationPrincipal UserPrincipal userPrincipal) {
        
        conversationService.archiveConversation(conversationId, userPrincipal.getId());
        
        return ResponseEntity.ok(Map.of(
                "message", "Conversation archived successfully",
                "conversationId", conversationId.toString()
        ));
    }

    @DeleteMapping("/{conversationId}")
    @Operation(summary = "Delete conversation", description = "Delete a conversation and all its messages")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Conversation deleted successfully"),
        @ApiResponse(responseCode = "404", description = "Conversation not found"),
        @ApiResponse(responseCode = "401", description = "Authentication required")
    })
    public ResponseEntity<Map<String, String>> deleteConversation(
            @PathVariable UUID conversationId,
            @AuthenticationPrincipal UserPrincipal userPrincipal) {
        
        conversationService.deleteConversation(conversationId, userPrincipal.getId());
        
        return ResponseEntity.ok(Map.of(
                "message", "Conversation deleted successfully",
                "conversationId", conversationId.toString()
        ));
    }

    @GetMapping("/health")
    @Operation(summary = "Conversation service health", description = "Check if conversation service is operational")
    public ResponseEntity<Map<String, Object>> healthCheck() {
        return ResponseEntity.ok(Map.of(
                "status", "healthy",
                "service", "conversation-service",
                "timestamp", System.currentTimeMillis(),
                "features", List.of("websocket", "rest-api", "ai-processing")
        ));
    }

    // Helper methods and DTOs
    private ConversationDto convertToDto(Conversation conversation) {
        return ConversationDto.builder()
                .id(conversation.getId())
                .title(conversation.getTitle())
                .sessionId(conversation.getSessionId())
                .status(conversation.getStatus().toString())
                .messageCount(conversation.getMessageCount())
                .lastActivityAt(conversation.getLastActivityAt())
                .createdAt(conversation.getCreatedAt())
                .build();
    }

    private ConversationDetailDto convertToDetailDto(Conversation conversation, List<ConversationMessage> messages) {
        List<MessageDto> messageDtos = messages.stream()
                .map(this::convertMessageToDto)
                .toList();

        return ConversationDetailDto.builder()
                .id(conversation.getId())
                .title(conversation.getTitle())
                .sessionId(conversation.getSessionId())
                .status(conversation.getStatus().toString())
                .lastActivityAt(conversation.getLastActivityAt())
                .createdAt(conversation.getCreatedAt())
                .messages(messageDtos)
                .build();
    }

    private MessageDto convertMessageToDto(ConversationMessage message) {
        return MessageDto.builder()
                .id(message.getId())
                .type(message.getType().toString())
                .role(message.getRole().toString())
                .content(message.getContent())
                .intentRecognized(message.getIntentRecognized())
                .confidenceScore(message.getConfidenceScore())
                .status(message.getStatus().toString())
                .createdAt(message.getCreatedAt())
                .build();
    }

    // DTO classes
    @lombok.Data
    @lombok.Builder
    @lombok.NoArgsConstructor
    @lombok.AllArgsConstructor
    public static class ConversationDto {
        private UUID id;
        private String title;
        private String sessionId;
        private String status;
        private int messageCount;
        private java.time.LocalDateTime lastActivityAt;
        private java.time.LocalDateTime createdAt;
    }

    @lombok.Data
    @lombok.Builder
    @lombok.NoArgsConstructor
    @lombok.AllArgsConstructor
    public static class ConversationDetailDto {
        private UUID id;
        private String title;
        private String sessionId;
        private String status;
        private java.time.LocalDateTime lastActivityAt;
        private java.time.LocalDateTime createdAt;
        private List<MessageDto> messages;
    }

    @lombok.Data
    @lombok.Builder
    @lombok.NoArgsConstructor
    @lombok.AllArgsConstructor
    public static class MessageDto {
        private UUID id;
        private String type;
        private String role;
        private String content;
        private String intentRecognized;
        private Double confidenceScore;
        private String status;
        private java.time.LocalDateTime createdAt;
    }
}
