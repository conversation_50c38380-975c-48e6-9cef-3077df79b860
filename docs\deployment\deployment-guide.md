# IntelliFin Deployment Guide

## Overview

This guide provides comprehensive deployment instructions for the IntelliFin platform, covering local development, staging, and production environments.

## 1. Prerequisites

### 1.1 Required Software
- Docker 24.0+ and Docker Compose 2.0+
- Node.js 18+ (for frontend development)
- Java 17+ (for backend development)
- Python 3.10+ (for AI service development)
- Git 2.30+

### 1.2 Cloud Accounts
- Google Cloud Platform account (primary)
- Azure account (for AI Search - if using hybrid approach)
- Domain name and SSL certificates

## 2. Local Development Setup

### 2.1 Quick Start

```bash
# Clone the repository
git clone https://github.com/your-org/intellifin.git
cd intellifin

# Copy environment template
cp .env.example .env

# Edit environment variables
nano .env

# Start all services
docker-compose up -d

# Initialize database
docker-compose exec postgres psql -U intellifin_user -d intellifin_dev -f /docker-entrypoint-initdb.d/001_initial_schema.sql

# Pull and start Ollama model for AI development
docker-compose exec ollama ollama pull llama3.1:8b

# Verify all services are running
docker-compose ps
```

### 2.2 Environment Variables

```bash
# .env file for local development
# Database Configuration
DATABASE_URL=**********************************************
DATABASE_USERNAME=intellifin_user
DATABASE_PASSWORD=intellifin_dev_password

# Redis Configuration
REDIS_URL=redis://redis:6379

# JWT Configuration
JWT_SECRET=your-local-jwt-secret-key-min-256-bits
JWT_EXPIRATION=86400

# AI Service Configuration
OLLAMA_BASE_URL=http://ollama:11434
OPENAI_API_KEY=your-openai-api-key-for-embeddings
GOOGLE_API_KEY=your-google-api-key-for-gemini

# External API Configuration (Development)
MTN_API_BASE_URL=https://sandbox.mtn.com/api
MTN_API_KEY=sandbox-key
ZRA_API_BASE_URL=https://sandbox.zra.org.zm/api
ZRA_API_KEY=sandbox-key

# Frontend Configuration
NEXT_PUBLIC_API_BASE_URL=http://localhost:8080
NEXT_PUBLIC_WS_URL=ws://localhost:8080
```

### 2.3 Service Health Checks

```bash
# Check all services
./scripts/health-check.sh

# Individual service checks
curl http://localhost:8080/actuator/health  # Core Backend
curl http://localhost:8000/health           # AI Service
curl http://localhost:8081/actuator/health  # ZRA Service
curl http://localhost:3000/api/health       # Frontend
```

## 3. CI/CD Pipeline Configuration

### 3.1 GitHub Actions Workflow

```yaml
# .github/workflows/ci-cd.yml
name: IntelliFin CI/CD Pipeline

on:
  push:
    branches: [main, develop]
  pull_request:
    branches: [main]

env:
  REGISTRY: gcr.io
  PROJECT_ID: intellifin-prod

jobs:
  test:
    runs-on: ubuntu-latest
    services:
      postgres:
        image: postgres:15
        env:
          POSTGRES_PASSWORD: test_password
          POSTGRES_DB: intellifin_test
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
      redis:
        image: redis:7-alpine
        options: >-
          --health-cmd "redis-cli ping"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5

    steps:
      - uses: actions/checkout@v4
      
      - name: Set up JDK 17
        uses: actions/setup-java@v3
        with:
          java-version: '17'
          distribution: 'temurin'
      
      - name: Set up Node.js
        uses: actions/setup-node@v3
        with:
          node-version: '18'
          cache: 'npm'
          cache-dependency-path: apps/frontend-nextjs/package-lock.json
      
      - name: Set up Python
        uses: actions/setup-python@v4
        with:
          python-version: '3.10'
      
      - name: Cache Maven dependencies
        uses: actions/cache@v3
        with:
          path: ~/.m2
          key: ${{ runner.os }}-m2-${{ hashFiles('**/pom.xml') }}
      
      - name: Run Backend Tests
        run: |
          cd apps/backend-java-core
          ./mvnw test
      
      - name: Run AI Service Tests
        run: |
          cd apps/backend-python-ai
          pip install -r requirements.txt
          pytest
      
      - name: Run Frontend Tests
        run: |
          cd apps/frontend-nextjs
          npm ci
          npm run test
      
      - name: Run Integration Tests
        run: |
          docker-compose -f docker-compose.test.yml up -d
          ./scripts/run-integration-tests.sh
          docker-compose -f docker-compose.test.yml down

  build-and-deploy:
    needs: test
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main'
    
    steps:
      - uses: actions/checkout@v4
      
      - name: Authenticate to Google Cloud
        uses: google-github-actions/auth@v1
        with:
          credentials_json: ${{ secrets.GCP_SA_KEY }}
      
      - name: Configure Docker for GCR
        run: gcloud auth configure-docker
      
      - name: Build and Push Images
        run: |
          # Build and push backend core
          docker build -t $REGISTRY/$PROJECT_ID/intellifin-backend-core:$GITHUB_SHA apps/backend-java-core
          docker push $REGISTRY/$PROJECT_ID/intellifin-backend-core:$GITHUB_SHA
          
          # Build and push AI service
          docker build -t $REGISTRY/$PROJECT_ID/intellifin-ai-service:$GITHUB_SHA apps/backend-python-ai
          docker push $REGISTRY/$PROJECT_ID/intellifin-ai-service:$GITHUB_SHA
          
          # Build and push ZRA service
          docker build -t $REGISTRY/$PROJECT_ID/intellifin-zra-service:$GITHUB_SHA apps/backend-java-zra
          docker push $REGISTRY/$PROJECT_ID/intellifin-zra-service:$GITHUB_SHA
          
          # Build and push frontend
          docker build -t $REGISTRY/$PROJECT_ID/intellifin-frontend:$GITHUB_SHA apps/frontend-nextjs
          docker push $REGISTRY/$PROJECT_ID/intellifin-frontend:$GITHUB_SHA
      
      - name: Deploy to GKE
        run: |
          gcloud container clusters get-credentials intellifin-cluster --zone us-central1-a
          
          # Update deployment manifests with new image tags
          sed -i "s/IMAGE_TAG/$GITHUB_SHA/g" k8s/production/*.yaml
          
          # Apply Kubernetes manifests
          kubectl apply -f k8s/production/
          
          # Wait for rollout to complete
          kubectl rollout status deployment/intellifin-backend-core
          kubectl rollout status deployment/intellifin-ai-service
          kubectl rollout status deployment/intellifin-zra-service
          kubectl rollout status deployment/intellifin-frontend
```

## 4. Production Infrastructure

### 4.1 Google Cloud Platform Setup

```bash
# Infrastructure provisioning script
#!/bin/bash

# Set project and region
export PROJECT_ID=intellifin-prod
export REGION=us-central1
export ZONE=us-central1-a

# Enable required APIs
gcloud services enable container.googleapis.com
gcloud services enable sqladmin.googleapis.com
gcloud services enable redis.googleapis.com
gcloud services enable secretmanager.googleapis.com

# Create GKE cluster
gcloud container clusters create intellifin-cluster \
    --zone=$ZONE \
    --num-nodes=3 \
    --machine-type=e2-standard-4 \
    --enable-autoscaling \
    --min-nodes=2 \
    --max-nodes=10 \
    --enable-autorepair \
    --enable-autoupgrade

# Create Cloud SQL instance
gcloud sql instances create intellifin-db \
    --database-version=POSTGRES_15 \
    --tier=db-custom-2-4096 \
    --region=$REGION \
    --storage-type=SSD \
    --storage-size=100GB \
    --storage-auto-increase

# Create Redis instance
gcloud redis instances create intellifin-cache \
    --size=1 \
    --region=$REGION \
    --redis-version=redis_7_0

# Create secrets
echo -n "your-production-jwt-secret" | gcloud secrets create jwt-secret --data-file=-
echo -n "your-production-db-password" | gcloud secrets create db-password --data-file=-
```

### 4.2 Kubernetes Manifests

```yaml
# k8s/production/backend-core-deployment.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: intellifin-backend-core
  labels:
    app: intellifin-backend-core
spec:
  replicas: 3
  selector:
    matchLabels:
      app: intellifin-backend-core
  template:
    metadata:
      labels:
        app: intellifin-backend-core
    spec:
      containers:
      - name: backend-core
        image: gcr.io/intellifin-prod/intellifin-backend-core:IMAGE_TAG
        ports:
        - containerPort: 8080
        env:
        - name: SPRING_PROFILES_ACTIVE
          value: "production"
        - name: DATABASE_URL
          valueFrom:
            secretKeyRef:
              name: app-secrets
              key: database-url
        - name: JWT_SECRET
          valueFrom:
            secretKeyRef:
              name: app-secrets
              key: jwt-secret
        resources:
          requests:
            memory: "512Mi"
            cpu: "250m"
          limits:
            memory: "1Gi"
            cpu: "500m"
        livenessProbe:
          httpGet:
            path: /actuator/health
            port: 8080
          initialDelaySeconds: 60
          periodSeconds: 30
        readinessProbe:
          httpGet:
            path: /actuator/health
            port: 8080
          initialDelaySeconds: 30
          periodSeconds: 10

---
apiVersion: v1
kind: Service
metadata:
  name: intellifin-backend-core-service
spec:
  selector:
    app: intellifin-backend-core
  ports:
  - protocol: TCP
    port: 80
    targetPort: 8080
  type: ClusterIP
```

## 5. Monitoring and Logging

### 5.1 Application Monitoring

```yaml
# k8s/monitoring/prometheus-config.yaml
apiVersion: v1
kind: ConfigMap
metadata:
  name: prometheus-config
data:
  prometheus.yml: |
    global:
      scrape_interval: 15s
    scrape_configs:
    - job_name: 'intellifin-backend'
      kubernetes_sd_configs:
      - role: pod
      relabel_configs:
      - source_labels: [__meta_kubernetes_pod_label_app]
        action: keep
        regex: intellifin-.*
      - source_labels: [__meta_kubernetes_pod_annotation_prometheus_io_scrape]
        action: keep
        regex: true
      - source_labels: [__meta_kubernetes_pod_annotation_prometheus_io_path]
        action: replace
        target_label: __metrics_path__
        regex: (.+)
```

### 5.2 Log Aggregation

```yaml
# k8s/logging/fluentd-config.yaml
apiVersion: v1
kind: ConfigMap
metadata:
  name: fluentd-config
data:
  fluent.conf: |
    <source>
      @type tail
      path /var/log/containers/intellifin-*.log
      pos_file /var/log/fluentd-containers.log.pos
      tag kubernetes.*
      format json
      time_format %Y-%m-%dT%H:%M:%S.%NZ
    </source>
    
    <match kubernetes.**>
      @type google_cloud
      project_id intellifin-prod
      zone us-central1-a
      vm_id intellifin-cluster
    </match>
```

## 6. Backup and Disaster Recovery

### 6.1 Database Backup

```bash
# Automated backup script
#!/bin/bash

# Create database backup
gcloud sql export sql intellifin-db gs://intellifin-backups/db-backup-$(date +%Y%m%d-%H%M%S).sql \
    --database=intellifin_prod

# Cleanup old backups (keep last 30 days)
gsutil -m rm gs://intellifin-backups/db-backup-$(date -d '30 days ago' +%Y%m%d)*.sql
```

### 6.2 Disaster Recovery Plan

```markdown
## Disaster Recovery Procedures

### RTO (Recovery Time Objective): 4 hours
### RPO (Recovery Point Objective): 1 hour

### Recovery Steps:
1. Assess the scope of the outage
2. Activate backup infrastructure in secondary region
3. Restore database from latest backup
4. Update DNS to point to backup infrastructure
5. Verify all services are operational
6. Communicate status to stakeholders

### Backup Infrastructure:
- Secondary GKE cluster in different region
- Cross-region database replicas
- Automated failover procedures
```

This deployment guide provides a comprehensive foundation for deploying IntelliFin across different environments while maintaining security, scalability, and reliability.
