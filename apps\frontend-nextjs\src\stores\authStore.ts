import { create } from 'zustand';
import { devtools, persist } from 'zustand/middleware';
import { AuthService } from '@/services/api/auth';
import { SocialAuthService } from '@/services/api/social-auth';
import { apiClient, TokenManager } from '@/services/api/client';
import { UserProfile, LoginRequest, RegisterRequest } from '@/types/api';

interface AuthState {
  // State
  user: UserProfile | null;
  token: string | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  error: string | null;
  
  // Actions
  login: (credentials: LoginRequest) => Promise<void>;
  register: (userData: RegisterRequest) => Promise<void>;
  loginWithGoogle: () => Promise<void>;
  loginWithApple: () => Promise<void>;
  logout: () => Promise<void>;
  refreshToken: () => Promise<void>;
  validateToken: () => Promise<boolean>;
  forgotPassword: (email: string) => Promise<void>;
  resetPassword: (token: string, newPassword: string) => Promise<void>;
  updateProfile: (updates: Partial<UserProfile>) => Promise<void>;
  updateOnboardingStatus: (status: UserProfile['onboardingStatus']) => Promise<void>;
  clearError: () => void;
  setLoading: (loading: boolean) => void;
  
  // Initialization
  initialize: () => Promise<void>;
}

export const useAuthStore = create<AuthState>()(
  devtools(
    persist(
      (set, get) => ({
        // Initial state
        user: null,
        token: null,
        isAuthenticated: false,
        isLoading: false,
        error: null,

        // Actions
        login: async (credentials: LoginRequest) => {
          set({ isLoading: true, error: null });

          try {
            console.log('[AuthStore] Calling AuthService.login with:', credentials);
            const response = await AuthService.login(credentials);
            console.log('[AuthStore] AuthService.login response:', response);

            // Store token and user data
            TokenManager.setToken(response.token);
            TokenManager.setUser(response.user);
            
            set({
              user: response.user,
              token: response.token,
              isAuthenticated: true,
              isLoading: false,
              error: null,
            });
          } catch (error: any) {
            set({
              isLoading: false,
              error: error.message || 'Login failed',
              isAuthenticated: false,
              user: null,
              token: null,
            });
            throw error;
          }
        },

        register: async (userData: RegisterRequest) => {
          set({ isLoading: true, error: null });
          
          try {
            const response = await AuthService.register(userData);
            
            // Store token and user data
            TokenManager.setToken(response.token);
            TokenManager.setUser(response.user);
            
            set({
              user: response.user,
              token: response.token,
              isAuthenticated: true,
              isLoading: false,
              error: null,
            });
          } catch (error: any) {
            set({
              isLoading: false,
              error: error.message || 'Registration failed',
              isAuthenticated: false,
              user: null,
              token: null,
            });
            throw error;
          }
        },

        loginWithGoogle: async () => {
          set({ isLoading: true, error: null });

          try {
            // This will redirect to Google OAuth
            await SocialAuthService.loginWithGoogle();
            // Note: The actual login completion will happen on OAuth callback
          } catch (error: any) {
            set({
              isLoading: false,
              error: error.message || 'Google login failed',
              isAuthenticated: false,
              user: null,
              token: null,
            });
            throw error;
          }
        },

        loginWithApple: async () => {
          set({ isLoading: true, error: null });

          try {
            // This will redirect to Apple OAuth
            await SocialAuthService.loginWithApple();
            // Note: The actual login completion will happen on OAuth callback
          } catch (error: any) {
            set({
              isLoading: false,
              error: error.message || 'Apple login failed',
              isAuthenticated: false,
              user: null,
              token: null,
            });
            throw error;
          }
        },

        logout: async () => {
          set({ isLoading: true });
          
          try {
            // Call logout API to invalidate token on server
            await AuthService.logout();
          } catch (error) {
            // Continue with logout even if API call fails
            console.warn('Logout API call failed:', error);
          } finally {
            // Clear local storage and state
            TokenManager.removeToken();
            
            set({
              user: null,
              token: null,
              isAuthenticated: false,
              isLoading: false,
              error: null,
            });
          }
        },

        refreshToken: async () => {
          const currentToken = get().token;
          if (!currentToken) {
            throw new Error('No token to refresh');
          }

          try {
            const response = await AuthService.refreshToken();
            
            // Update token and user data
            TokenManager.setToken(response.token);
            TokenManager.setUser(response.user);
            
            set({
              user: response.user,
              token: response.token,
              isAuthenticated: true,
              error: null,
            });
          } catch (error: any) {
            // If refresh fails, logout user
            get().logout();
            throw error;
          }
        },

        validateToken: async (): Promise<boolean> => {
          const currentToken = get().token;
          if (!currentToken) {
            return false;
          }

          try {
            const response = await AuthService.validateToken();
            console.log('[AuthStore] Token validation response:', response);

            if (response.valid) {
              // Token is valid, keep current user data
              set({
                isAuthenticated: true,
                error: null,
              });
              return true;
            } else {
              // Token is invalid, logout
              console.log('[AuthStore] Token invalid, logging out');
              get().logout();
              return false;
            }
          } catch (error) {
            // Token validation failed, logout
            console.log('[AuthStore] Token validation failed:', error);
            get().logout();
            return false;
          }
        },

        forgotPassword: async (email: string) => {
          set({ isLoading: true, error: null });

          try {
            await AuthService.forgotPassword(email);
            set({
              isLoading: false,
              error: null,
            });
          } catch (error: any) {
            set({
              isLoading: false,
              error: error.message || 'Failed to send password reset email',
            });
            throw error;
          }
        },

        resetPassword: async (token: string, newPassword: string) => {
          set({ isLoading: true, error: null });

          try {
            await AuthService.resetPassword(token, newPassword);
            set({
              isLoading: false,
              error: null,
            });
          } catch (error: any) {
            set({
              isLoading: false,
              error: error.message || 'Password reset failed',
            });
            throw error;
          }
        },

        updateProfile: async (updates: Partial<UserProfile>) => {
          set({ isLoading: true, error: null });
          
          try {
            const updatedUser = await AuthService.updateProfile(updates);
            
            // Update local storage and state
            TokenManager.setUser(updatedUser);
            
            set({
              user: updatedUser,
              isLoading: false,
              error: null,
            });
          } catch (error: any) {
            set({
              isLoading: false,
              error: error.message || 'Profile update failed',
            });
            throw error;
          }
        },

        updateOnboardingStatus: async (status: UserProfile['onboardingStatus']) => {
          try {
            const updatedUser = await AuthService.updateOnboardingStatus(status);
            
            // Update local storage and state
            TokenManager.setUser(updatedUser);
            
            set({
              user: updatedUser,
              error: null,
            });
          } catch (error: any) {
            set({
              error: error.message || 'Onboarding status update failed',
            });
            throw error;
          }
        },

        clearError: () => {
          set({ error: null });
        },

        setLoading: (loading: boolean) => {
          set({ isLoading: loading });
        },

        initialize: async () => {
          set({ isLoading: true });
          
          try {
            // Check for stored token
            const storedToken = TokenManager.getToken();
            const storedUser = TokenManager.getUser();
            
            if (storedToken && storedUser) {
              // Validate the stored token
              const isValid = await get().validateToken();
              
              if (isValid) {
                set({
                  token: storedToken,
                  user: storedUser,
                  isAuthenticated: true,
                  isLoading: false,
                  error: null,
                });
              } else {
                set({
                  isLoading: false,
                  isAuthenticated: false,
                  user: null,
                  token: null,
                });
              }
            } else {
              set({
                isLoading: false,
                isAuthenticated: false,
                user: null,
                token: null,
              });
            }
          } catch (error) {
            console.error('Auth initialization failed:', error);
            set({
              isLoading: false,
              isAuthenticated: false,
              user: null,
              token: null,
              error: null, // Don't show error on initialization
            });
          }
        },
      }),
      {
        name: 'intellifin-auth',
        partialize: (state) => ({
          // Only persist essential data
          user: state.user,
          token: state.token,
          isAuthenticated: state.isAuthenticated,
        }),
      }
    ),
    {
      name: 'auth-store',
    }
  )
);

// Selectors for common use cases
export const authSelectors = {
  isLoggedIn: (state: AuthState) => state.isAuthenticated && !!state.user,
  isLoading: (state: AuthState) => state.isLoading,
  hasError: (state: AuthState) => !!state.error,
  needsOnboarding: (state: AuthState) => 
    state.user?.onboardingStatus !== 'ALL_SET',
  needsEmailVerification: (state: AuthState) => 
    state.user && !state.user.emailVerified,
  userDisplayName: (state: AuthState) => 
    state.user ? `${state.user.firstName} ${state.user.lastName}` : '',
  organizationName: (state: AuthState) => 
    state.user?.organizationName || '',
};
