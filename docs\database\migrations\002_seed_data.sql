-- IntelliFin Database Seed Data
-- Version: 1.0
-- Date: 2024-07-30

-- Insert default system categories for expenses
INSERT INTO categories (id, user_id, name, type, is_system_defined, created_at, updated_at) VALUES
-- Business Expenses
(uuid_generate_v4(), NULL, 'Office Supplies', 'EXPENSE', TRUE, NOW(), NOW()),
(uuid_generate_v4(), NULL, 'Transport', 'EXPENSE', TRUE, NOW(), NOW()),
(uuid_generate_v4(), NULL, 'Utilities', 'EXPENSE', TRUE, NOW(), NOW()),
(uuid_generate_v4(), NULL, 'Rent', 'EXPENSE', TRUE, NOW(), NOW()),
(uuid_generate_v4(), NULL, 'Marketing', 'EXPENSE', TRUE, NOW(), NOW()),
(uuid_generate_v4(), NULL, 'Professional Services', 'EXPENSE', TRUE, NOW(), NOW()),
(uuid_generate_v4(), NULL, 'Equipment', 'EXPENSE', TRUE, NOW(), NOW()),
(uuid_generate_v4(), NULL, 'Insurance', 'EXPENSE', TRUE, NOW(), NOW()),
(uuid_generate_v4(), NULL, 'Bank Charges', 'EXPENSE', TRUE, NOW(), NOW()),
(uuid_generate_v4(), NULL, 'Telecommunications', 'EXPENSE', TRUE, NOW(), NOW()),
(uuid_generate_v4(), NULL, 'Fuel', 'EXPENSE', TRUE, NOW(), NOW()),
(uuid_generate_v4(), NULL, 'Maintenance', 'EXPENSE', TRUE, NOW(), NOW()),
(uuid_generate_v4(), NULL, 'Training', 'EXPENSE', TRUE, NOW(), NOW()),
(uuid_generate_v4(), NULL, 'Legal Fees', 'EXPENSE', TRUE, NOW(), NOW()),
(uuid_generate_v4(), NULL, 'Licenses', 'EXPENSE', TRUE, NOW(), NOW()),

-- Inventory/Cost of Goods Sold
(uuid_generate_v4(), NULL, 'Raw Materials', 'EXPENSE', TRUE, NOW(), NOW()),
(uuid_generate_v4(), NULL, 'Inventory Purchase', 'EXPENSE', TRUE, NOW(), NOW()),
(uuid_generate_v4(), NULL, 'Packaging', 'EXPENSE', TRUE, NOW(), NOW()),
(uuid_generate_v4(), NULL, 'Shipping', 'EXPENSE', TRUE, NOW(), NOW()),

-- Personal/Owner Expenses
(uuid_generate_v4(), NULL, 'Owner Draw', 'EXPENSE', TRUE, NOW(), NOW()),
(uuid_generate_v4(), NULL, 'Personal Expense', 'EXPENSE', TRUE, NOW(), NOW()),

-- Tax Related
(uuid_generate_v4(), NULL, 'Tax Payment', 'EXPENSE', TRUE, NOW(), NOW()),
(uuid_generate_v4(), NULL, 'VAT Payment', 'EXPENSE', TRUE, NOW(), NOW()),

-- Other
(uuid_generate_v4(), NULL, 'Miscellaneous', 'EXPENSE', TRUE, NOW(), NOW()),
(uuid_generate_v4(), NULL, 'Uncategorized', 'EXPENSE', TRUE, NOW(), NOW());

-- Insert default system categories for income
INSERT INTO categories (id, user_id, name, type, is_system_defined, created_at, updated_at) VALUES
-- Revenue Streams
(uuid_generate_v4(), NULL, 'Sales Revenue', 'INCOME', TRUE, NOW(), NOW()),
(uuid_generate_v4(), NULL, 'Service Revenue', 'INCOME', TRUE, NOW(), NOW()),
(uuid_generate_v4(), NULL, 'Consulting Revenue', 'INCOME', TRUE, NOW(), NOW()),
(uuid_generate_v4(), NULL, 'Commission Income', 'INCOME', TRUE, NOW(), NOW()),
(uuid_generate_v4(), NULL, 'Rental Income', 'INCOME', TRUE, NOW(), NOW()),
(uuid_generate_v4(), NULL, 'Interest Income', 'INCOME', TRUE, NOW(), NOW()),

-- Other Income
(uuid_generate_v4(), NULL, 'Refunds', 'INCOME', TRUE, NOW(), NOW()),
(uuid_generate_v4(), NULL, 'Grants', 'INCOME', TRUE, NOW(), NOW()),
(uuid_generate_v4(), NULL, 'Other Income', 'INCOME', TRUE, NOW(), NOW()),
(uuid_generate_v4(), NULL, 'Uncategorized Income', 'INCOME', TRUE, NOW(), NOW());

-- Insert default system accounts for chart of accounts
INSERT INTO accounts (id, user_id, name, account_type, balance, is_system_defined, created_at, updated_at) VALUES
-- Assets
(uuid_generate_v4(), NULL, 'Cash', 'ASSET', 0.00, TRUE, NOW(), NOW()),
(uuid_generate_v4(), NULL, 'Bank Account', 'ASSET', 0.00, TRUE, NOW(), NOW()),
(uuid_generate_v4(), NULL, 'Mobile Money', 'ASSET', 0.00, TRUE, NOW(), NOW()),
(uuid_generate_v4(), NULL, 'Accounts Receivable', 'ASSET', 0.00, TRUE, NOW(), NOW()),
(uuid_generate_v4(), NULL, 'Inventory', 'ASSET', 0.00, TRUE, NOW(), NOW()),
(uuid_generate_v4(), NULL, 'Equipment', 'ASSET', 0.00, TRUE, NOW(), NOW()),
(uuid_generate_v4(), NULL, 'Prepaid Expenses', 'ASSET', 0.00, TRUE, NOW(), NOW()),

-- Liabilities
(uuid_generate_v4(), NULL, 'Accounts Payable', 'LIABILITY', 0.00, TRUE, NOW(), NOW()),
(uuid_generate_v4(), NULL, 'Loans Payable', 'LIABILITY', 0.00, TRUE, NOW(), NOW()),
(uuid_generate_v4(), NULL, 'VAT Payable', 'LIABILITY', 0.00, TRUE, NOW(), NOW()),
(uuid_generate_v4(), NULL, 'Income Tax Payable', 'LIABILITY', 0.00, TRUE, NOW(), NOW()),

-- Equity
(uuid_generate_v4(), NULL, 'Owner Equity', 'EQUITY', 0.00, TRUE, NOW(), NOW()),
(uuid_generate_v4(), NULL, 'Retained Earnings', 'EQUITY', 0.00, TRUE, NOW(), NOW()),

-- Revenue
(uuid_generate_v4(), NULL, 'Sales Revenue', 'REVENUE', 0.00, TRUE, NOW(), NOW()),
(uuid_generate_v4(), NULL, 'Service Revenue', 'REVENUE', 0.00, TRUE, NOW(), NOW()),
(uuid_generate_v4(), NULL, 'Other Revenue', 'REVENUE', 0.00, TRUE, NOW(), NOW()),

-- Expenses
(uuid_generate_v4(), NULL, 'Cost of Goods Sold', 'EXPENSE', 0.00, TRUE, NOW(), NOW()),
(uuid_generate_v4(), NULL, 'Operating Expenses', 'EXPENSE', 0.00, TRUE, NOW(), NOW()),
(uuid_generate_v4(), NULL, 'Administrative Expenses', 'EXPENSE', 0.00, TRUE, NOW(), NOW()),
(uuid_generate_v4(), NULL, 'Marketing Expenses', 'EXPENSE', 0.00, TRUE, NOW(), NOW()),
(uuid_generate_v4(), NULL, 'Interest Expense', 'EXPENSE', 0.00, TRUE, NOW(), NOW());
