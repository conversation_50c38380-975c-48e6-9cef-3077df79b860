#!/bin/sh
# Maven Wrapper Script
# This script allows you to run <PERSON><PERSON> without having <PERSON><PERSON> installed

MAVEN_WRAPPER_VERSION="3.9.5"
MAVEN_WRAPPER_JAR=".mvn/wrapper/maven-wrapper.jar"
MAVEN_WRAPPER_PROPERTIES=".mvn/wrapper/maven-wrapper.properties"

# Check if Maven wrapper jar exists
if [ ! -f "$MAVEN_WRAPPER_JAR" ]; then
    echo "Maven wrapper jar not found. Please run 'mvn wrapper:wrapper' first."
    exit 1
fi

# Execute Maven with wrapper
exec java -jar "$MAVEN_WRAPPER_JAR" "$@"
