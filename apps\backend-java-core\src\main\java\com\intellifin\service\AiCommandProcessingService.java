package com.intellifin.service;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.intellifin.model.Conversation;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
@RequiredArgsConstructor
@Slf4j
public class AiCommandProcessingService {

    private final RestTemplate restTemplate;
    private final ObjectMapper objectMapper;

    @Value("${external.ai-service.url:http://localhost:8000}")
    private String aiServiceUrl;

    @Value("${external.ai-service.timeout:30}")
    private int timeoutSeconds;

    public CommandProcessingResult processCommand(String command, Conversation conversation) {
        log.debug("Processing command with AI service: {}", command);
        
        try {
            // Prepare request payload
            Map<String, Object> requestPayload = new HashMap<>();
            requestPayload.put("command", command);
            requestPayload.put("context", buildConversationContext(conversation));
            requestPayload.put("user_id", conversation.getUser().getId().toString());
            requestPayload.put("conversation_id", conversation.getId().toString());
            
            // Set up headers
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            headers.setAccept(List.of(MediaType.APPLICATION_JSON));
            
            HttpEntity<Map<String, Object>> request = new HttpEntity<>(requestPayload, headers);
            
            // Call AI service
            String endpoint = aiServiceUrl + "/api/v1/process-command";
            ResponseEntity<String> response = restTemplate.exchange(
                endpoint,
                HttpMethod.POST,
                request,
                String.class
            );
            
            if (response.getStatusCode().is2xxSuccessful() && response.getBody() != null) {
                return parseAiResponse(response.getBody());
            } else {
                log.warn("AI service returned non-success status: {}", response.getStatusCode());
                return createFallbackResponse(command);
            }
            
        } catch (Exception e) {
            log.error("Error calling AI service for command processing", e);
            return createFallbackResponse(command);
        }
    }

    private Map<String, Object> buildConversationContext(Conversation conversation) {
        Map<String, Object> context = new HashMap<>();
        context.put("conversation_id", conversation.getId().toString());
        context.put("session_id", conversation.getSessionId());
        context.put("user_id", conversation.getUser().getId().toString());
        context.put("user_name", conversation.getUser().getFullName());
        context.put("user_email", conversation.getUser().getEmail());
        context.put("organization", conversation.getUser().getOrganizationName());
        context.put("message_count", conversation.getMessageCount());
        context.put("conversation_title", conversation.getTitle());
        
        // Add recent message history for context
        if (!conversation.getMessages().isEmpty()) {
            List<Map<String, Object>> recentMessages = conversation.getMessages()
                .stream()
                .limit(10) // Last 10 messages for context
                .map(msg -> {
                    Map<String, Object> msgMap = new HashMap<>();
                    msgMap.put("role", msg.getRole().toString().toLowerCase());
                    msgMap.put("content", msg.getContent());
                    msgMap.put("intent", msg.getIntentRecognized());
                    msgMap.put("timestamp", msg.getCreatedAt().toString());
                    return msgMap;
                })
                .toList();
            context.put("recent_messages", recentMessages);
        }
        
        return context;
    }

    private CommandProcessingResult parseAiResponse(String responseBody) {
        try {
            JsonNode responseJson = objectMapper.readTree(responseBody);
            
            String intent = responseJson.path("intent").asText("unknown");
            double confidence = responseJson.path("confidence").asDouble(0.0);
            String response = responseJson.path("response").asText("I'm not sure how to help with that.");
            boolean requiresFollowUp = responseJson.path("requires_follow_up").asBoolean(false);
            String followUpPrompt = responseJson.path("follow_up_prompt").asText(null);
            
            // Extract any additional metadata
            Map<String, Object> metadata = new HashMap<>();
            if (responseJson.has("metadata")) {
                JsonNode metadataNode = responseJson.get("metadata");
                metadataNode.fieldNames().forEachRemaining(fieldName -> {
                    metadata.put(fieldName, metadataNode.get(fieldName).asText());
                });
            }
            
            return CommandProcessingResult.builder()
                    .intent(intent)
                    .confidence(confidence)
                    .response(response)
                    .requiresFollowUp(requiresFollowUp)
                    .followUpPrompt(followUpPrompt)
                    .metadata(metadata)
                    .success(true)
                    .build();
                    
        } catch (Exception e) {
            log.error("Error parsing AI service response", e);
            return createFallbackResponse("Error parsing AI response");
        }
    }

    private CommandProcessingResult createFallbackResponse(String originalCommand) {
        String fallbackResponse = generateFallbackResponse(originalCommand);
        
        return CommandProcessingResult.builder()
                .intent("unknown")
                .confidence(0.1)
                .response(fallbackResponse)
                .requiresFollowUp(false)
                .success(false)
                .build();
    }

    private String generateFallbackResponse(String command) {
        // Simple keyword-based fallback responses
        String lowerCommand = command.toLowerCase();
        
        if (lowerCommand.contains("balance") || lowerCommand.contains("money")) {
            return "I can help you check your account balance. Please make sure your accounts are connected in the settings.";
        } else if (lowerCommand.contains("transaction") || lowerCommand.contains("payment")) {
            return "I can help you manage transactions. Would you like to view recent transactions or add a new one?";
        } else if (lowerCommand.contains("invoice") || lowerCommand.contains("bill")) {
            return "I can help you create invoices. Would you like to create a new invoice or view existing ones?";
        } else if (lowerCommand.contains("help") || lowerCommand.contains("what can you do")) {
            return "I'm IntelliFin's AI assistant! I can help you with:\n" +
                   "• Checking account balances\n" +
                   "• Managing transactions\n" +
                   "• Creating invoices\n" +
                   "• Financial insights\n" +
                   "• ZRA compliance\n\n" +
                   "Just ask me in natural language!";
        } else {
            return "I'm not sure how to help with that right now. I'm still learning! " +
                   "You can ask me about account balances, transactions, invoices, or say 'help' to see what I can do.";
        }
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class CommandProcessingResult {
        private String intent;
        private Double confidence;
        private String response;
        private Boolean requiresFollowUp;
        private String followUpPrompt;
        private Map<String, Object> metadata;
        private Boolean success;
        
        public boolean requiresFollowUp() {
            return Boolean.TRUE.equals(requiresFollowUp);
        }
        
        public boolean isSuccess() {
            return Boolean.TRUE.equals(success);
        }
    }
}
