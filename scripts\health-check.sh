#!/bin/bash

# IntelliFin Health Check Script
# Validates that all services are running and healthy

set -e

echo "🏥 IntelliFin Health Check Starting..."
echo "=================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Service endpoints
BACKEND_CORE_URL="http://localhost:8080"
AI_SERVICE_URL="http://localhost:8000"
ZRA_SERVICE_URL="http://localhost:8081"
FRONTEND_URL="http://localhost:3000"

# Function to check service health
check_service() {
    local service_name=$1
    local url=$2
    local endpoint=$3
    
    echo -n "Checking $service_name... "
    
    if curl -f -s "$url$endpoint" > /dev/null 2>&1; then
        echo -e "${GREEN}✅ Healthy${NC}"
        return 0
    else
        echo -e "${RED}❌ Unhealthy${NC}"
        return 1
    fi
}

# Function to check detailed service info
check_service_detailed() {
    local service_name=$1
    local url=$2
    local health_endpoint=$3
    local info_endpoint=$4
    
    echo "📊 $service_name Details:"
    echo "------------------------"
    
    # Health check
    if health_response=$(curl -f -s "$url$health_endpoint" 2>/dev/null); then
        echo "  Status: $(echo "$health_response" | jq -r '.status // "unknown"')"
        echo "  Version: $(echo "$health_response" | jq -r '.version // "unknown"')"
        echo "  Uptime: $(echo "$health_response" | jq -r '.uptime // "unknown"')s"
        echo "  Environment: $(echo "$health_response" | jq -r '.environment // "unknown"')"
    else
        echo -e "  ${RED}❌ Health check failed${NC}"
    fi
    
    # Info check (if available)
    if [ -n "$info_endpoint" ] && info_response=$(curl -f -s "$url$info_endpoint" 2>/dev/null); then
        echo "  Description: $(echo "$info_response" | jq -r '.description // "N/A"')"
    fi
    
    echo ""
}

# Function to check Docker containers
check_docker_containers() {
    echo "🐳 Docker Container Status:"
    echo "----------------------------"
    
    containers=(
        "intellifin-postgres"
        "intellifin-redis"
        "intellifin-ollama"
        "intellifin-backend-core"
        "intellifin-ai-service"
        "intellifin-zra-service"
        "intellifin-frontend"
    )
    
    for container in "${containers[@]}"; do
        if docker ps --format "table {{.Names}}\t{{.Status}}" | grep -q "$container"; then
            status=$(docker ps --format "table {{.Names}}\t{{.Status}}" | grep "$container" | awk '{print $2, $3}')
            echo -e "  $container: ${GREEN}$status${NC}"
        else
            echo -e "  $container: ${RED}❌ Not running${NC}"
        fi
    done
    echo ""
}

# Function to check database connectivity
check_database() {
    echo "🗄️  Database Connectivity:"
    echo "--------------------------"
    
    if docker exec intellifin-postgres pg_isready -U intellifin_user -d intellifin_dev > /dev/null 2>&1; then
        echo -e "  PostgreSQL: ${GREEN}✅ Connected${NC}"
        
        # Check if tables exist
        table_count=$(docker exec intellifin-postgres psql -U intellifin_user -d intellifin_dev -t -c "SELECT count(*) FROM information_schema.tables WHERE table_schema = 'public';" 2>/dev/null | xargs)
        echo "  Tables: $table_count"
    else
        echo -e "  PostgreSQL: ${RED}❌ Connection failed${NC}"
    fi
    
    if docker exec intellifin-redis redis-cli ping > /dev/null 2>&1; then
        echo -e "  Redis: ${GREEN}✅ Connected${NC}"
    else
        echo -e "  Redis: ${RED}❌ Connection failed${NC}"
    fi
    echo ""
}

# Function to check AI model availability
check_ai_models() {
    echo "🤖 AI Model Status:"
    echo "-------------------"
    
    if docker exec intellifin-ollama ollama list > /dev/null 2>&1; then
        models=$(docker exec intellifin-ollama ollama list 2>/dev/null | tail -n +2 | wc -l)
        echo -e "  Ollama: ${GREEN}✅ Running${NC}"
        echo "  Models available: $models"
        
        if [ "$models" -gt 0 ]; then
            echo "  Model list:"
            docker exec intellifin-ollama ollama list 2>/dev/null | tail -n +2 | while read line; do
                model_name=$(echo "$line" | awk '{print $1}')
                echo "    - $model_name"
            done
        fi
    else
        echo -e "  Ollama: ${RED}❌ Not available${NC}"
    fi
    echo ""
}

# Main health check execution
main() {
    echo "🔍 Quick Health Check:"
    echo "----------------------"
    
    # Quick service checks
    check_service "Backend Core" "$BACKEND_CORE_URL" "/api/v1/health"
    check_service "AI Service" "$AI_SERVICE_URL" "/health"
    check_service "ZRA Service" "$ZRA_SERVICE_URL" "/actuator/health"
    check_service "Frontend" "$FRONTEND_URL" "/api/health"
    
    echo ""
    
    # Docker container status
    check_docker_containers
    
    # Database connectivity
    check_database
    
    # AI models
    check_ai_models
    
    # Detailed service information
    echo "📋 Detailed Service Information:"
    echo "================================"
    check_service_detailed "Backend Core" "$BACKEND_CORE_URL" "/api/v1/health" "/api/v1/info"
    check_service_detailed "AI Service" "$AI_SERVICE_URL" "/health" "/info"
    check_service_detailed "ZRA Service" "$ZRA_SERVICE_URL" "/actuator/health" "/actuator/info"
    
    echo "🎉 Health check completed!"
    echo ""
    echo "📖 Quick Access URLs:"
    echo "  Frontend: http://localhost:3000"
    echo "  Backend API: http://localhost:8080/api/v1"
    echo "  AI Service: http://localhost:8000"
    echo "  API Docs: http://localhost:8080/swagger-ui.html"
    echo "  AI Docs: http://localhost:8000/docs"
    echo "  pgAdmin: http://localhost:5050"
}

# Check if jq is installed
if ! command -v jq &> /dev/null; then
    echo -e "${YELLOW}⚠️  Warning: jq is not installed. JSON parsing will be limited.${NC}"
    echo "   Install with: sudo apt-get install jq (Ubuntu) or brew install jq (macOS)"
    echo ""
fi

# Run main function
main
