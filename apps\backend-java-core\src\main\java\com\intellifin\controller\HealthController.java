package com.intellifin.controller;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.time.Instant;
import java.util.HashMap;
import java.util.Map;

@RestController
@RequestMapping("/api/v1")
public class HealthController {

    @Value("${spring.application.name:intellifin-backend-core}")
    private String serviceName;

    @Value("${spring.profiles.active:default}")
    private String activeProfile;

    private final long startTime = System.currentTimeMillis();

    @GetMapping("/health")
    public ResponseEntity<Map<String, Object>> health() {
        Map<String, Object> health = new HashMap<>();
        health.put("status", "healthy");
        health.put("service", serviceName);
        health.put("timestamp", Instant.now().toString());
        health.put("version", "1.0.0");
        health.put("uptime", System.currentTimeMillis() - startTime);
        health.put("environment", activeProfile);
        
        return ResponseEntity.ok(health);
    }

    @GetMapping("/info")
    public ResponseEntity<Map<String, Object>> info() {
        Map<String, Object> info = new HashMap<>();
        info.put("service", serviceName);
        info.put("version", "1.0.0");
        info.put("description", "IntelliFin Core Backend Service");
        info.put("environment", activeProfile);
        info.put("java.version", System.getProperty("java.version"));
        info.put("spring.version", org.springframework.core.SpringVersion.getVersion());
        
        return ResponseEntity.ok(info);
    }
}
