'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useAuthStore } from '@/stores/authStore';
import { ROUTES } from '@/utils/constants';

export default function OnboardingPage() {
  const router = useRouter();
  const { user, updateOnboardingStatus, isLoading } = useAuthStore();
  const [currentStep, setCurrentStep] = useState(0);

  const steps = [
    {
      id: 'STARTED',
      title: 'Welcome to IntelliFin',
      description: 'Let\'s get you set up with your financial management platform.',
    },
    {
      id: 'PROFILE_COMPLETE',
      title: 'Profile Setup',
      description: 'Your basic profile information has been collected.',
    },
    {
      id: 'ACCOUNTS_CONNECTED',
      title: 'Connect Accounts',
      description: 'Connect your financial accounts to get started.',
    },
    {
      id: 'ALL_SET',
      title: 'All Set!',
      description: 'You\'re ready to start using IntelliFin.',
    },
  ];

  useEffect(() => {
    if (!user) {
      router.push(ROUTES.LOGIN);
      return;
    }

    // Set current step based on onboarding status
    const stepIndex = steps.findIndex(step => step.id === user.onboardingStatus);
    setCurrentStep(stepIndex >= 0 ? stepIndex : 0);
  }, [user, router]);

  const handleNextStep = async () => {
    if (currentStep < steps.length - 1) {
      const nextStep = steps[currentStep + 1];
      try {
        // TODO: Implement backend UserController for onboarding status updates
        // await updateOnboardingStatus(nextStep.id as any);
        console.log('Would update onboarding status to:', nextStep.id);
        setCurrentStep(currentStep + 1);
      } catch (error) {
        console.error('Failed to update onboarding status:', error);
      }
    }
  };

  const handleSkipToEnd = async () => {
    try {
      // TODO: Implement backend UserController for onboarding status updates
      // await updateOnboardingStatus('ALL_SET');
      console.log('Would update onboarding status to: ALL_SET');
      router.push(ROUTES.DASHBOARD);
    } catch (error) {
      console.error('Failed to complete onboarding:', error);
    }
  };

  const handleGoToDashboard = () => {
    router.push(ROUTES.DASHBOARD);
  };

  if (!user) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-2 text-gray-600">Loading...</p>
        </div>
      </div>
    );
  }

  const currentStepData = steps[currentStep];
  const isLastStep = currentStep === steps.length - 1;

  return (
    <div className="min-h-screen bg-gray-50 flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-md w-full space-y-8">
        <div className="text-center">
          <h2 className="mt-6 text-3xl font-extrabold text-gray-900">
            Welcome to IntelliFin
          </h2>
          <p className="mt-2 text-sm text-gray-600">
            Let's get you set up in just a few steps
          </p>
        </div>

        <div className="bg-white rounded-lg shadow-md p-6">
          {/* Progress Bar */}
          <div className="mb-6">
            <div className="flex justify-between text-xs text-gray-500 mb-2">
              <span>Step {currentStep + 1} of {steps.length}</span>
              <span>{Math.round(((currentStep + 1) / steps.length) * 100)}% Complete</span>
            </div>
            <div className="w-full bg-gray-200 rounded-full h-2">
              <div 
                className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                style={{ width: `${((currentStep + 1) / steps.length) * 100}%` }}
              ></div>
            </div>
          </div>

          {/* Current Step Content */}
          <div className="text-center mb-6">
            <h3 className="text-xl font-semibold text-gray-900 mb-2">
              {currentStepData.title}
            </h3>
            <p className="text-gray-600">
              {currentStepData.description}
            </p>
          </div>

          {/* User Info */}
          <div className="bg-gray-50 rounded-lg p-4 mb-6">
            <h4 className="font-medium text-gray-900 mb-2">Your Information</h4>
            <div className="text-sm text-gray-600 space-y-1">
              <p><span className="font-medium">Name:</span> {user.firstName} {user.lastName}</p>
              <p><span className="font-medium">Email:</span> {user.email}</p>
              {user.organizationName && (
                <p><span className="font-medium">Organization:</span> {user.organizationName}</p>
              )}
              <p><span className="font-medium">Status:</span> {user.onboardingStatus}</p>
            </div>
          </div>

          {/* Action Buttons */}
          <div className="space-y-3">
            {!isLastStep ? (
              <>
                <button
                  onClick={handleNextStep}
                  disabled={isLoading}
                  className="w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  {isLoading ? 'Processing...' : 'Continue'}
                </button>
                <button
                  onClick={handleSkipToEnd}
                  disabled={isLoading}
                  className="w-full flex justify-center py-2 px-4 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  Skip to Dashboard
                </button>
              </>
            ) : (
              <button
                onClick={handleGoToDashboard}
                className="w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500"
              >
                Go to Dashboard
              </button>
            )}
          </div>

          {/* Debug Info (for development) */}
          {process.env.NODE_ENV === 'development' && (
            <div className="mt-6 p-3 bg-yellow-50 border border-yellow-200 rounded-md">
              <p className="text-xs text-yellow-800">
                <strong>Debug:</strong> Current onboarding status: {user.onboardingStatus}
              </p>
              <p className="text-xs text-yellow-800 mt-1">
                <strong>Note:</strong> Backend UserController not implemented yet. Onboarding status updates are mocked.
              </p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
