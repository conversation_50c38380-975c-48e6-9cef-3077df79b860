# IntelliFin PRD - Epic List

## Epic List (Revised)

Here is the revised list of the Epics planned for the IntelliFin MVP. This structure prioritizes a cost-effective, local-first development workflow and a clear path of delivering incremental value to the user.

### Epic 1: Local Development Environment & CI/CD Foundation

*   **Goal:** The primary objective of this foundational epic is to maximize developer velocity and minimize operational costs by creating a fully containerized, "one-command" local development environment. Our AI service will be configured to use Google's Gemini free tier API, ensuring we develop against a production-class model from day one, at zero initial cost. This entire local environment, containerized with Docker, will form the basis for our CI/CD pipeline in GitHub Actions, which will automate the building, testing, and future deployment of our applications to Azure.
*   **Key Deliverables:**
    *   Create Dockerfiles for all microservices (Java/Spring Boot, Python/FastAPI, Next.js).
    *   Create a docker-compose.yml file to orchestrate the entire stack locally (including PostgreSQL and Redis containers).
    *   Integrate Ollama into the Docker Compose setup to run a local LLM for cost-free AI development and testing.
    *   Establish the foundational CI/CD pipeline using GitHub Actions that automates building, testing, and containerizing our applications.
*   **Outcome:** A developer can clone the repository and run a single command to have the entire IntelliFin application running on their local machine.

### Epic 2: Cloud Provisioning & User Onboarding

*   **Goal:** To deploy the foundational platform to the cloud and deliver the first piece of user-facing functionality: secure user registration and login.
*   **Key Deliverables:**
    *   Provision the initial, essential Azure infrastructure (Static Web Apps, App Service, PostgreSQL).
    *   Develop and deploy the user registration and login features.
    *   Develop and deploy the secure connection flow for a user's MTN Mobile Money account.
*   **Outcome:** The application is live on the internet, and a user can create an account and connect their financial lifeline.

### Epic 3: Foundational AI Engine & Transaction Categorization

*   **Goal:** To build and activate our core "Neural Financial Network" to deliver the first "magic moment" of automated transaction categorization.
*   **Key Deliverables:**
    *   Set up the LangChain4j orchestrator and configure the RAG pipeline to work with both the local Ollama LLM and the cloud-based Azure AI Search.
    *   Implement the full end-to-end user workflow for AI-powered transaction categorization and user review/confirmation.
*   **Outcome:** A user can see their chaotic transaction data automatically organized and categorized by the AI.

### Epic 4: ZRA Smart Invoicing & Compliance Engine

*   **Goal:** To solve the user's acute compliance pain point by enabling AI-driven ZRA invoice creation.
*   **Key Deliverables:** Develop the functionality for users to initiate, draft, review, approve, and submit ZRA-compliant Smart Invoices via natural language commands.
*   **Outcome:** A user can create and submit a ZRA-compliant invoice simply by having a conversation with the platform.

### Epic 5: Basic Financial Insights & Reporting

*   **Goal:** To deliver on the promise of financial visibility by allowing users to ask questions about their business's performance.
*   **Key Deliverables:** Enable users to request and view fundamental financial summaries (Sales, Expenses, Profit/Loss) through the conversational interface.
*   **Outcome:** A user can get an instant, clear answer to the question, "How is my business doing?"

---

**Previous Section:** [Technical Assumptions](../prd/05-technical-assumptions.md)  
**Next Section:** [Next Steps](../prd/07-next-steps.md) 