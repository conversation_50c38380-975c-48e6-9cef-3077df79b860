# IntelliFin PRD - Technical Assumptions

## Technical Assumptions

This section documents the technical decisions that will guide the Architect in designing IntelliFin.

### Architecture Decisions

*   **Repository Structure:** Monorepo - For a unified codebase managing polyglot microservices (as stated in Project Brief).
*   **Service Architecture:** Microservices Architecture - To prioritize scalability, security, and maintainability, with specialized services developed in their optimal language (Java Spring Boot, Python FastAPI, Containerized Java ZRA app).

### Testing Strategy

Our testing strategy will follow the Testing Pyramid model to ensure a balance of speed, cost, and confidence. Robust, automated testing is a non-negotiable requirement.

*   **Unit Tests (The Foundation):** All critical business logic in the backend services (Java, Python) and key components/utility functions in the frontend (TypeScript) must be covered by unit tests. We will aim for a minimum of 80% code coverage on these critical modules.
*   **Integration Tests (The Connective Tissue):** We must have automated tests that verify the "contracts" between our services. This includes testing our Spring Boot API endpoints to ensure they behave as expected when called, and testing the interaction between our services and the PostgreSQL database.
*   **End-to-End (E2E) Tests (The User Journey):** For the MVP, we will implement a small but vital suite of automated E2E tests covering the most critical user "happy paths":
    *   Successful user registration, login, and connection of an MTN account.
    *   A user command to view a financial summary and the successful display of that summary.
    *   The full lifecycle of creating a ZRA invoice via a command, reviewing the draft, and receiving a success confirmation (mocking the final ZRA submission).
*   **Manual QA:** A formal QA checklist must be executed before every release. This will cover usability, visual consistency across supported browsers, and exploratory testing to catch issues not covered by automated tests.

### Technology Stack

*   **Cloud Provider:** Microsoft Azure
*   **Frontend Framework/Language:** Next.js 14 (React) with TypeScript
*   **Backend Core Logic:** Java with Spring Boot 3
*   **Specialized AI/ML:** Python with FastAPI (containerized)
*   **ZRA Compliance Application:** Dedicated containerized Java application
*   **Primary Database:** PostgreSQL
*   **Caching:** Redis
*   **AI Orchestration:** LangChain4j
*   **AI Knowledge Base/Vector DB:** Azure AI Search
*   **Real-time Communication:** Spring WebSockets (STOMP protocol)
*   **Containerization Tool:** Docker
*   **Critical Integrations:** MTN Mobile Money API, Stitch aggregator API, ZRA API for VSDC communication.

---

**Previous Section:** [User Interface Design Goals](../prd/04-ui-design-goals.md)  
**Next Section:** [Epic List](../prd/06-epic-list.md) 