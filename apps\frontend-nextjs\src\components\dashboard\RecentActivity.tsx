"use client"

import React from 'react';
import { motion } from 'framer-motion';
import { 
  TrendingUp, 
  TrendingDown, 
  Receipt, 
  CreditCard, 
  Users, 
  AlertCircle,
  CheckCircle,
  Clock,
  ExternalLink,
  MoreHorizontal
} from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { DashboardData } from '@/types/financial';
import { formatCurrency, formatRelativeTime } from '@/utils';

interface RecentActivityProps {
  data: DashboardData | null;
  isLoading: boolean;
}

interface ActivityItem {
  id: string;
  type: 'transaction' | 'invoice' | 'client' | 'alert' | 'insight';
  title: string;
  description: string;
  amount?: number;
  timestamp: string;
  status: 'success' | 'warning' | 'error' | 'info';
  icon: React.ComponentType<{ className?: string }>;
  actionable?: boolean;
}

export function RecentActivity({ data, isLoading }: RecentActivityProps) {
  if (isLoading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">Recent Activity</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {[1, 2, 3, 4, 5].map((i) => (
              <div key={i} className="animate-pulse">
                <div className="flex items-start space-x-3">
                  <div className="w-8 h-8 bg-gray-200 rounded-full" />
                  <div className="flex-1 space-y-2">
                    <div className="w-3/4 h-4 bg-gray-200 rounded" />
                    <div className="w-1/2 h-3 bg-gray-200 rounded" />
                  </div>
                  <div className="w-16 h-4 bg-gray-200 rounded" />
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    );
  }

  if (!data) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">Recent Activity</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center py-8">
            <Clock className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <p className="text-gray-500">No recent activity</p>
            <p className="text-sm text-gray-400 mt-1">
              Activity will appear here as you use IntelliFin
            </p>
          </div>
        </CardContent>
      </Card>
    );
  }

  // Generate activity items from dashboard data
  const activities: ActivityItem[] = [];

  // Add recent transactions
  data.recentTransactions.slice(0, 3).forEach(transaction => {
    activities.push({
      id: `transaction-${transaction.id}`,
      type: 'transaction',
      title: transaction.type === 'INCOME' ? 'Payment Received' : 'Expense Recorded',
      description: transaction.description,
      amount: transaction.amount,
      timestamp: transaction.createdAt,
      status: transaction.type === 'INCOME' ? 'success' : 'info',
      icon: transaction.type === 'INCOME' ? TrendingUp : TrendingDown,
      actionable: transaction.status === 'PENDING_CLASSIFICATION',
    });
  });

  // Add recent invoices
  data.pendingInvoices.slice(0, 2).forEach(invoice => {
    activities.push({
      id: `invoice-${invoice.id}`,
      type: 'invoice',
      title: 'Invoice Created',
      description: `Invoice ${invoice.invoiceNumber} for ${invoice.client.name}`,
      amount: invoice.totalAmount,
      timestamp: invoice.createdAt,
      status: invoice.status === 'OVERDUE' ? 'error' : 'warning',
      icon: Receipt,
      actionable: true,
    });
  });

  // Add alerts
  data.alerts.slice(0, 2).forEach(alert => {
    activities.push({
      id: `alert-${alert.id}`,
      type: 'alert',
      title: alert.title,
      description: alert.message,
      timestamp: alert.createdAt,
      status: alert.severity === 'CRITICAL' ? 'error' : alert.severity === 'HIGH' ? 'warning' : 'info',
      icon: AlertCircle,
      actionable: alert.actionRequired,
    });
  });

  // Add insights
  data.insights.slice(0, 1).forEach(insight => {
    activities.push({
      id: `insight-${insight.id}`,
      type: 'insight',
      title: insight.title,
      description: insight.description,
      timestamp: insight.createdAt,
      status: insight.impact === 'POSITIVE' ? 'success' : insight.impact === 'NEGATIVE' ? 'warning' : 'info',
      icon: CheckCircle,
      actionable: false,
    });
  });

  // Sort by timestamp (most recent first)
  activities.sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime());

  // Take only the most recent 6 items
  const recentActivities = activities.slice(0, 6);

  const getStatusColor = (status: string) => {
    const colors = {
      success: 'text-green-500 bg-green-100',
      warning: 'text-yellow-500 bg-yellow-100',
      error: 'text-red-500 bg-red-100',
      info: 'text-blue-500 bg-blue-100',
    };
    return colors[status as keyof typeof colors] || colors.info;
  };

  const getAmountColor = (type: string, amount?: number) => {
    if (!amount) return '';
    if (type === 'transaction') {
      return amount > 0 ? 'text-green-600' : 'text-red-600';
    }
    return 'text-gray-900';
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="text-lg flex items-center justify-between">
          Recent Activity
          <Button variant="ghost" size="sm">
            <MoreHorizontal className="h-4 w-4" />
          </Button>
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {recentActivities.map((activity, index) => (
            <motion.div
              key={activity.id}
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.3, delay: index * 0.1 }}
              className={`flex items-start space-x-3 p-3 rounded-lg hover:bg-gray-50 transition-colors ${
                activity.actionable ? 'cursor-pointer border border-transparent hover:border-primary/20' : ''
              }`}
            >
              <div className={`w-8 h-8 rounded-full flex items-center justify-center flex-shrink-0 ${getStatusColor(activity.status)}`}>
                <activity.icon className="h-4 w-4" />
              </div>
              
              <div className="flex-1 min-w-0">
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <p className="text-sm font-medium text-gray-900 truncate">
                      {activity.title}
                    </p>
                    <p className="text-xs text-gray-500 mt-1 line-clamp-2">
                      {activity.description}
                    </p>
                    <p className="text-xs text-gray-400 mt-1">
                      {formatRelativeTime(activity.timestamp)}
                    </p>
                  </div>
                  
                  <div className="flex flex-col items-end ml-2">
                    {activity.amount && (
                      <span className={`text-sm font-semibold ${getAmountColor(activity.type, activity.amount)}`}>
                        {activity.type === 'transaction' && activity.amount > 0 ? '+' : ''}
                        {formatCurrency(Math.abs(activity.amount))}
                      </span>
                    )}
                    {activity.actionable && (
                      <ExternalLink className="h-3 w-3 text-gray-400 mt-1" />
                    )}
                  </div>
                </div>
              </div>
            </motion.div>
          ))}
        </div>

        {recentActivities.length === 0 && (
          <div className="text-center py-8">
            <Clock className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <p className="text-gray-500">No recent activity</p>
            <p className="text-sm text-gray-400 mt-1">
              Start by connecting your accounts or creating transactions
            </p>
          </div>
        )}

        {recentActivities.length > 0 && (
          <div className="mt-4 pt-4 border-t border-gray-200">
            <Button variant="ghost" size="sm" className="w-full">
              View All Activity
              <ExternalLink className="h-3 w-3 ml-2" />
            </Button>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
