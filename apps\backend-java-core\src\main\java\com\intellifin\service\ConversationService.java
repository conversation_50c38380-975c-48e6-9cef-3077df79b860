package com.intellifin.service;

import com.intellifin.dto.conversation.ConversationCommandRequest;
import com.intellifin.dto.conversation.ConversationResponse;
import com.intellifin.model.Conversation;
import com.intellifin.model.ConversationMessage;
import com.intellifin.model.User;
import com.intellifin.repository.ConversationMessageRepository;
import com.intellifin.repository.ConversationRepository;
import com.intellifin.repository.UserRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.messaging.simp.SimpMessagingTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;
import java.util.UUID;
import java.util.concurrent.CompletableFuture;

@Service
@RequiredArgsConstructor
@Slf4j
@Transactional
public class ConversationService {

    private final ConversationRepository conversationRepository;
    private final ConversationMessageRepository messageRepository;
    private final UserRepository userRepository;
    private final SimpMessagingTemplate messagingTemplate;
    private final AiCommandProcessingService aiCommandProcessingService;

    public ConversationResponse processCommand(ConversationCommandRequest request, UUID userId) {
        log.info("Processing command for user: {}, session: {}", userId, request.getSessionId());
        
        try {
            // Get or create conversation
            Conversation conversation = getOrCreateConversation(request, userId);
            
            // Create user message
            ConversationMessage userMessage = createUserMessage(conversation, request);
            userMessage = messageRepository.save(userMessage);
            
            // Send immediate acknowledgment
            ConversationResponse ackResponse = ConversationResponse.progress(
                userMessage.getId(),
                request.getSessionId(),
                conversation.getId(),
                "Processing your request..."
            );
            
            sendWebSocketMessage(userId, ackResponse);
            
            // Process command asynchronously
            processCommandAsync(userMessage, request, userId);
            
            return ackResponse;
            
        } catch (Exception e) {
            log.error("Error processing command for user: {}", userId, e);
            return ConversationResponse.error(
                null,
                request.getSessionId(),
                null,
                "Failed to process command: " + e.getMessage()
            );
        }
    }

    private Conversation getOrCreateConversation(ConversationCommandRequest request, UUID userId) {
        User user = userRepository.findById(userId)
                .orElseThrow(() -> new RuntimeException("User not found"));

        if (request.hasSessionId()) {
            Optional<Conversation> existing = conversationRepository.findActiveBySessionId(request.getSessionId());
            if (existing.isPresent()) {
                existing.get().updateActivity();
                return conversationRepository.save(existing.get());
            }
        }

        // Create new conversation
        String sessionId = request.hasSessionId() ? request.getSessionId() : generateSessionId();
        String title = generateConversationTitle(request.getCommand());
        
        Conversation conversation = Conversation.builder()
                .user(user)
                .sessionId(sessionId)
                .title(title)
                .status(Conversation.ConversationStatus.ACTIVE)
                .lastActivityAt(LocalDateTime.now())
                .build();

        return conversationRepository.save(conversation);
    }

    private ConversationMessage createUserMessage(Conversation conversation, ConversationCommandRequest request) {
        return ConversationMessage.builder()
                .conversation(conversation)
                .type(ConversationMessage.MessageType.COMMAND)
                .role(ConversationMessage.MessageRole.USER)
                .content(request.getCommand())
                .status(ConversationMessage.MessageStatus.PENDING)
                .build();
    }

    private void processCommandAsync(ConversationMessage userMessage, ConversationCommandRequest request, UUID userId) {
        CompletableFuture.runAsync(() -> {
            try {
                long startTime = System.currentTimeMillis();
                
                // Mark message as processing
                userMessage.markAsProcessing();
                messageRepository.save(userMessage);
                
                // Send processing update
                ConversationResponse processingResponse = ConversationResponse.progress(
                    userMessage.getId(),
                    request.getSessionId(),
                    userMessage.getConversation().getId(),
                    "Analyzing your request..."
                );
                sendWebSocketMessage(userId, processingResponse);
                
                // Process with AI service
                AiCommandProcessingService.CommandProcessingResult result = 
                    aiCommandProcessingService.processCommand(request.getCommand(), userMessage.getConversation());
                
                long processingTime = System.currentTimeMillis() - startTime;
                
                // Update user message with results
                userMessage.setProcessingResult(
                    result.getIntent(),
                    result.getConfidence(),
                    processingTime
                );
                userMessage.markAsCompleted();
                messageRepository.save(userMessage);
                
                // Create assistant response message
                ConversationMessage assistantMessage = ConversationMessage.builder()
                        .conversation(userMessage.getConversation())
                        .type(ConversationMessage.MessageType.RESPONSE)
                        .role(ConversationMessage.MessageRole.ASSISTANT)
                        .content(result.getResponse())
                        .intentRecognized(result.getIntent())
                        .confidenceScore(result.getConfidence())
                        .processingTimeMs(processingTime)
                        .status(ConversationMessage.MessageStatus.COMPLETED)
                        .build();
                
                assistantMessage = messageRepository.save(assistantMessage);
                
                // Send final response
                ConversationResponse finalResponse = ConversationResponse.success(
                    assistantMessage.getId(),
                    request.getSessionId(),
                    userMessage.getConversation().getId(),
                    result.getResponse(),
                    result.getIntent(),
                    result.getConfidence(),
                    processingTime
                );
                
                if (result.requiresFollowUp()) {
                    finalResponse.setRequiresFollowUp(true);
                    finalResponse.setFollowUpPrompt(result.getFollowUpPrompt());
                }
                
                sendWebSocketMessage(userId, finalResponse);
                
                log.info("Command processed successfully for user: {}, intent: {}, confidence: {}", 
                        userId, result.getIntent(), result.getConfidence());
                
            } catch (Exception e) {
                log.error("Error in async command processing for user: {}", userId, e);
                
                // Mark user message as failed
                userMessage.markAsFailed(e.getMessage());
                messageRepository.save(userMessage);
                
                // Send error response
                ConversationResponse errorResponse = ConversationResponse.error(
                    userMessage.getId(),
                    request.getSessionId(),
                    userMessage.getConversation().getId(),
                    "I apologize, but I encountered an error processing your request. Please try again."
                );
                
                sendWebSocketMessage(userId, errorResponse);
            }
        });
    }

    private void sendWebSocketMessage(UUID userId, ConversationResponse response) {
        try {
            messagingTemplate.convertAndSendToUser(
                userId.toString(),
                "/queue/conversation",
                response
            );
        } catch (Exception e) {
            log.error("Failed to send WebSocket message to user: {}", userId, e);
        }
    }

    public List<Conversation> getUserConversations(UUID userId) {
        User user = userRepository.findById(userId)
                .orElseThrow(() -> new RuntimeException("User not found"));
        
        return conversationRepository.findActiveByUser(user);
    }

    public Optional<Conversation> getConversation(UUID conversationId, UUID userId) {
        return conversationRepository.findById(conversationId)
                .filter(conv -> conv.getUser().getId().equals(userId));
    }

    public List<ConversationMessage> getConversationMessages(UUID conversationId, UUID userId) {
        Optional<Conversation> conversation = getConversation(conversationId, userId);
        if (conversation.isPresent()) {
            return messageRepository.findByConversationOrderByCreatedAtAsc(conversation.get());
        }
        return List.of();
    }

    public void archiveConversation(UUID conversationId, UUID userId) {
        Optional<Conversation> conversation = getConversation(conversationId, userId);
        if (conversation.isPresent()) {
            conversation.get().archive();
            conversationRepository.save(conversation.get());
        }
    }

    public void deleteConversation(UUID conversationId, UUID userId) {
        Optional<Conversation> conversation = getConversation(conversationId, userId);
        if (conversation.isPresent()) {
            conversation.get().delete();
            conversationRepository.save(conversation.get());
        }
    }

    private String generateSessionId() {
        return "session_" + UUID.randomUUID().toString().replace("-", "").substring(0, 16);
    }

    private String generateConversationTitle(String command) {
        // Simple title generation - could be enhanced with AI
        String title = command.length() > 50 ? command.substring(0, 47) + "..." : command;
        return title.replaceAll("[^a-zA-Z0-9\\s]", "").trim();
    }
}
