package com.intellifin.controller;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.intellifin.dto.auth.LoginRequest;
import com.intellifin.dto.auth.RegisterRequest;
import com.intellifin.model.User;
import com.intellifin.repository.UserRepository;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureWebMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.MediaType;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.TestPropertySource;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.transaction.annotation.Transactional;

import java.util.UUID;

import static org.hamcrest.Matchers.*;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@AutoConfigureWebMvc
@ActiveProfiles("test")
@TestPropertySource(properties = {
    "spring.datasource.url=jdbc:h2:mem:testdb",
    "spring.jpa.hibernate.ddl-auto=create-drop",
    "app.email.enabled=false"
})
@Transactional
class AuthControllerIntegrationTest {

    @Autowired
    private MockMvc mockMvc;

    @Autowired
    private ObjectMapper objectMapper;

    @Autowired
    private UserRepository userRepository;

    @Autowired
    private PasswordEncoder passwordEncoder;

    private RegisterRequest validRegisterRequest;
    private LoginRequest validLoginRequest;

    @BeforeEach
    void setUp() {
        userRepository.deleteAll();

        validRegisterRequest = RegisterRequest.builder()
                .email("<EMAIL>")
                .password("SecurePass123!")
                .confirmPassword("SecurePass123!")
                .firstName("Grace")
                .lastName("Phiri")
                .organizationName("Phiri Catering Services")
                .tpin("1234567890")
                .build();

        validLoginRequest = LoginRequest.builder()
                .email("<EMAIL>")
                .password("SecurePass123!")
                .rememberMe(false)
                .build();
    }

    @Test
    @DisplayName("POST /api/v1/auth/register - Should register user successfully")
    void shouldRegisterUserSuccessfully() throws Exception {
        mockMvc.perform(post("/api/v1/auth/register")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(validRegisterRequest)))
                .andExpect(status().isCreated())
                .andExpect(jsonPath("$.token", notNullValue()))
                .andExpect(jsonPath("$.tokenType", is("Bearer")))
                .andExpect(jsonPath("$.user.email", is("<EMAIL>")))
                .andExpect(jsonPath("$.user.firstName", is("Grace")))
                .andExpect(jsonPath("$.user.lastName", is("Phiri")))
                .andExpect(jsonPath("$.user.organizationName", is("Phiri Catering Services")))
                .andExpect(jsonPath("$.user.emailVerified", is(false)))
                .andExpect(jsonPath("$.user.onboardingStatus", is("STARTED")))
                .andExpect(jsonPath("$.message", containsString("Please check your email")));
    }

    @Test
    @DisplayName("POST /api/v1/auth/register - Should reject duplicate email")
    void shouldRejectDuplicateEmail() throws Exception {
        // Create existing user
        User existingUser = User.builder()
                .id(UUID.randomUUID())
                .email("<EMAIL>")
                .passwordHash(passwordEncoder.encode("password"))
                .firstName("Grace")
                .lastName("Phiri")
                .onboardingStatus(User.OnboardingStatus.STARTED)
                .emailVerified(false)
                .accountLocked(false)
                .failedLoginAttempts(0)
                .build();
        userRepository.save(existingUser);

        mockMvc.perform(post("/api/v1/auth/register")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(validRegisterRequest)))
                .andExpect(status().isBadRequest())
                .andExpect(jsonPath("$.message", containsString("User with this email already exists")));
    }

    @Test
    @DisplayName("POST /api/v1/auth/register - Should validate password confirmation")
    void shouldValidatePasswordConfirmation() throws Exception {
        validRegisterRequest.setConfirmPassword("DifferentPassword");

        mockMvc.perform(post("/api/v1/auth/register")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(validRegisterRequest)))
                .andExpect(status().isBadRequest())
                .andExpect(jsonPath("$.message", containsString("Password confirmation does not match")));
    }

    @Test
    @DisplayName("POST /api/v1/auth/register - Should validate email format")
    void shouldValidateEmailFormat() throws Exception {
        validRegisterRequest.setEmail("invalid-email");

        mockMvc.perform(post("/api/v1/auth/register")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(validRegisterRequest)))
                .andExpect(status().isBadRequest())
                .andExpect(jsonPath("$.details", hasSize(greaterThan(0))))
                .andExpect(jsonPath("$.details[*].field", hasItem("email")));
    }

    @Test
    @DisplayName("POST /api/v1/auth/register - Should validate password strength")
    void shouldValidatePasswordStrength() throws Exception {
        validRegisterRequest.setPassword("weak");
        validRegisterRequest.setConfirmPassword("weak");

        mockMvc.perform(post("/api/v1/auth/register")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(validRegisterRequest)))
                .andExpect(status().isBadRequest())
                .andExpect(jsonPath("$.details", hasSize(greaterThan(0))))
                .andExpect(jsonPath("$.details[*].field", hasItem("password")));
    }

    @Test
    @DisplayName("POST /api/v1/auth/login - Should login user successfully")
    void shouldLoginUserSuccessfully() throws Exception {
        // Create verified user
        User user = User.builder()
                .id(UUID.randomUUID())
                .email("<EMAIL>")
                .passwordHash(passwordEncoder.encode("SecurePass123!"))
                .firstName("Grace")
                .lastName("Phiri")
                .organizationName("Phiri Catering Services")
                .onboardingStatus(User.OnboardingStatus.STARTED)
                .emailVerified(true)
                .accountLocked(false)
                .failedLoginAttempts(0)
                .build();
        userRepository.save(user);

        mockMvc.perform(post("/api/v1/auth/login")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(validLoginRequest)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.token", notNullValue()))
                .andExpect(jsonPath("$.tokenType", is("Bearer")))
                .andExpect(jsonPath("$.user.email", is("<EMAIL>")))
                .andExpect(jsonPath("$.message", is("Authentication successful")));
    }

    @Test
    @DisplayName("POST /api/v1/auth/login - Should reject invalid credentials")
    void shouldRejectInvalidCredentials() throws Exception {
        // Create user with different password
        User user = User.builder()
                .id(UUID.randomUUID())
                .email("<EMAIL>")
                .passwordHash(passwordEncoder.encode("DifferentPassword"))
                .firstName("Grace")
                .lastName("Phiri")
                .onboardingStatus(User.OnboardingStatus.STARTED)
                .emailVerified(true)
                .accountLocked(false)
                .failedLoginAttempts(0)
                .build();
        userRepository.save(user);

        mockMvc.perform(post("/api/v1/auth/login")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(validLoginRequest)))
                .andExpect(status().isUnauthorized())
                .andExpect(jsonPath("$.message", containsString("Invalid email or password")));
    }

    @Test
    @DisplayName("POST /api/v1/auth/login - Should reject unverified user")
    void shouldRejectUnverifiedUser() throws Exception {
        // Create unverified user
        User user = User.builder()
                .id(UUID.randomUUID())
                .email("<EMAIL>")
                .passwordHash(passwordEncoder.encode("SecurePass123!"))
                .firstName("Grace")
                .lastName("Phiri")
                .onboardingStatus(User.OnboardingStatus.STARTED)
                .emailVerified(false) // Not verified
                .accountLocked(false)
                .failedLoginAttempts(0)
                .build();
        userRepository.save(user);

        mockMvc.perform(post("/api/v1/auth/login")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(validLoginRequest)))
                .andExpect(status().isUnauthorized())
                .andExpect(jsonPath("$.message", containsString("Account is disabled")));
    }

    @Test
    @DisplayName("POST /api/v1/auth/login - Should reject locked account")
    void shouldRejectLockedAccount() throws Exception {
        // Create locked user
        User user = User.builder()
                .id(UUID.randomUUID())
                .email("<EMAIL>")
                .passwordHash(passwordEncoder.encode("SecurePass123!"))
                .firstName("Grace")
                .lastName("Phiri")
                .onboardingStatus(User.OnboardingStatus.STARTED)
                .emailVerified(true)
                .accountLocked(true) // Locked
                .failedLoginAttempts(5)
                .build();
        userRepository.save(user);

        mockMvc.perform(post("/api/v1/auth/login")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(validLoginRequest)))
                .andExpect(status().isUnauthorized())
                .andExpect(jsonPath("$.message", containsString("Account is locked")));
    }

    @Test
    @DisplayName("POST /api/v1/auth/forgot-password - Should handle password reset request")
    void shouldHandlePasswordResetRequest() throws Exception {
        mockMvc.perform(post("/api/v1/auth/forgot-password")
                .param("email", "<EMAIL>"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.message", containsString("password reset link has been sent")))
                .andExpect(jsonPath("$.status", is("success")));
    }

    @Test
    @DisplayName("GET /api/v1/auth/validate-token - Should validate valid token")
    void shouldValidateValidToken() throws Exception {
        // First register and get token
        String response = mockMvc.perform(post("/api/v1/auth/register")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(validRegisterRequest)))
                .andExpect(status().isCreated())
                .andReturn()
                .getResponse()
                .getContentAsString();

        String token = objectMapper.readTree(response).get("token").asText();

        // Then validate token
        mockMvc.perform(get("/api/v1/auth/validate-token")
                .header("Authorization", "Bearer " + token))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.valid", is(true)))
                .andExpect(jsonPath("$.message", is("Token is valid")));
    }

    @Test
    @DisplayName("GET /api/v1/auth/validate-token - Should reject invalid token")
    void shouldRejectInvalidToken() throws Exception {
        mockMvc.perform(get("/api/v1/auth/validate-token")
                .header("Authorization", "Bearer invalid-token"))
                .andExpect(status().isUnauthorized());
    }
}
