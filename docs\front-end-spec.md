# IntelliFin UI/UX Specification

## 1. Introduction

This document defines the user experience goals, information architecture, user flows, and visual design specifications for IntelliFin's user interface. It serves as the foundation for visual design and frontend development, ensuring a cohesive and user-centered experience.

### 1.1 Overall UX Goals & Principles

#### 1.1.1 Target User Personas

*   **<PERSON> (The Growing Service Business):** A 38-year-old caterer/event planner in Lusaka. Pragmatic smartphone user, fluent in mobile money and WhatsApp. Struggles with financial visibility, time-consuming manual reconciliation, and fear of ZRA compliance. Seeks to reclaim time, formalize her business, and access loans for growth.
*   **<PERSON> (The Busy Shop Owner):** A 28-year-old electronics shop owner in Lusaka. Highly tech-aware, constantly on his smartphone. Struggles with product profitability, supplier debt juggling, and hidden costs. Seeks instant stock levels, product profitability insights, and lines of credit.

#### 1.1.2 Usability Goals

*   **Ease of Learning:** New users must quickly grasp the conversational interface and core interactions.
*   **Efficiency of Use:** Experienced users should be able to complete tasks (e.g., categorizing transactions, drafting invoices) rapidly with minimal friction.
*   **Error Prevention & Recovery:** The system should guide users to prevent mistakes and provide clear, helpful pathways for recovery when errors occur (especially important for financial transactions).
*   **Trust & Control:** Users must feel in absolute control of their financial data and actions, with transparent processes and clear confirmation steps for sensitive operations.
*   **Accessibility:** The interface should be usable by individuals with diverse abilities, adhering to WCAG AA standards.

#### 1.1.3 Design Principles

1.  **Conversational First:** The primary interaction model is natural language dialogue; the UI adapts to and facilitates this conversation.
2.  **Clarity over Cleverness:** Prioritize clear, unambiguous communication and simple workflows over intricate or overly innovative aesthetics.
3.  **Action-Oriented Feedback:** Every user command should result in a clear, immediate, and actionable response or UI update.
4.  **Progressive Disclosure:** Present information and options only when relevant to the user's current context or command, avoiding cognitive overload.
5.  **Built-in Trust:** Transparency, auditable actions, and explicit "Confirm & Commit" steps are integrated into every sensitive workflow to build and maintain user confidence.
6.  **Human-Centered Automation:** Automation (e.g., AI categorization) should feel like a helpful assistant, not a black box, with easy ways for users to review and correct.
7.  **Responsive & Mobile-Optimized:** Design must flawlessly adapt to various screen sizes, prioritizing a seamless mobile experience.

### 1.2 Change Log

| Date       | Version | Description        | Author     |
| :--------- | :------ | :----------------- | :--------- |
| 2024-07-30 | 1.0     | Initial draft      | Sally, UX Expert |

---

## 2. Information Architecture (IA)

This section defines how the content and functionality of IntelliFin will be organized, structured, and presented to the user.

### 2.1 Site Map / Screen Inventory

This diagram illustrates the main conceptual views or contexts that a user will encounter and interact with in IntelliFin. These are primarily dynamic views presented within the central conversational workspace, rather than distinct, navigable web pages.

```mermaid
graph TD
    A[User (Entry Point)] --> B(Login / Signup)
    B --> C(Main Conversational Workspace)

    C --> C1[Display: Financial Summary]
    C --> C2[View: Transactions (Review/Categorize)]
    C --> C3[View: Invoice Draft / Approval]
    C --> C4[Settings / Account Connection]

    C1 --> |Triggered by command like "show profit"| C
    C2 --> |Triggered by command or system alert| C
    C3 --> |Triggered by command like "create invoice"| C
    C4 --> |Triggered by command or settings icon| C

    C2 --- |Associated Data| D[Transaction Data]
    C3 --- |Associated Data| E[Invoice Data & Client Data]
    C4 --- |Associated Data| F[User Profile & Financial Account Data]

    style C fill:#ADD8E6,stroke:#333,stroke-width:2px
    style C1 fill:#FFE4B5,stroke:#333,stroke-width:2px
    style C2 fill:#FFE4B5,stroke:#333,stroke-width:2px
    style C3 fill:#FFE4B5,stroke:#333,stroke-width:2px
    style C4 fill:#FFE4B5,stroke:#333,stroke-width:2px
    style D fill:#90EE90,stroke:#333,stroke-width:2px
    style E fill:#90EE90,stroke:#333,stroke-width:2px
    style F fill:#90EE90,stroke:#333,stroke-width:2px
```

### 2.2 Navigation Structure

Given our conversational-first approach, traditional "navigation" is minimized. The primary method of moving between contexts will be through user commands.

*   **Primary Navigation:** The **Conversational Command Bar** serves as the central "navigation" element. Users articulate their intent in natural language, and the system dynamically shifts the main workspace view or presents relevant information.
*   **Secondary Navigation:** A minimal set of persistently visible UI elements (e.g., a "hamburger menu" icon or a small bottom navigation bar on mobile) may offer direct access to static sections like "Settings," "Help," or "Logout," bypassing the conversational flow for common utility functions.
*   **Breadcrumb Strategy:** Not applicable in the traditional sense due to the conversational, dynamic nature. Context will be implied by the current command and the displayed view. However, for deeper views (e.g., viewing details of a specific transaction within the "Transactions" view), a simple "back" action or clear heading may indicate the current context.

---

## 3. User Flows

This section maps out the step-by-step interactions for critical user tasks within IntelliFin, illustrating how users achieve their goals. For this pivotal flow, we will detail both the user's experience and the underlying system orchestration.

### 3.1 Creating a ZRA-Compliant Smart Invoice via Conversation

*   **User Goal:** Grace's goal is to quickly and confidently generate a professional, ZRA-compliant Smart Invoice for a client without getting bogged down in complex forms or worrying about compliance errors. She wants to fulfill this critical business requirement effortlessly, so she can secure payment from her formal clients and focus on running her business.
*   **Entry Points:** The user initiates this flow exclusively from the Main Conversational Workspace by typing a natural language command into the command bar. The system must be flexible enough to understand various levels of detail, such as:
    *   A simple, open-ended command: "Create a ZRA invoice."
    *   A command with key entities: "Draft an invoice for Phiri Corp for K5000."
    *   A more detailed, conversational command: "I need to send an invoice to Banda Logistics for the delivery service, it's K2500 and it's due in 14 days."
*   **Success Criteria:** The flow is successfully completed when both the user-facing and system-level criteria are met.
    *   **User-Facing Success Criteria:**
        *   The user receives a clear, unambiguous confirmation message within the IntelliFin interface stating that the invoice was successfully submitted to ZRA.
        *   The confirmation includes the official ZRA Invoice ID or reference number.
        *   The user can easily view the finalized invoice within their IntelliFin account.
        *   The user has the option to download a professional-looking PDF of the invoice or send it directly to their client via email/WhatsApp.
        *   The entire process, from command to confirmation, feels swift, transparent, and instills a sense of confidence and relief.
    *   **System-Level Success Criteria:**
        *   The AI correctly parses the user's intent ("CREATE_INVOICE") and extracts all relevant entities (client name, amount, due date, etc.).
        *   If information is missing, the system successfully prompts the user for clarification and captures the required data.
        *   The backend successfully creates and persists a new `Invoice` and associated `InvoiceLineItem` records in the database with a status of `DRAFT`.
        *   After user confirmation, the `Invoice` status is updated to `PENDING_SUBMISSION`.
        *   The system successfully formats the invoice data according to ZRA specifications and submits it via the internal ZRA VSDC component.
        *   Upon a successful response from the ZRA API, the `Invoice` record is updated with `zraStatus: SUBMITTED` and the `zraInvoiceId`.

#### 3.1.1 Flow Diagram

```mermaid
graph LR
    subgraph User Experience Flow
        A[User: Enter Command] --> B{UI: Main Conversational Workspace (Command Bar)}
        B -->|Sends text via WebSocket| C(System: IntelliFin Backend Orchestrator)

        C -- WebSocket Push --> D{UI: AI Prompts for Missing Info?}
        D -->|Yes| E[User: Provides Missing Details]
        E -->|Sends text via WebSocket| C

        D -->|No| F{UI: Displays Draft Invoice for Review}
        F -->|User: Reviews & Confirms (via UI Action)| G(System: Orchestrator Receives Confirmation)
        G -- WebSocket Push --> H[UI: Displays ZRA Submission Confirmation]
        H --> I[User: View/Download Invoice]
    end

    subgraph System Orchestration Flow
        C --> J[Orchestrator: Pass to AI Service]
        J --> K(AI Service: NLU & Entity Extraction - Gemini/LangChain4j)
        K --> L{AI Service: Returns Intent + Entities}
        L --> M{Orchestrator: All required Invoice entities present?}

        M -->|No / Missing| N[Orchestrator: Generate Clarification Prompt]
        N --> C; style N fill:#FFC107,stroke:#333,stroke-width:2px;

        M -->|Yes| O[Orchestrator: Call Internal REST APIs to create DRAFT Invoice (e.g., POST /invoices)]
        O --> P[DB: Persist Invoice (DRAFT)]
        P --> Q[Orchestrator: Push DRAFT Invoice to UI via WebSocket]
        Q --> F; style Q fill:#17A2B8,stroke:#333,stroke-width:2px;

        G --> R[Orchestrator: Update Invoice Status to PENDING_SUBMISSION]
        R --> S[DB: Update Invoice Status]
        S --> T[Orchestrator: Call Internal ZRA Compliance Service API]
        T --> U(ZRA Compliance Service: Format data & Call External ZRA VSDC API)

        U -->|ZRA API Success| V[ZRA Compliance Service: Update Invoice with zraStatus:SUBMITTED, zraInvoiceId]
        V --> W[DB: Update Invoice]
        W --> X[Orchestrator: Push Success Confirmation to UI via WebSocket]
        X --> H; style X fill:#28A745,stroke:#333,stroke-width:2px;

        U -->|ZRA API Failure| Y[ZRA Compliance Service: Update Invoice with zraStatus:FAILED, Error Details]
        Y --> Z[DB: Update Invoice]
        Z --> AA[Orchestrator: Push Error Message to UI via WebSocket]
        AA --> H; style AA fill:#DC3545,stroke:#333,stroke-width:2px;

    end

    linkStyle 0 stroke-width:2px,fill:none,stroke:black;
    linkStyle 1 stroke-width:2px,fill:none,stroke:black;
    linkStyle 2 stroke-width:2px,fill:none,stroke:black;
    linkStyle 3 stroke-width:2px,fill:none,stroke:black;
    linkStyle 4 stroke-width:2px,fill:none,stroke:black;
    linkStyle 5 stroke-width:2px,fill:none,stroke:black;
    linkStyle 6 stroke-width:2px,fill:none,stroke:black;
    linkStyle 7 stroke-width:2px,fill:none,stroke:black;
    linkStyle 8 stroke-width:2px,fill:none,stroke:black;
    linkStyle 9 stroke-width:2px,fill:none,stroke:black;
    linkStyle 10 stroke-width:2px,fill:none,stroke:black;
    linkStyle 11 stroke-width:2px,fill:none,stroke:black;
    linkStyle 12 stroke-width:2px,fill:none,stroke:black;
    linkStyle 13 stroke-width:2px,fill:none,stroke:black;
    linkStyle 14 stroke-width:2px,fill:none,stroke:black;
    linkStyle 15 stroke-width:2px,fill:none,stroke:black;
    linkStyle 16 stroke-width:2px,fill:none,stroke:black;
    linkStyle 17 stroke-width:2px,fill:none,stroke:black;
    linkStyle 18 stroke-width:2px,fill:none,stroke:black;
    linkStyle 19 stroke-width:2px,fill:none,stroke:black;
    linkStyle 20 stroke-width:2px,fill:none,stroke:black;
    linkStyle 21 stroke-width:2px,fill:none,stroke:black;
    linkStyle 22 stroke-width:2px,fill:none,stroke:black;

    style A fill:#ADD8E6,stroke:#333,stroke-width:2px
    style B fill:#ADD8E6,stroke:#333,stroke-width:2px
    style C fill:#FFE4B5,stroke:#333,stroke-width:2px
    style D fill:#ADD8E6,stroke:#333,stroke-width:2px
    style E fill:#ADD8E6,stroke:#333,stroke-width:2px
    style F fill:#ADD8E6,stroke:#333,stroke-width:2px
    style G fill:#FFE4B5,stroke:#333,stroke-width:2px
    style H fill:#ADD8E6,stroke:#333,stroke-width:2px
    style I fill:#ADD8E6,stroke:#333,stroke-width:2px

    style J fill:#FFE4B5,stroke:#333,stroke-width:2px
    style K fill:#F0E68C,stroke:#333,stroke-width:2px
    style L fill:#F0E68C,stroke:#333,stroke-width:2px
    style M fill:#FFE4B5,stroke:#333,stroke-width:2px
    style N fill:#FFE4B5,stroke:#333,stroke-width:2px
    style O fill:#FFE4B5,stroke:#333,stroke-width:2px
    style P fill:#90EE90,stroke:#333,stroke-width:2px
    style Q fill:#17A2B8,stroke:#333,stroke-width:2px
    style R fill:#FFE4B5,stroke:#333,stroke-width:2px
    style S fill:#90EE90,stroke:#333,stroke-width:2px
    style T fill:#FFE4B5,stroke:#333,stroke-width:2px
    style U fill:#F0E68C,stroke:#333,stroke-width:2px
    style V fill:#F0E68C,stroke:#333,stroke-width:2px
    style W fill:#90EE90,stroke:#333,stroke-width:2px
    style X fill:#28A745,stroke:#333,stroke-width:2px
    style Y fill:#F0E68C,stroke:#333,stroke-width:2px
    style Z fill:#90EE90,stroke:#333,stroke-width:2px
    style AA fill:#DC3545,stroke:#333,stroke-width:2px
```

#### 3.1.2 Edge Cases & Error Handling:

*   **Missing Information:** If the initial natural language command lacks critical entities (e.g., client name, amount), the AI Orchestrator will identify the gaps and dynamically prompt the user for the missing details via the conversational interface.
*   **Invalid Input:** If the user provides invalid data (e.g., non-numeric amount), the system will use the AI to provide helpful, context-specific error messages and guide the user to correct their input.
*   **Client Not Found:** If the AI attempts to identify a client (e.g., "Phiri Corp") but no matching record exists, the system will prompt the user to either create a new client record or select from a list of existing ones.
*   **Internal API Failure:** If an internal REST API call (e.g., `POST /invoices`) fails, the orchestrator will catch the error, log it, and provide a user-friendly message to the frontend, preventing the user from seeing technical errors.
*   **External ZRA API Failure:** If the ZRA VSDC API returns an error (e.g., invalid TPIN, service unavailable), the ZRA Compliance Service will handle the error, update the `Invoice` `zraStatus` to `FAILED` (with error details), and push an informative error message back to the user via WebSocket. The user will be advised on next steps (e.g., "Please check the client's TPIN and try again").
*   **Confirmation Timeout/Cancellation:** If the user does not confirm the draft invoice within a reasonable time or explicitly cancels, the draft invoice will remain in `DRAFT` status and will not be submitted to ZRA.

---

## 4. Wireframes & Mockups

This section outlines the approach to visual design and how wireframes and mockups will be utilized to articulate the user interface.

#### 4.1 Design Files

*   **Primary Design Tool:** Figma
*   **Design Files Link:** `(To be provided upon creation of Figma project)`

#### 4.2 Key Screen Layouts

Given IntelliFin's conversational interface, traditional static "screens" are less emphasized. Instead, we will focus on defining the layouts and visual treatment of **dynamic "views" and interactive components** that appear within the main conversational workspace. These layouts will be high-fidelity mockups created directly in Figma, incorporating our chosen Shadcn/ui components.

*   **Main Conversational Workspace Layout:**
    *   **Purpose:** To define the overall structure of the central interaction area, including the persistent command bar, the dynamic content display area, and any persistent utility elements (e.g., settings icon, notifications).
    *   **Key Elements:** Chat history display, input command bar (always visible), dynamic content area for responses (e.g., financial summaries, invoice drafts), subtle contextual hints/suggestions.
    *   **Interaction Notes:** Emphasize fluid scrolling, smooth transitions for dynamic content, and clear visual hierarchy for AI responses vs. user input.

*   **Transaction Review View:**
    *   **Purpose:** Layout for displaying a list of newly fetched or pending transactions, allowing users to confirm or re-categorize.
    *   **Key Elements:** Transaction details (date, amount, description), AI-suggested category, "Confirm" button, "Edit Category" action, visual indicators for "needs review."
    *   **Interaction Notes:** Easy swiping/scrolling through transactions, clear visual state for confirmed vs. unconfirmed, intuitive category selection.

*   **Invoice Draft/Approval View:**
    *   **Purpose:** Layout for presenting a draft ZRA Smart Invoice for user review and final approval.
    *   **Key Elements:** Invoice header details (client, date, number), line items table, calculated totals (subtotal, tax, grand total), clear call-to-action for "Approve & Submit to ZRA," "Edit" or "Cancel" options.
    *   **Interaction Notes:** Clear visual separation of editable vs. non-editable fields, prominent "Confirm & Commit" button, confirmation modal for high-stakes actions.

*   **Financial Summary Display View:**
    *   **Purpose:** Layout for presenting requested financial summaries (e.g., Profit/Loss, Sales overview) within the conversational flow.
    *   **Key Elements:** Clear headings, period selection (if dynamic), key metrics (sales, expenses, profit/loss), clean numerical display, subtle charting if simple.
    *   **Interaction Notes:** Concise presentation, easy readability, quick load times.

---

## 5. Component Library / Design System

This section defines our approach to managing reusable UI components and maintaining visual consistency across the IntelliFin application.

#### 5.1 Design System Approach

**Approach:** We will adopt a "component recipe" approach using **Shadcn/ui** as our foundation.

**Rationale:** Shadcn/ui is not a traditional npm package dependency; instead, it provides ready-to-use, accessible, and customizable React components built with Radix UI primitives and styled with Tailwind CSS. We will copy and paste these components directly into our codebase. This approach offers:
*   **Full Control:** We own every line of code, allowing for deep customization without ejecting from a library.
*   **Reduced Bundle Size:** Only the components we actually use are included.
*   **Flexibility:** Easily modify components to perfectly match IntelliFin's unique branding and interaction needs.
*   **Accessibility:** Built-in accessibility features from Radix UI.
*   **Developer Experience:** Familiarity with React, Tailwind CSS, and Radix UI.

Our internal design system will evolve as a collection of these customized Shadcn/ui components, along with our defined styling tokens (colors, typography, spacing).

**Visual Treatment Guidelines:** As we customize these components, our guiding principle is to ensure they support the "calm, clear, and conversational" feel of the application. The styling should be minimal and professional, always prioritizing clarity and reducing cognitive load for the user, reinforcing the idea that this is a tool that brings relief, not complexity.

#### 5.2 Core Components

We will prioritize customizing and building out the following core components from Shadcn/ui to establish IntelliFin's unique look and feel early on. These will form the foundation for consistency across all dynamic views.

*   **Button:**
    *   **Purpose:** Primary action triggers (e.g., "Confirm," "Submit," "Connect Account").
    *   **Variants:** Primary (filled), Secondary (outline), Ghost, Link, Destructive (for sensitive actions).
    *   **States:** Default, Hover, Focus, Active, Disabled, Loading.
    *   **Usage Guidelines:** Clear hierarchy of buttons, consistent sizing, and prominent "Confirm & Commit" styling.
*   **Input Field (Text/Number):}
    *   **Purpose:** For text-based input in the command bar and any dynamic forms (e.g., client name, invoice amount).
    *   **Variants:** Standard, With adornments (e.g., currency symbol, search icon).
    *   **States:** Default, Focused, Disabled, Error, Read-only.
    *   **Usage Guidelines:** Clear labels, inline validation feedback, focus accessibility.
*   **Dialog/Modal:**
    *   **Purpose:** For critical confirmation steps (e.g., "Confirm & Commit" for ZRA invoice submission) or detailed information views that require user focus.
    *   **Variants:** Alert dialog, Standard dialog, Full-screen modal (if needed for complex flows).
    *   **States:** Open, Closed, Loading (content).
    *   **Usage Guidelines:** Clear titles and calls to action, simple interaction, accessible trapping of focus within the modal.
*   **Dropdown Menu/Select:**
    *   **Purpose:** For selecting from predefined options (e.g., re-categorizing a transaction, selecting a period for a financial report).
    *   **Variants:** Standard select, Command Palette-like interface (for AI-driven selections).
    *   **States:** Default, Open, Selected, Disabled.
    *   **Usage Guidelines:** Clear visual affordance for selection, accessible keyboard navigation.
*   **Data Display (Tables/Lists):**
    *   **Purpose:** For presenting structured data like transaction lists, invoice line items, or financial summaries.
    *   **Variants:** Simple list, Data table (for larger datasets).
    *   **States:** Empty state, Loading state.
    *   **Usage Guidelines:** Readability on small screens, clear headers, easy scannability, responsive columns.
*   **Toast/Notification:**
    *   **Purpose:** For transient, non-disruptive feedback (e.g., "Invoice submitted successfully," "Transaction synced").
    *   **Variants:** Success, Error, Warning, Info.
    *   **States:** Visible, Hidden (auto-dismiss or user dismiss).
    *   **Usage Guidelines:** Concise messaging, clear iconography, unobtrusive placement.

---

## 6. Branding & Style Guide

This section defines the core visual identity elements for IntelliFin, ensuring a consistent, professional, and user-friendly aesthetic across the application.

#### 6.1 Visual Identity

*   **Brand Guidelines:** IntelliFin's visual identity should convey trustworthiness, intelligence, simplicity, and modernity. The design should feel approachable and professional, consistently reinforcing the tagline "Accounting, as easy as a conversation." Visuals should be clean, intuitive, and minimize cognitive load, embodying a sense of relief and clarity for the user.

#### 6.2 Color Palette

| Color Type                  | Hex Code   | Usage                                                                                                            |
| :-------------------------- | :--------- | :--------------------------------------------------------------------------------------------------------------- |
| **Primary**                 | `#007BFF`  | Main interactive elements, primary branding accents, key UI elements (inspired by the vibrant blue arc).          |
| **Secondary**               | `#28A745`  | Complementary elements, secondary actions, background accents (inspired by the vibrant green arc).                |
| **Accent (AI/Sparkle)**     | `#00E0FF`  | Subtle highlights, AI-generated elements, dynamic indicators (inspired by the light blue sparkle).                |
| **Accent (Highlight)**      | `#FFB74D`  | Small emphasis, interactive states, dot over 'i' (inspired by the orange dot).                                     |
| **Success**                 | `#28A745`  | Positive feedback, confirmations, successful operations (re-using the vibrant green for clear positive association). |
| **Warning**                 | `#FFC107`  | Cautions, important notices, areas needing attention.                                                             |
| **Error**                   | `#DC3545`  | Errors, destructive actions, critical alerts.                                                                     |
| **Neutral (Dark Text/Surface)** | `#2A4E8D` | Primary text, strong backgrounds, headers (inspired by the dark blue "IntelliFin" text).                         |
| **Neutral (Light Background/Text)** | `#F8F9FA` | Light backgrounds, subtle borders, secondary text, disabled states.                                              |

#### 6.3 Typography

This section defines the typographic system for IntelliFin, ensuring readability, consistent visual hierarchy, and alignment with the overall brand identity.

##### 6.3.1 Font Families

*   **Primary:** 'Inter', sans-serif - A highly readable and versatile modern sans-serif typeface, optimized for screens.
*   **Secondary:** 'Inter', sans-serif - Using the same primary font for consistency and to minimize font loading overhead, differentiating through weight/size.
*   **Monospace:** 'JetBrains Mono', monospace - For code snippets, unique identifiers, or technical displays within the interface.

##### 6.3.2 Type Scale

| Element        | Size (px) | Weight      | Line Height (ratio) | Usage                                     |
| :------------- | :-------- | :---------- | :------------------ | :---------------------------------------- |
| **H1**         | 48        | 700 (Bold)  | 1.2                 | Page titles, major section headings       |
| **H2**         | 36        | 600 (Semi-bold) | 1.3                 | Main section headings                     |
| **H3**         | 28        | 600 (Semi-bold) | 1.4                 | Sub-section headings, key data labels     |
| **Body (Large)** | 18        | 400 (Regular) | 1.6                 | Prominent body text, conversational responses |
| **Body (Medium)** | 16        | 400 (Regular) | 1.5                 | Standard body text, paragraph text        |
| **Body (Small)** | 14        | 400 (Regular) | 1.4                 | Fine print, captions, footnotes           |
| **Button Text** | 16        | 500 (Medium) | 1.5                 | Call-to-action text                       |
| **Input Text** | 16        | 400 (Regular) | 1.5                 | Text within form fields, command bar      |

#### 6.4 Iconography

This section defines the iconographic system for IntelliFin, ensuring visual consistency and intuitive representation of actions and information.

*   **Icon Library:** We will utilize **Lucide React** (for the Next.js frontend).
    *   **Rationale:** Lucide is a modern, open-source icon library that provides a comprehensive set of clean, simple, and highly customizable SVG icons. It offers excellent tree-shaking capabilities, ensuring minimal bundle size, and its design philosophy aligns with our "clarity over cleverness" principle and Shadcn/ui's aesthetic. Being React components makes integration seamless.

*   **Usage Guidelines:**
    *   **Clarity:** Icons must be immediately understandable and support the primary action or information they represent.
    *   **Consistency:** Use icons from the selected library exclusively. Maintain consistent sizing and stroke weight.
    *   **Minimalism:** Avoid overly detailed or decorative icons that might clutter the interface.
    *   **Context:** Icons should always be used with accompanying text labels when their meaning might be ambiguous, especially for critical actions.
    *   **Accessibility:** Ensure icons have appropriate `aria-labels` or alternative text for screen readers.

#### 6.5 Spacing & Layout

This section defines the principles and rules for spacing elements and structuring layouts within the IntelliFin application, ensuring visual consistency and responsiveness across all devices.

*   **Grid System:** We will employ a **12-column flexible grid system** for primary page layouts, particularly on wider screens (tablets and desktops).
    *   **Rationale:** Provides robust structure for content alignment, enables consistent responsiveness, and simplifies content distribution across various screen sizes.

*   **Spacing Scale:** We will use a **consistent 8-pixel base unit** for all spacing (margins, paddings, gaps between elements). Multiples of 8px (e.g., 8px, 16px, 24px, 32px, 48px, etc.) will be used to define a predictable and harmonious visual rhythm.
    *   **Rationale:** The 8-pixel grid system is widely adopted, simplifies spacing decisions, ensures vertical and horizontal alignment, and contributes to overall visual balance and clarity. Smaller increments (e.g., 4px) may be used for very fine adjustments within components.

---

## 7. Accessibility Requirements

This section outlines the specific requirements to ensure IntelliFin is accessible and usable by individuals with diverse abilities, adhering to recognized industry standards.

#### 7.1 Compliance Target

*   **Standard:** Web Content Accessibility Guidelines (WCAG) 2.1 Level AA

#### 7.2 Key Requirements

Meeting WCAG 2.1 Level AA requires adherence to a comprehensive set of guidelines across four core principles: Perceivable, Operable, Understandable, and Robust. Key requirements for IntelliFin include:

*   **Perceivable:**
    *   **Text Alternatives:** Provide text alternatives for all non-text content (e.g., `alt` text for images, `aria-labels` for icons) so it can be changed into other forms people need, such as large print, braille, speech, symbols, or simpler language.
    *   **Captions & Other Alternatives:** (If any audio/video content is introduced in future phases) Provide synchronized captions and alternative formats for multimedia.
    *   **Adaptable:** Content should be structured so that it can be presented in different ways without losing information or structure (e.g., a simple linear reading order for screen readers, content reflow on zoom).
    *   **Distinguishable:** Ensure sufficient color contrast for all text and important UI elements (minimum contrast ratio of 4.5:1 for normal text). Avoid using color alone to convey information.
*   **Operable:**
    *   **Keyboard Accessible:** All functionality must be operable through a keyboard interface without requiring specific timings for individual keystrokes. This includes custom components from Shadcn/ui.
    *   **Enough Time:** Provide users with enough time to read and use content (e.g., configurable session timeouts, ability to extend time limits for tasks).
    *   **Seizures & Physical Reactions:** Avoid designing content in a way that is known to cause seizures (e.g., no flashing content more than three times a second).
    *   **Navigable:** Provide mechanisms to help users navigate, find content, and determine where they are (e.g., clear headings, logical tab order, skip links for repetitive content).
    *   **Input Modalities:** Ensure user interface components (e.g., buttons, form elements) are clear, large enough to be easily activated, and provide appropriate feedback for various input methods.
*   **Understandable:**
    *   **Readable:** Make text content readable and understandable (e.g., use clear and concise language, avoid jargon where possible, consistent fonts and sizing from our Typography guide).
    *   **Predictable:** Make web pages appear and operate in predictable ways. Navigation and interactive components should behave consistently.
    *   **Input Assistance:** Help users avoid and correct mistakes (e.g., clear instructions, input validation, error identification, and suggestions for correction). The AI's explainability feature supports this.
*   **Robust:**
    *   **Compatible:** Maximize compatibility with current and future user agents, including assistive technologies. Use proper semantic HTML and ARIA attributes where necessary for custom components (like those from Shadcn/ui built on Radix UI). 