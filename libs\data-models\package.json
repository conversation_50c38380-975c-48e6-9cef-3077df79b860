{"name": "@intellifin/data-models", "version": "1.0.0", "description": "Shared TypeScript interfaces and data models for IntelliFin platform", "main": "dist/index.js", "types": "dist/index.d.ts", "scripts": {"build": "tsc", "build:watch": "tsc --watch", "clean": "rm -rf dist", "lint": "eslint src --ext .ts", "lint:fix": "eslint src --ext .ts --fix", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage"}, "files": ["dist", "src"], "dependencies": {"zod": "^3.22.4"}, "devDependencies": {"@types/node": "^20.8.0", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "eslint": "^8.50.0", "jest": "^29.7.0", "@types/jest": "^29.5.0", "ts-jest": "^29.1.0", "typescript": "^5.2.0"}, "peerDependencies": {"typescript": "^5.0.0"}, "publishConfig": {"access": "restricted"}, "repository": {"type": "git", "url": "https://github.com/intellifin/intellifin.git", "directory": "libs/data-models"}, "keywords": ["intellifin", "typescript", "data-models", "interfaces"], "author": "IntelliFin Team", "license": "MIT"}