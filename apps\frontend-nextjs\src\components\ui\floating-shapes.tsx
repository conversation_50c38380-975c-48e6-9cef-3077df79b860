"use client"

import React from "react"
import { motion } from "framer-motion"

export function FloatingShapes() {
  const shapes = [
    { id: 1, size: 80, x: "10%", y: "20%", delay: 0, color: "bg-white/10" },
    { id: 2, size: 60, x: "85%", y: "15%", delay: 1, color: "bg-accent/20" },
    { id: 3, size: 100, x: "15%", y: "70%", delay: 2, color: "bg-white/10" },
    { id: 4, size: 70, x: "80%", y: "60%", delay: 0.5, color: "bg-secondary/20" },
    { id: 5, size: 90, x: "50%", y: "80%", delay: 1.5, color: "bg-white/10" },
    { id: 6, size: 50, x: "90%", y: "85%", delay: 2.5, color: "bg-highlight/20" },
  ]

  return (
    <div className="absolute inset-0 overflow-hidden pointer-events-none">
      {shapes.map((shape) => (
        <motion.div
          key={shape.id}
          className={`absolute rounded-2xl ${shape.color} backdrop-blur-sm`}
          style={{
            width: shape.size,
            height: shape.size,
            left: shape.x,
            top: shape.y,
          }}
          initial={{ opacity: 0, scale: 0, rotate: 0 }}
          animate={{
            opacity: [0.3, 0.6, 0.3],
            scale: [1, 1.1, 1],
            rotate: [0, 180, 360]
          }}
          transition={{
            duration: 8,
            delay: shape.delay,
            repeat: Infinity,
            ease: "easeInOut",
          }}
        />
      ))}
    </div>
  )
}
