-- Create conversation tables for Story 2.1
-- This script adds the conversational interface database schema

-- Create conversations table
CREATE TABLE IF NOT EXISTS conversations (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    title VARCHAR(255) NOT NULL,
    session_id VARCHAR(255) NOT NULL UNIQUE,
    status VARCHAR(50) NOT NULL DEFAULT 'ACTIVE',
    last_activity_at TIMESTAMP WITH TIME ZONE,
    context JSONB,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create conversation_messages table
CREATE TABLE IF NOT EXISTS conversation_messages (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    conversation_id UUID NOT NULL REFERENCES conversations(id) ON DELETE CASCADE,
    type VARCHAR(50) NOT NULL,
    role VARCHAR(50) NOT NULL,
    content TEXT NOT NULL,
    processed_content JSONB,
    intent_recognized VARCHAR(255),
    confidence_score DECIMAL(5,4),
    processing_time_ms BIGINT,
    status VARCHAR(50) NOT NULL DEFAULT 'PENDING',
    error_message TEXT,
    metadata JSONB,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_conversations_user_id ON conversations(user_id);
CREATE INDEX IF NOT EXISTS idx_conversations_session_id ON conversations(session_id);
CREATE INDEX IF NOT EXISTS idx_conversations_status ON conversations(status);
CREATE INDEX IF NOT EXISTS idx_conversations_last_activity ON conversations(last_activity_at);
CREATE INDEX IF NOT EXISTS idx_conversations_user_status ON conversations(user_id, status);

CREATE INDEX IF NOT EXISTS idx_conversation_messages_conversation_id ON conversation_messages(conversation_id);
CREATE INDEX IF NOT EXISTS idx_conversation_messages_type ON conversation_messages(type);
CREATE INDEX IF NOT EXISTS idx_conversation_messages_role ON conversation_messages(role);
CREATE INDEX IF NOT EXISTS idx_conversation_messages_status ON conversation_messages(status);
CREATE INDEX IF NOT EXISTS idx_conversation_messages_created_at ON conversation_messages(created_at);
CREATE INDEX IF NOT EXISTS idx_conversation_messages_intent ON conversation_messages(intent_recognized);

-- Create composite indexes for common queries
CREATE INDEX IF NOT EXISTS idx_conversations_user_activity ON conversations(user_id, last_activity_at DESC);
CREATE INDEX IF NOT EXISTS idx_conversation_messages_conv_created ON conversation_messages(conversation_id, created_at ASC);

-- Add constraints
ALTER TABLE conversations 
ADD CONSTRAINT chk_conversations_status 
CHECK (status IN ('ACTIVE', 'ARCHIVED', 'DELETED'));

ALTER TABLE conversation_messages 
ADD CONSTRAINT chk_messages_type 
CHECK (type IN ('COMMAND', 'RESPONSE', 'ERROR', 'PROGRESS', 'SYSTEM'));

ALTER TABLE conversation_messages 
ADD CONSTRAINT chk_messages_role 
CHECK (role IN ('USER', 'ASSISTANT', 'SYSTEM'));

ALTER TABLE conversation_messages 
ADD CONSTRAINT chk_messages_status 
CHECK (status IN ('PENDING', 'PROCESSING', 'COMPLETED', 'FAILED'));

-- Create trigger to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_conversations_updated_at 
    BEFORE UPDATE ON conversations 
    FOR EACH ROW 
    EXECUTE FUNCTION update_updated_at_column();

-- Insert sample conversation for testing
INSERT INTO conversations (
    user_id,
    title,
    session_id,
    status,
    last_activity_at
) 
SELECT 
    u.id,
    'Welcome conversation',
    'sample-session-' || u.id,
    'ACTIVE',
    NOW()
FROM users u 
WHERE u.email = '<EMAIL>'
ON CONFLICT (session_id) DO NOTHING;

-- Insert sample messages for testing
INSERT INTO conversation_messages (
    conversation_id,
    type,
    role,
    content,
    intent_recognized,
    confidence_score,
    status
)
SELECT 
    c.id,
    'COMMAND',
    'USER',
    'Hello, what can you help me with?',
    'greeting',
    0.95,
    'COMPLETED'
FROM conversations c
JOIN users u ON c.user_id = u.id
WHERE u.email = '<EMAIL>'
AND c.session_id LIKE 'sample-session-%'
ON CONFLICT DO NOTHING;

INSERT INTO conversation_messages (
    conversation_id,
    type,
    role,
    content,
    intent_recognized,
    confidence_score,
    status
)
SELECT 
    c.id,
    'RESPONSE',
    'ASSISTANT',
    'Hello! I''m IntelliFin''s AI assistant. I can help you with account balances, transactions, invoices, financial insights, and ZRA compliance. What would you like to know?',
    'greeting_response',
    0.98,
    'COMPLETED'
FROM conversations c
JOIN users u ON c.user_id = u.id
WHERE u.email = '<EMAIL>'
AND c.session_id LIKE 'sample-session-%'
ON CONFLICT DO NOTHING;

-- Verify the schema
\dt conversations
\dt conversation_messages
\d conversations
\d conversation_messages

-- Show sample data
SELECT 'Conversations:' as info;
SELECT id, title, session_id, status, created_at FROM conversations LIMIT 5;

SELECT 'Messages:' as info;
SELECT id, type, role, LEFT(content, 50) as content_preview, status, created_at 
FROM conversation_messages 
ORDER BY created_at DESC 
LIMIT 5;
