/**
 * Stores Index
 * Central export point for all Zustand stores
 */

// Export stores
export { useAuthStore, authSelectors } from './authStore';
export { useConversationStore, conversationSelectors } from './conversationStore';
export { useFinancialStore, financialSelectors } from './financialStore';

// Export types
export type { UserProfile } from '@/types/api';
export type { DisplayMessage, ConversationState, CommandSuggestion } from '@/types/conversation';
export type { DashboardData, FinancialAlert, BusinessInsight } from '@/types/financial';

/**
 * Combined store hook for accessing multiple stores
 * Usage: const { auth, conversation, financial } = useStores();
 */
export const useStores = () => ({
  auth: useAuthStore(),
  conversation: useConversationStore(),
  financial: useFinancialStore(),
});

/**
 * Store initialization function
 * Call this on app startup to initialize all stores
 */
export const initializeStores = async () => {
  try {
    // Initialize auth store first
    await useAuthStore.getState().initialize();
    
    // If user is authenticated, initialize financial data
    const { isAuthenticated } = useAuthStore.getState();
    if (isAuthenticated) {
      const financialStore = useFinancialStore.getState();
      
      // Fetch essential data in parallel
      await Promise.allSettled([
        financialStore.fetchAccounts(),
        financialStore.fetchCategories(),
        financialStore.fetchClients(),
        financialStore.fetchDashboardData(),
      ]);
    }
  } catch (error) {
    console.error('Store initialization failed:', error);
  }
};

/**
 * Store cleanup function
 * Call this on logout or app cleanup
 */
export const cleanupStores = () => {
  // Clear conversation messages
  useConversationStore.getState().clearMessages();
  
  // Reset financial store to initial state
  useFinancialStore.setState({
    transactions: [],
    accounts: [],
    categories: [],
    invoices: [],
    clients: [],
    dashboardData: null,
    financialSummary: null,
    isLoading: {
      transactions: false,
      accounts: false,
      categories: false,
      invoices: false,
      clients: false,
      dashboard: false,
      summary: false,
    },
    errors: {
      transactions: null,
      accounts: null,
      categories: null,
      invoices: null,
      clients: null,
      dashboard: null,
      summary: null,
    },
    pagination: {
      transactions: { page: 1, totalPages: 1, total: 0 },
      invoices: { page: 1, totalPages: 1, total: 0 },
    },
  });
};

/**
 * Store selectors for common cross-store queries
 */
export const globalSelectors = {
  /**
   * Check if user is fully onboarded and ready to use the app
   */
  isUserReady: () => {
    const auth = useAuthStore.getState();
    const financial = useFinancialStore.getState();
    
    return (
      auth.isAuthenticated &&
      auth.user?.onboardingStatus === 'ALL_SET' &&
      financial.accounts.length > 0
    );
  },

  /**
   * Get overall app loading state
   */
  isAppLoading: () => {
    const auth = useAuthStore.getState();
    const conversation = useConversationStore.getState();
    const financial = useFinancialStore.getState();
    
    return (
      auth.isLoading ||
      conversation.isConnecting ||
      financialSelectors.isLoading(financial)
    );
  },

  /**
   * Get critical errors that need user attention
   */
  getCriticalErrors: () => {
    const auth = useAuthStore.getState();
    const conversation = useConversationStore.getState();
    const financial = useFinancialStore.getState();
    
    const errors: string[] = [];
    
    if (auth.error) errors.push(`Auth: ${auth.error}`);
    if (conversation.connectionError) errors.push(`Connection: ${conversation.connectionError}`);
    if (financialSelectors.hasErrors(financial)) {
      Object.entries(financial.errors).forEach(([key, error]) => {
        if (error) errors.push(`${key}: ${error}`);
      });
    }
    
    return errors;
  },

  /**
   * Get user's business health summary
   */
  getBusinessHealth: () => {
    const financial = useFinancialStore.getState();
    const { dashboardData } = financial;
    
    if (!dashboardData) return null;
    
    const criticalAlerts = dashboardData.alerts.filter(
      alert => alert.severity === 'CRITICAL' || alert.severity === 'HIGH'
    );
    
    const connectedAccounts = financialSelectors.connectedAccounts(financial);
    const overdueInvoices = financialSelectors.overdueInvoices(financial);
    
    return {
      score: criticalAlerts.length === 0 ? 'GOOD' : 'NEEDS_ATTENTION',
      connectedAccounts: connectedAccounts.length,
      totalAccounts: financial.accounts.length,
      overdueInvoices: overdueInvoices.length,
      criticalAlerts: criticalAlerts.length,
      lastUpdated: dashboardData.summary.accountBalances[0]?.lastUpdated || new Date().toISOString(),
    };
  },
};

/**
 * Store event listeners for cross-store communication
 */
export const setupStoreListeners = () => {
  // Listen for auth changes to update other stores
  useAuthStore.subscribe(
    (state) => state.isAuthenticated,
    (isAuthenticated, previousIsAuthenticated) => {
      if (isAuthenticated && !previousIsAuthenticated) {
        // User just logged in, initialize financial data
        const financialStore = useFinancialStore.getState();
        Promise.allSettled([
          financialStore.fetchAccounts(),
          financialStore.fetchCategories(),
          financialStore.fetchClients(),
          financialStore.fetchDashboardData(),
        ]);
      } else if (!isAuthenticated && previousIsAuthenticated) {
        // User just logged out, cleanup stores
        cleanupStores();
      }
    }
  );

  // Listen for conversation connection changes
  useConversationStore.subscribe(
    (state) => state.isConnected,
    (isConnected) => {
      if (isConnected) {
        // Generate new session when connected
        useConversationStore.getState().generateNewSession();
      }
    }
  );

  // Listen for financial data changes to update dashboard
  useFinancialStore.subscribe(
    (state) => [state.transactions, state.accounts, state.invoices],
    () => {
      // Debounce dashboard updates
      const timeoutId = setTimeout(() => {
        useFinancialStore.getState().fetchDashboardData();
      }, 1000);
      
      return () => clearTimeout(timeoutId);
    }
  );
};

/**
 * Development helpers
 */
export const devHelpers = {
  /**
   * Reset all stores to initial state (development only)
   */
  resetAllStores: () => {
    if (process.env.NODE_ENV !== 'development') {
      console.warn('resetAllStores is only available in development');
      return;
    }
    
    useAuthStore.setState({
      user: null,
      token: null,
      isAuthenticated: false,
      isLoading: false,
      error: null,
    });
    
    useConversationStore.setState({
      messages: [],
      isConnected: false,
      isConnecting: false,
      connectionError: null,
      currentSessionId: null,
      isTyping: false,
      awaitingResponse: false,
      lastActivity: null,
      connectionState: 'DISCONNECTED',
      reconnectAttempts: 0,
    });
    
    cleanupStores();
  },

  /**
   * Log current state of all stores (development only)
   */
  logStoreStates: () => {
    if (process.env.NODE_ENV !== 'development') {
      console.warn('logStoreStates is only available in development');
      return;
    }
    
    console.group('Store States');
    console.log('Auth:', useAuthStore.getState());
    console.log('Conversation:', useConversationStore.getState());
    console.log('Financial:', useFinancialStore.getState());
    console.groupEnd();
  },
};
