package com.intellifin.repository;

import com.intellifin.model.User;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.Optional;
import java.util.UUID;

@Repository
public interface UserRepository extends JpaRepository<User, UUID> {

    /**
     * Find user by email address
     */
    Optional<User> findByEmail(String email);

    /**
     * Check if email already exists
     */
    boolean existsByEmail(String email);

    /**
     * Find users by organization name
     */
    Optional<User> findByOrganizationName(String organizationName);

    /**
     * Find users with failed login attempts
     */
    @Query("SELECT u FROM User u WHERE u.failedLoginAttempts >= :attempts")
    java.util.List<User> findUsersWithFailedAttempts(@Param("attempts") Integer attempts);

    /**
     * Find locked users that should be unlocked
     */
    @Query("SELECT u FROM User u WHERE u.accountLocked = true AND u.lockedUntil < :now")
    java.util.List<User> findExpiredLockedUsers(@Param("now") LocalDateTime now);

    /**
     * Update last login time
     */
    @Modifying
    @Query("UPDATE User u SET u.lastLoginAt = :loginTime WHERE u.id = :userId")
    void updateLastLoginTime(@Param("userId") UUID userId, @Param("loginTime") LocalDateTime loginTime);

    /**
     * Reset failed login attempts
     */
    @Modifying
    @Query("UPDATE User u SET u.failedLoginAttempts = 0, u.accountLocked = false, u.lockedUntil = null WHERE u.id = :userId")
    void resetFailedLoginAttempts(@Param("userId") UUID userId);

    /**
     * Verify user email
     */
    @Modifying
    @Query("UPDATE User u SET u.emailVerified = true WHERE u.id = :userId")
    void verifyEmail(@Param("userId") UUID userId);

    /**
     * Update user password
     */
    @Modifying
    @Query("UPDATE User u SET u.passwordHash = :passwordHash WHERE u.id = :userId")
    void updatePassword(@Param("userId") UUID userId, @Param("passwordHash") String passwordHash);

    /**
     * Find users by onboarding status
     */
    java.util.List<User> findByOnboardingStatus(User.OnboardingStatus status);

    /**
     * Count users by email verification status
     */
    @Query("SELECT COUNT(u) FROM User u WHERE u.emailVerified = :verified")
    Long countByEmailVerified(@Param("verified") Boolean verified);

    /**
     * Find recently registered users
     */
    @Query("SELECT u FROM User u WHERE u.createdAt >= :since ORDER BY u.createdAt DESC")
    java.util.List<User> findRecentlyRegistered(@Param("since") LocalDateTime since);
}
