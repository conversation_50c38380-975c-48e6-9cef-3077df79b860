# IntelliFin Architecture - API Specification

## 5. API Specification

This section defines IntelliFin's Hybrid API Strategy, combining a real-time conversational gateway with standard RESTful endpoints. This strategy is fundamental to how our frontend and backend will communicate to create the unique IntelliFin experience.

### The Hybrid API Strategy: Combining Real-Time Intent with RESTful Resources

Our API will operate on two distinct levels:

*   **The Conversational Gateway (WebSocket):** This is the primary entry point for all user intent. It's a real-time, stateful channel where the user sends their natural language commands. The frontend's main job is to send raw user text through this gateway.
*   **The Resource-Oriented APIs (REST):** These are the "workhorse" APIs for creating, reading, updating, and deleting our core data models (User, Invoice, etc.). These APIs are primarily used by our own backend orchestrator, not directly by the user's conversational commands.

To make this crystal clear, here is the end-to-end flow for our "Draft Invoice" vision:

1.  **User Input:** The user types their command into the Next.js UI.
2.  **WebSocket Message:** The frontend sends a simple message over our secure Spring WebSocket (STOMP) connection to `/app/conversation/command`. The payload is just the raw text.
3.  **AI Orchestration:** Our core Spring Boot backend receives this command and passes it to our AI Service.
4.  **Intent Recognition:** The AI Service (Python/FastAPI) uses LangChain4j and Gemini to parse the command and returns a structured object like `{"intent": "CREATE_INVOICE", "entities": {...}}`.
5.  **Internal REST Calls:** The Spring Boot orchestrator now uses our own internal workhorse APIs to execute the intent. It might call `GET /api/v1/clients?name=Phiri Corp`, then construct a valid invoice object and call `POST /api/v1/invoices`.
6.  **WebSocket Response:** The orchestrator gets the newly created draft Invoice object and pushes it back to the user's frontend over a dedicated WebSocket topic.
7.  **Dynamic UI Update:** The frontend receives the Invoice object and dynamically renders the "Invoice Draft/Approval Screen" view.

This hybrid approach allows us to have a revolutionary conversational UI for the user while building our backend on a foundation of robust, stateless, and well-defined RESTful services.

With that context, here is the formal API specification for the MVP:

### 5.1 Conversational Command API (WebSocket)

This is the primary entry point for all user interactions.

*   **WebSocket Topic:** `/app/conversation/command`
*   **Purpose:** To receive a natural language command from the user for processing and orchestration by the backend.
*   **Message Body (Client to Server):** A JSON object containing the user's command string. Ex: `{"command": "Show me my profit this month"}`.
*   **Response (Server to Client):** Asynchronous push to a user-specific topic (e.g., `/user/queue/responses`). The payload will be a structured object representing the result (e.g., a FinancialSummary object, a draft Invoice object, a confirmation message).
*   **Authentication:** The WebSocket connection must be established and authenticated using the user's JWT.

### 5.2 Resource Management API (REST)

These are the stateless, "workhorse" endpoints used primarily by the backend orchestrator and for basic, non-conversational data retrieval by the frontend. All authenticated endpoints expect a JWT.

#### User Management & Authentication (`/api/v1/auth`, `/api/v1/users`)

*   `POST /api/v1/auth/register`: Creates a new user account. (Public)
*   `POST /api/v1/auth/login`: Authenticates a user and returns a JWT. (Public)
*   `GET /api/v1/users/me`: Fetches the profile of the currently authenticated user. (Requires JWT)

#### Financial Account Management (`/api/v1/financial-accounts`)

*   `POST /api/v1/financial-accounts/connect/mtn`: Initiates the secure connection process for an MTN Mobile Money account. (Requires JWT)
*   `GET /api/v1/financial-accounts`: Retrieves a list of all financial accounts connected by the current user. (Requires JWT)
*   `GET /api/v1/financial-accounts/{accountId}`: Retrieves the details of a specific financial account. (Requires JWT)

#### Transaction Management (`/api/v1/transactions`)

*   `GET /api/v1/transactions`: Retrieves a paginated list of transactions for the current user. (Requires JWT)
*   `POST /api/v1/transactions`: Creates a new transaction. (Requires JWT)
*   `PUT /api/v1/transactions/{transactionId}`: Updates a transaction (e.g., category assignment). (Requires JWT)
*   `GET /api/v1/transactions/{transactionId}`: Retrieves a specific transaction. (Requires JWT)

#### Category Management (`/api/v1/categories`)

*   `GET /api/v1/categories`: Retrieves all categories (system and user-defined) for the current user. (Requires JWT)
*   `POST /api/v1/categories`: Creates a new user-defined category. (Requires JWT)
*   `PUT /api/v1/categories/{categoryId}`: Updates a user-defined category. (Requires JWT)

#### Client Management (`/api/v1/clients`)

*   `GET /api/v1/clients`: Retrieves a list of all clients for the current user. (Requires JWT)
*   `POST /api/v1/clients`: Creates a new client. (Requires JWT)
*   `PUT /api/v1/clients/{clientId}`: Updates a client. (Requires JWT)
*   `GET /api/v1/clients/{clientId}`: Retrieves a specific client. (Requires JWT)

#### Invoice Management (`/api/v1/invoices`)

*   `GET /api/v1/invoices`: Retrieves a list of all invoices for the current user. (Requires JWT)
*   `POST /api/v1/invoices`: Creates a new invoice draft. (Requires JWT)
*   `PUT /api/v1/invoices/{invoiceId}`: Updates an invoice. (Requires JWT)
*   `POST /api/v1/invoices/{invoiceId}/submit`: Submits an invoice to ZRA. (Requires JWT)
*   `GET /api/v1/invoices/{invoiceId}`: Retrieves a specific invoice. (Requires JWT)

#### Financial Reporting (`/api/v1/reports`)

*   `GET /api/v1/reports/summary?period={period}`: Retrieves financial summary for a specified period. (Requires JWT)
*   `GET /api/v1/reports/income-statement?startDate={date}&endDate={date}`: Retrieves income statement. (Requires JWT)
*   `GET /api/v1/reports/balance-sheet?asOf={date}`: Retrieves balance sheet. (Requires JWT)

### 5.3 API Response Format

All REST API responses follow a consistent format:

```typescript
interface APIResponse<T> {
  success: boolean;
  data?: T;
  error?: {
    code: string;
    message: string;
    details?: any;
  };
  meta?: {
    pagination?: {
      page: number;
      limit: number;
      total: number;
      totalPages: number;
    };
    timestamp: string;
  };
}
```

### 5.4 Error Handling

*   **HTTP Status Codes:** Standard HTTP status codes (200, 201, 400, 401, 403, 404, 500, etc.)
*   **Error Response Format:** Consistent error response structure with error codes and messages
*   **Validation Errors:** Detailed validation error messages for request body validation
*   **Rate Limiting:** 429 status code for rate limit exceeded
*   **Circuit Breaker:** 503 status code when services are temporarily unavailable

### 5.5 Authentication & Authorization

*   **JWT Tokens:** All authenticated endpoints require a valid JWT in the Authorization header
*   **Token Format:** `Authorization: Bearer <jwt_token>`
*   **Token Expiration:** Short-lived access tokens with refresh token mechanism
*   **Scope-based Access:** Fine-grained permissions based on user roles and resource ownership

---

**Previous Section:** [Data Models](04-data-models.md)  
**Next Section:** [External Integrations](06-external-integrations.md) 