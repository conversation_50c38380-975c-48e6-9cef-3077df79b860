# IntelliFin Conversational Interface Integration Test Suite
# Tests the complete conversational command processing system

Write-Host "🧪 IntelliFin Conversational Interface Integration Tests" -ForegroundColor Blue
Write-Host "======================================================" -ForegroundColor Blue

# Test configuration
$API_BASE_URL = "http://localhost:8080"
$WS_URL = "ws://localhost:8080/ws/conversation"
$AI_SERVICE_URL = "http://localhost:8000"

# Test credentials
$TEST_EMAIL = "<EMAIL>"
$TEST_PASSWORD = "TestPass123!"

# Function to print test status
function Print-Test {
    param(
        [string]$TestName,
        [string]$Status
    )
    
    switch ($Status) {
        "PASS" { Write-Host "  ✅ $TestName" -ForegroundColor Green }
        "FAIL" { Write-Host "  ❌ $TestName" -ForegroundColor Red }
        "SKIP" { Write-Host "  ⏭️  $TestName (SKIPPED)" -ForegroundColor Yellow }
        default { Write-Host "  🔄 $TestName" -ForegroundColor Blue }
    }
}

# Function to test API endpoint
function Test-ApiEndpoint {
    param(
        [string]$Endpoint,
        [int]$ExpectedStatus,
        [string]$Method = "GET",
        [string]$Data = "",
        [hashtable]$Headers = @{}
    )
    
    try {
        $requestParams = @{
            Uri = $Endpoint
            Method = $Method
            UseBasicParsing = $true
            TimeoutSec = 10
        }
        
        if ($Data) {
            $requestParams.Body = $Data
            $requestParams.ContentType = "application/json"
        }
        
        if ($Headers.Count -gt 0) {
            $requestParams.Headers = $Headers
        }
        
        $response = Invoke-WebRequest @requestParams
        return @{
            Success = ($response.StatusCode -eq $ExpectedStatus)
            StatusCode = $response.StatusCode
            Content = $response.Content
        }
    }
    catch {
        $statusCode = if ($_.Exception.Response) { $_.Exception.Response.StatusCode.value__ } else { 0 }
        return @{
            Success = ($statusCode -eq $ExpectedStatus)
            StatusCode = $statusCode
            Content = $_.Exception.Message
        }
    }
}

# Function to authenticate and get JWT token
function Get-AuthToken {
    $loginData = @{
        email = $TEST_EMAIL
        password = $TEST_PASSWORD
    } | ConvertTo-Json
    
    $result = Test-ApiEndpoint "$API_BASE_URL/api/v1/auth/login" 200 "POST" $loginData
    
    if ($result.Success) {
        $response = $result.Content | ConvertFrom-Json
        return $response.token
    }
    
    return $null
}

Write-Host ""
Write-Host "🔍 Phase 1: Infrastructure Health Checks" -ForegroundColor Cyan
Write-Host "----------------------------------------" -ForegroundColor Cyan

# Test database connectivity
Print-Test "PostgreSQL Database" "RUNNING"
try {
    $pgResult = docker exec intellifin-postgres pg_isready -U intellifin_user -d intellifin_dev 2>$null
    if ($LASTEXITCODE -eq 0) {
        Print-Test "PostgreSQL Database" "PASS"
        $POSTGRES_AVAILABLE = $true
    } else {
        Print-Test "PostgreSQL Database" "FAIL"
        $POSTGRES_AVAILABLE = $false
    }
} catch {
    Print-Test "PostgreSQL Database" "FAIL"
    $POSTGRES_AVAILABLE = $false
}

# Test Redis connectivity
Print-Test "Redis Cache" "RUNNING"
try {
    $redisResult = docker exec intellifin-redis redis-cli ping 2>$null
    if ($LASTEXITCODE -eq 0) {
        Print-Test "Redis Cache" "PASS"
        $REDIS_AVAILABLE = $true
    } else {
        Print-Test "Redis Cache" "FAIL"
        $REDIS_AVAILABLE = $false
    }
} catch {
    Print-Test "Redis Cache" "FAIL"
    $REDIS_AVAILABLE = $false
}

Write-Host ""
Write-Host "🗄️  Phase 2: Database Schema Validation" -ForegroundColor Cyan
Write-Host "--------------------------------------" -ForegroundColor Cyan

if ($POSTGRES_AVAILABLE) {
    # Check conversation tables
    Print-Test "Conversation Tables Schema" "RUNNING"
    try {
        $tableCheck = docker exec intellifin-postgres psql -U intellifin_user -d intellifin_dev -c "SELECT COUNT(*) FROM information_schema.tables WHERE table_name IN ('conversations', 'conversation_messages');" 2>$null
        if ($LASTEXITCODE -eq 0) {
            Print-Test "Conversation Tables Schema" "PASS"
        } else {
            Print-Test "Conversation Tables Schema" "FAIL"
        }
    } catch {
        Print-Test "Conversation Tables Schema" "FAIL"
    }
    
    # Check conversation table structure
    Print-Test "Conversation Table Structure" "RUNNING"
    try {
        $structureCheck = docker exec intellifin-postgres psql -U intellifin_user -d intellifin_dev -c "\d conversations" 2>$null
        if ($LASTEXITCODE -eq 0) {
            Print-Test "Conversation Table Structure" "PASS"
        } else {
            Print-Test "Conversation Table Structure" "FAIL"
        }
    } catch {
        Print-Test "Conversation Table Structure" "FAIL"
    }
    
    # Check message table structure
    Print-Test "Message Table Structure" "RUNNING"
    try {
        $messageStructureCheck = docker exec intellifin-postgres psql -U intellifin_user -d intellifin_dev -c "\d conversation_messages" 2>$null
        if ($LASTEXITCODE -eq 0) {
            Print-Test "Message Table Structure" "PASS"
        } else {
            Print-Test "Message Table Structure" "FAIL"
        }
    } catch {
        Print-Test "Message Table Structure" "FAIL"
    }
} else {
    Write-Host "  ⚠️  Skipping database schema tests - PostgreSQL not available" -ForegroundColor Yellow
}

Write-Host ""
Write-Host "🌐 Phase 3: Backend Service Tests" -ForegroundColor Cyan
Write-Host "--------------------------------" -ForegroundColor Cyan

# Test backend core service
Print-Test "Backend Core Service Health" "RUNNING"
$healthResult = Test-ApiEndpoint "$API_BASE_URL/api/v1/health" 200
if ($healthResult.Success) {
    Print-Test "Backend Core Service Health" "PASS"
    $BACKEND_AVAILABLE = $true
} else {
    Print-Test "Backend Core Service Health" "SKIP"
    Write-Host "  ⚠️  Backend service not running. Status: $($healthResult.StatusCode)" -ForegroundColor Yellow
    $BACKEND_AVAILABLE = $false
}

# Test conversation service health
if ($BACKEND_AVAILABLE) {
    Print-Test "Conversation Service Health" "RUNNING"
    $convHealthResult = Test-ApiEndpoint "$API_BASE_URL/api/v1/conversations/health" 200
    if ($convHealthResult.Success) {
        Print-Test "Conversation Service Health" "PASS"
        $CONVERSATION_SERVICE_AVAILABLE = $true
    } else {
        Print-Test "Conversation Service Health" "FAIL"
        $CONVERSATION_SERVICE_AVAILABLE = $false
    }
} else {
    Print-Test "Conversation Service Health" "SKIP"
    $CONVERSATION_SERVICE_AVAILABLE = $false
}

# Test AI service
Print-Test "AI Service Health" "RUNNING"
$aiHealthResult = Test-ApiEndpoint "$AI_SERVICE_URL/health" 200
if ($aiHealthResult.Success) {
    Print-Test "AI Service Health" "PASS"
    $AI_SERVICE_AVAILABLE = $true
} else {
    Print-Test "AI Service Health" "SKIP"
    Write-Host "  ⚠️  AI service not running. This is expected in current setup." -ForegroundColor Yellow
    $AI_SERVICE_AVAILABLE = $false
}

Write-Host ""
Write-Host "🔐 Phase 4: Authentication Integration Tests" -ForegroundColor Cyan
Write-Host "-------------------------------------------" -ForegroundColor Cyan

if ($BACKEND_AVAILABLE) {
    # Test user authentication
    Print-Test "User Authentication" "RUNNING"
    $authToken = Get-AuthToken
    if ($authToken) {
        Print-Test "User Authentication" "PASS"
        $AUTH_AVAILABLE = $true
        Write-Host "  📝 JWT Token obtained successfully" -ForegroundColor Gray
    } else {
        Print-Test "User Authentication" "FAIL"
        $AUTH_AVAILABLE = $false
    }
    
    # Test authenticated conversation endpoints
    if ($AUTH_AVAILABLE) {
        Print-Test "Authenticated Conversation API" "RUNNING"
        $headers = @{ Authorization = "Bearer $authToken" }
        $convListResult = Test-ApiEndpoint "$API_BASE_URL/api/v1/conversations" 200 "GET" "" $headers
        if ($convListResult.Success) {
            Print-Test "Authenticated Conversation API" "PASS"
        } else {
            Print-Test "Authenticated Conversation API" "FAIL"
        }
    }
} else {
    Print-Test "User Authentication" "SKIP"
    $AUTH_AVAILABLE = $false
}

Write-Host ""
Write-Host "💬 Phase 5: Conversation API Tests" -ForegroundColor Cyan
Write-Host "---------------------------------" -ForegroundColor Cyan

if ($BACKEND_AVAILABLE -and $AUTH_AVAILABLE) {
    $headers = @{ Authorization = "Bearer $authToken" }
    
    # Test REST command processing
    Print-Test "REST Command Processing" "RUNNING"
    $commandData = @{
        command = "What can you help me with?"
        sessionId = "test-session-$(Get-Date -Format 'yyyyMMdd-HHmmss')"
        expectResponse = $true
        saveToHistory = $true
    } | ConvertTo-Json
    
    $commandResult = Test-ApiEndpoint "$API_BASE_URL/api/v1/conversations/command" 200 "POST" $commandData $headers
    if ($commandResult.Success) {
        Print-Test "REST Command Processing" "PASS"
        
        # Parse response to get conversation details
        try {
            $commandResponse = $commandResult.Content | ConvertFrom-Json
            $conversationId = $commandResponse.conversationId
            Write-Host "  📝 Conversation created: $conversationId" -ForegroundColor Gray
            
            # Test conversation retrieval
            if ($conversationId) {
                Print-Test "Conversation Retrieval" "RUNNING"
                $convDetailResult = Test-ApiEndpoint "$API_BASE_URL/api/v1/conversations/$conversationId" 200 "GET" "" $headers
                if ($convDetailResult.Success) {
                    Print-Test "Conversation Retrieval" "PASS"
                } else {
                    Print-Test "Conversation Retrieval" "FAIL"
                }
            }
        } catch {
            Write-Host "  ⚠️  Could not parse command response" -ForegroundColor Yellow
        }
    } else {
        Print-Test "REST Command Processing" "FAIL"
        Write-Host "  ❌ Status: $($commandResult.StatusCode)" -ForegroundColor Red
    }
    
    # Test conversation list
    Print-Test "Conversation List API" "RUNNING"
    $listResult = Test-ApiEndpoint "$API_BASE_URL/api/v1/conversations" 200 "GET" "" $headers
    if ($listResult.Success) {
        Print-Test "Conversation List API" "PASS"
        
        try {
            $conversations = $listResult.Content | ConvertFrom-Json
            Write-Host "  📝 Found $($conversations.Count) conversations" -ForegroundColor Gray
        } catch {
            Write-Host "  ⚠️  Could not parse conversation list" -ForegroundColor Yellow
        }
    } else {
        Print-Test "Conversation List API" "FAIL"
    }
} else {
    Write-Host "  ⚠️  Skipping conversation API tests - backend or auth not available" -ForegroundColor Yellow
}

Write-Host ""
Write-Host "📊 Integration Test Summary" -ForegroundColor Green
Write-Host "==========================" -ForegroundColor Green

Write-Host ""
Write-Host "Infrastructure Status:" -ForegroundColor White
if ($POSTGRES_AVAILABLE) {
    Write-Host "  ✅ PostgreSQL Database - Ready" -ForegroundColor Green
} else {
    Write-Host "  ❌ PostgreSQL Database - Failed" -ForegroundColor Red
}

if ($REDIS_AVAILABLE) {
    Write-Host "  ✅ Redis Cache - Ready" -ForegroundColor Green
} else {
    Write-Host "  ❌ Redis Cache - Failed" -ForegroundColor Red
}

Write-Host ""
Write-Host "Application Services:" -ForegroundColor White
if ($BACKEND_AVAILABLE) {
    Write-Host "  ✅ Backend Core Service - Running" -ForegroundColor Green
} else {
    Write-Host "  ❌ Backend Core Service - Not Running" -ForegroundColor Red
}

if ($CONVERSATION_SERVICE_AVAILABLE) {
    Write-Host "  ✅ Conversation Service - Running" -ForegroundColor Green
} else {
    Write-Host "  ❌ Conversation Service - Not Running" -ForegroundColor Red
}

if ($AI_SERVICE_AVAILABLE) {
    Write-Host "  ✅ AI Service - Running" -ForegroundColor Green
} else {
    Write-Host "  ⚠️  AI Service - Not Running (Expected)" -ForegroundColor Yellow
}

Write-Host ""
Write-Host "Authentication & API:" -ForegroundColor White
if ($AUTH_AVAILABLE) {
    Write-Host "  ✅ JWT Authentication - Working" -ForegroundColor Green
    Write-Host "  ✅ Conversation API - Functional" -ForegroundColor Green
} else {
    Write-Host "  ❌ Authentication System - Failed" -ForegroundColor Red
}

Write-Host ""
Write-Host "🎯 Test Results:" -ForegroundColor Cyan
if ($POSTGRES_AVAILABLE -and $REDIS_AVAILABLE -and $BACKEND_AVAILABLE -and $CONVERSATION_SERVICE_AVAILABLE -and $AUTH_AVAILABLE) {
    Write-Host "  ✅ Conversational Interface: READY FOR DEPLOYMENT" -ForegroundColor Green
    Write-Host "  ✅ Database Schema: COMPLETE" -ForegroundColor Green
    Write-Host "  ✅ Authentication Integration: WORKING" -ForegroundColor Green
    Write-Host "  ✅ REST API: FUNCTIONAL" -ForegroundColor Green
} else {
    Write-Host "  ❌ Conversational Interface: INCOMPLETE" -ForegroundColor Red
    Write-Host "  ⚠️  Some components need to be started or fixed" -ForegroundColor Yellow
}

Write-Host ""
Write-Host "📋 Next Steps:" -ForegroundColor Yellow
if ($BACKEND_AVAILABLE) {
    Write-Host "  1. ✅ Backend services are running and functional" -ForegroundColor White
    Write-Host "  2. ✅ Conversation API is working correctly" -ForegroundColor White
    Write-Host "  3. 🔄 WebSocket testing requires browser-based testing" -ForegroundColor White
    Write-Host "  4. 🔄 Frontend integration testing requires UI testing" -ForegroundColor White
} else {
    Write-Host "  1. Start backend services: docker-compose up -d" -ForegroundColor White
    Write-Host "  2. Build application services: docker-compose build" -ForegroundColor White
    Write-Host "  3. Re-run integration tests" -ForegroundColor White
}

Write-Host ""
Write-Host "🎉 Conversational interface integration tests completed!" -ForegroundColor Green

# Return overall status
if ($POSTGRES_AVAILABLE -and $REDIS_AVAILABLE -and $BACKEND_AVAILABLE -and $CONVERSATION_SERVICE_AVAILABLE -and $AUTH_AVAILABLE) {
    exit 0
} else {
    exit 1
}
