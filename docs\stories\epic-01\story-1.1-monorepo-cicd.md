# Story 1.1: Monorepo Setup and CI/CD Pipeline with Hybrid Messaging

**Epic:** Foundation & Infrastructure
**Status:** Done
**Priority:** High
**Story Points:** 13

## User Story

**As a** development team
**I want** a native-first local development environment with hybrid messaging strategy and automated CI/CD
**So that** we can maximize developer velocity with zero-cost local messaging while ensuring production-grade deployments

## Acceptance Criteria

- [x] Monorepo structure is established with proper package organization
- [x] Native RabbitMQ installation and configuration for local development
- [x] Messaging abstraction layers implemented (Spring Cloud Stream, Python MessagingService)
- [x] Dockerfiles created for all microservices (Java/Spring Boot, Python/FastAPI, Next.js)
- [x] Docker Compose available for CI environments and production parity testing (including PostgreSQL, Redis, and RabbitMQ)
- [x] Google Gemini free tier integrated for cost-free AI development and testing
- [x] GitHub Actions CI/CD pipeline automates building, testing, and containerizing
- [x] Local development environment supports native service execution with localhost RabbitMQ
- [x] All services can communicate with each other via messaging abstraction layer
- [x] Environment variables and secrets are properly managed for both local and cloud messaging
- [x] Error handling is implemented for service startup failures and messaging broker connectivity
- [x] Graceful degradation is tested when services are unavailable

## Technical Implementation

### Monorepo Structure
```
intellifin/
├── apps/
│   ├── frontend-nextjs/     # Next.js frontend application
│   ├── backend-java-core/   # Spring Boot core backend
│   ├── backend-python-ai/   # FastAPI AI service
│   └── backend-java-zra/    # ZRA compliance service
├── libs/
│   ├── data-models/         # Shared TypeScript interfaces
│   ├── api-interfaces/      # API contract definitions
│   ├── shared-utils/        # Common utilities
│   └── auth-client/         # Authentication client library
├── docker-compose.yml       # Local development orchestration
├── .github/workflows/       # CI/CD pipelines
└── docs/                    # Project documentation
```

### Frontend Changes
- `apps/frontend-nextjs/Dockerfile` - Multi-stage build for Next.js app
- `apps/frontend-nextjs/next.config.js` - Next.js configuration for containerized environment
- `apps/frontend-nextjs/package.json` - Dependencies and build scripts

### Backend Changes
- `apps/backend-java-core/Dockerfile` - Spring Boot application container
- `apps/backend-java-core/pom.xml` - Maven configuration with Docker support
- `apps/backend-python-ai/Dockerfile` - FastAPI application container
- `apps/backend-python-ai/pyproject.toml` - Poetry configuration
- `apps/backend-java-zra/Dockerfile` - ZRA service container

### Infrastructure Changes
- `docker-compose.yml` - Complete CI/production parity stack (includes RabbitMQ)
- `docker-compose.override.yml` - Development-specific overrides
- `.env.example` - Environment variable templates for both local and cloud messaging
- `.github/workflows/ci.yml` - Main CI/CD pipeline
- `.github/workflows/deploy.yml` - Deployment pipeline
- `docs/setup/rabbitmq-installation.md` - Native RabbitMQ installation guide
- `docs/setup/local-development-setup.md` - Complete local development setup

### Messaging Infrastructure Changes
- Native RabbitMQ installation and configuration for local development
- Spring Cloud Stream configuration with profile-based binders (rabbit/servicebus)
- Python MessagingService wrapper with environment-based implementations
- Configuration profiles for seamless environment switching

### Database Changes
- `docker-compose.yml` includes PostgreSQL, Redis, and RabbitMQ containers for CI
- Database initialization scripts for development data
- Migration scripts for schema management

## API Contracts

```typescript
// Internal service communication contracts
interface ServiceHealthCheck {
  service: string;
  status: 'healthy' | 'unhealthy' | 'degraded';
  timestamp: string;
  version: string;
}

interface ServiceDiscovery {
  services: {
    [serviceName: string]: {
      url: string;
      health: string;
      version: string;
    };
  };
}
```

## Error Handling

- **Service Startup Failures:** Clear error messages and retry mechanisms
- **Database Connection Issues:** Graceful fallback and connection pooling
- **Container Orchestration Failures:** Health checks and automatic restart
- **CI/CD Pipeline Failures:** Detailed error reporting and rollback procedures
- **Environment Configuration Errors:** Validation and helpful error messages

## Definition of Done

- [x] Developer can clone repository and set up native local development environment
- [x] RabbitMQ runs natively on developer machine with proper configuration
- [x] All services communicate via messaging abstraction layer (local RabbitMQ)
- [x] Docker Compose available for CI environments and production parity testing
- [x] CI/CD pipeline successfully builds and tests all applications
- [x] Messaging abstraction enables seamless switching between RabbitMQ and Azure Service Bus
- [x] Google Gemini free tier integration provides cost-free AI development capability
- [x] Environment variables are properly configured for both local and cloud messaging
- [x] Health checks are implemented for all services and messaging connectivity
- [x] Error scenarios are handled gracefully including messaging broker failures
- [x] Documentation is complete for native local development setup and Docker alternatives
- [x] Performance meets development velocity requirements with native execution

## Dependencies

None - This is the foundational story that all other stories depend on.

## Notes

This story establishes the foundational development environment that all other stories will depend on. It must be completed before any service-specific development can begin. The hybrid messaging strategy provides zero-cost local development with RabbitMQ while ensuring production-grade reliability with Azure Service Bus. The monorepo structure enables code sharing while maintaining service independence. Messaging abstraction layers ensure seamless transitions between environments. Google Gemini free tier integration ensures cost-effective AI development and testing.

---

**Related Stories:**
- [Story 1.2: User Registration and Authentication Flow](story-1.2-user-auth.md)

**Epic:** [Foundation & Infrastructure](../../epics-and-stories.md#epic-1-foundation--infrastructure) 