package com.intellifin;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.context.properties.ConfigurationPropertiesScan;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.transaction.annotation.EnableTransactionManagement;

@SpringBootApplication
@ConfigurationPropertiesScan
@EnableAsync
@EnableTransactionManagement
public class IntelliFinApplication {

    /**
     * Main entry point for the IntelliFin Spring Boot application.
     * This method initializes and starts the Spring Boot application context,
     * enabling auto-configuration and component scanning for the entire application.
     *
     * @param args command-line arguments passed to the application at startup.
     *             These arguments can be used to configure application properties
     *             or override default Spring Boot settings.
     */
    public static void main(String[] args) {
        SpringApplication.run(IntelliFinApplication.class, args);
    }
}