import { create } from 'zustand';
import { devtools, subscribeWithSelector } from 'zustand/middleware';
import { 
  Transaction, 
  FinancialAccount, 
  Category, 
  Invoice, 
  Client, 
  FinancialSummary,
  PaginatedResponse 
} from '@/types/api';
import { 
  DashboardData, 
  FinancialAlert, 
  BusinessInsight,
  FinancialMetrics 
} from '@/types/financial';
import { 
  transactionsService, 
  categoriesService, 
  accountsService, 
  invoicesService, 
  clientsService, 
  reportsService 
} from '@/services/api';

interface FinancialState {
  // Data
  transactions: Transaction[];
  accounts: FinancialAccount[];
  categories: Category[];
  invoices: Invoice[];
  clients: Client[];
  dashboardData: DashboardData | null;
  financialSummary: FinancialSummary | null;
  
  // Loading states
  isLoading: {
    transactions: boolean;
    accounts: boolean;
    categories: boolean;
    invoices: boolean;
    clients: boolean;
    dashboard: boolean;
    summary: boolean;
  };
  
  // Error states
  errors: {
    transactions: string | null;
    accounts: string | null;
    categories: string | null;
    invoices: string | null;
    clients: string | null;
    dashboard: string | null;
    summary: string | null;
  };
  
  // Pagination
  pagination: {
    transactions: { page: number; totalPages: number; total: number };
    invoices: { page: number; totalPages: number; total: number };
  };
  
  // Actions
  // Transactions
  fetchTransactions: (params?: any) => Promise<void>;
  createTransaction: (transaction: any) => Promise<Transaction>;
  updateTransaction: (id: string, updates: any) => Promise<Transaction>;
  deleteTransaction: (id: string) => Promise<void>;
  categorizeTransaction: (id: string, categoryId: string) => Promise<Transaction>;
  
  // Accounts
  fetchAccounts: () => Promise<void>;
  connectAccount: (provider: string, credentials: any) => Promise<FinancialAccount>;
  syncAccount: (id: string) => Promise<void>;
  disconnectAccount: (id: string) => Promise<void>;
  
  // Categories
  fetchCategories: () => Promise<void>;
  createCategory: (category: any) => Promise<Category>;
  updateCategory: (id: string, updates: any) => Promise<Category>;
  deleteCategory: (id: string) => Promise<void>;
  
  // Invoices
  fetchInvoices: (params?: any) => Promise<void>;
  createInvoice: (invoice: any) => Promise<Invoice>;
  updateInvoice: (id: string, updates: any) => Promise<Invoice>;
  deleteInvoice: (id: string) => Promise<void>;
  submitInvoiceToZRA: (id: string) => Promise<void>;
  markInvoiceAsPaid: (id: string, paymentDetails?: any) => Promise<Invoice>;
  
  // Clients
  fetchClients: () => Promise<void>;
  createClient: (client: any) => Promise<Client>;
  updateClient: (id: string, updates: any) => Promise<Client>;
  deleteClient: (id: string) => Promise<void>;
  
  // Dashboard and Reports
  fetchDashboardData: () => Promise<void>;
  fetchFinancialSummary: (params?: any) => Promise<void>;
  
  // Utilities
  clearErrors: () => void;
  setLoading: (key: keyof FinancialState['isLoading'], loading: boolean) => void;
  setError: (key: keyof FinancialState['errors'], error: string | null) => void;
}

export const useFinancialStore = create<FinancialState>()(
  devtools(
    subscribeWithSelector((set, get) => ({
      // Initial state
      transactions: [],
      accounts: [],
      categories: [],
      invoices: [],
      clients: [],
      dashboardData: null,
      financialSummary: null,
      
      isLoading: {
        transactions: false,
        accounts: false,
        categories: false,
        invoices: false,
        clients: false,
        dashboard: false,
        summary: false,
      },
      
      errors: {
        transactions: null,
        accounts: null,
        categories: null,
        invoices: null,
        clients: null,
        dashboard: null,
        summary: null,
      },
      
      pagination: {
        transactions: { page: 1, totalPages: 1, total: 0 },
        invoices: { page: 1, totalPages: 1, total: 0 },
      },

      // Transaction actions
      fetchTransactions: async (params = {}) => {
        set((state) => ({
          isLoading: { ...state.isLoading, transactions: true },
          errors: { ...state.errors, transactions: null },
        }));

        try {
          const response = await transactionsService.getTransactions(params);
          set((state) => ({
            transactions: response.data,
            pagination: {
              ...state.pagination,
              transactions: {
                page: response.pagination.page,
                totalPages: response.pagination.totalPages,
                total: response.pagination.total,
              },
            },
            isLoading: { ...state.isLoading, transactions: false },
          }));
        } catch (error: any) {
          set((state) => ({
            isLoading: { ...state.isLoading, transactions: false },
            errors: { ...state.errors, transactions: error.message },
          }));
          throw error;
        }
      },

      createTransaction: async (transactionData) => {
        try {
          const transaction = await transactionsService.createTransaction(transactionData);
          set((state) => ({
            transactions: [transaction, ...state.transactions],
          }));
          return transaction;
        } catch (error: any) {
          set((state) => ({
            errors: { ...state.errors, transactions: error.message },
          }));
          throw error;
        }
      },

      updateTransaction: async (id, updates) => {
        try {
          const transaction = await transactionsService.updateTransaction(id, updates);
          set((state) => ({
            transactions: state.transactions.map((t) =>
              t.id === id ? transaction : t
            ),
          }));
          return transaction;
        } catch (error: any) {
          set((state) => ({
            errors: { ...state.errors, transactions: error.message },
          }));
          throw error;
        }
      },

      deleteTransaction: async (id) => {
        try {
          await transactionsService.deleteTransaction(id);
          set((state) => ({
            transactions: state.transactions.filter((t) => t.id !== id),
          }));
        } catch (error: any) {
          set((state) => ({
            errors: { ...state.errors, transactions: error.message },
          }));
          throw error;
        }
      },

      categorizeTransaction: async (id, categoryId) => {
        try {
          const transaction = await transactionsService.approveCategorization(id, categoryId);
          set((state) => ({
            transactions: state.transactions.map((t) =>
              t.id === id ? transaction : t
            ),
          }));
          return transaction;
        } catch (error: any) {
          set((state) => ({
            errors: { ...state.errors, transactions: error.message },
          }));
          throw error;
        }
      },

      // Account actions
      fetchAccounts: async () => {
        set((state) => ({
          isLoading: { ...state.isLoading, accounts: true },
          errors: { ...state.errors, accounts: null },
        }));

        try {
          const accounts = await accountsService.getAccounts();
          set((state) => ({
            accounts,
            isLoading: { ...state.isLoading, accounts: false },
          }));
        } catch (error: any) {
          set((state) => ({
            isLoading: { ...state.isLoading, accounts: false },
            errors: { ...state.errors, accounts: error.message },
          }));
          throw error;
        }
      },

      connectAccount: async (provider, credentials) => {
        try {
          let account: FinancialAccount;
          
          if (provider === 'MTN') {
            account = await accountsService.connectMTNAccount(credentials);
          } else if (provider === 'AIRTEL') {
            account = await accountsService.connectAirtelAccount(credentials);
          } else {
            account = await accountsService.connectBankAccount(credentials);
          }
          
          set((state) => ({
            accounts: [...state.accounts, account],
          }));
          
          return account;
        } catch (error: any) {
          set((state) => ({
            errors: { ...state.errors, accounts: error.message },
          }));
          throw error;
        }
      },

      syncAccount: async (id) => {
        try {
          await accountsService.syncAccount(id);
          // Refresh accounts and transactions after sync
          await get().fetchAccounts();
          await get().fetchTransactions();
        } catch (error: any) {
          set((state) => ({
            errors: { ...state.errors, accounts: error.message },
          }));
          throw error;
        }
      },

      disconnectAccount: async (id) => {
        try {
          await accountsService.disconnectAccount(id);
          set((state) => ({
            accounts: state.accounts.filter((a) => a.id !== id),
          }));
        } catch (error: any) {
          set((state) => ({
            errors: { ...state.errors, accounts: error.message },
          }));
          throw error;
        }
      },

      // Category actions
      fetchCategories: async () => {
        set((state) => ({
          isLoading: { ...state.isLoading, categories: true },
          errors: { ...state.errors, categories: null },
        }));

        try {
          const categories = await categoriesService.getCategories();
          set((state) => ({
            categories,
            isLoading: { ...state.isLoading, categories: false },
          }));
        } catch (error: any) {
          set((state) => ({
            isLoading: { ...state.isLoading, categories: false },
            errors: { ...state.errors, categories: error.message },
          }));
          throw error;
        }
      },

      createCategory: async (categoryData) => {
        try {
          const category = await categoriesService.createCategory(categoryData);
          set((state) => ({
            categories: [...state.categories, category],
          }));
          return category;
        } catch (error: any) {
          set((state) => ({
            errors: { ...state.errors, categories: error.message },
          }));
          throw error;
        }
      },

      updateCategory: async (id, updates) => {
        try {
          const category = await categoriesService.updateCategory(id, updates);
          set((state) => ({
            categories: state.categories.map((c) =>
              c.id === id ? category : c
            ),
          }));
          return category;
        } catch (error: any) {
          set((state) => ({
            errors: { ...state.errors, categories: error.message },
          }));
          throw error;
        }
      },

      deleteCategory: async (id) => {
        try {
          await categoriesService.deleteCategory(id);
          set((state) => ({
            categories: state.categories.filter((c) => c.id !== id),
          }));
        } catch (error: any) {
          set((state) => ({
            errors: { ...state.errors, categories: error.message },
          }));
          throw error;
        }
      },

      // Invoice actions (continuing in next part due to length)
      fetchInvoices: async (params = {}) => {
        set((state) => ({
          isLoading: { ...state.isLoading, invoices: true },
          errors: { ...state.errors, invoices: null },
        }));

        try {
          const response = await invoicesService.getInvoices(params);
          set((state) => ({
            invoices: response.data,
            pagination: {
              ...state.pagination,
              invoices: {
                page: response.pagination.page,
                totalPages: response.pagination.totalPages,
                total: response.pagination.total,
              },
            },
            isLoading: { ...state.isLoading, invoices: false },
          }));
        } catch (error: any) {
          set((state) => ({
            isLoading: { ...state.isLoading, invoices: false },
            errors: { ...state.errors, invoices: error.message },
          }));
          throw error;
        }
      },

      createInvoice: async (invoiceData) => {
        try {
          const invoice = await invoicesService.createInvoice(invoiceData);
          set((state) => ({
            invoices: [invoice, ...state.invoices],
          }));
          return invoice;
        } catch (error: any) {
          set((state) => ({
            errors: { ...state.errors, invoices: error.message },
          }));
          throw error;
        }
      },

      updateInvoice: async (id, updates) => {
        try {
          const invoice = await invoicesService.updateInvoice(id, updates);
          set((state) => ({
            invoices: state.invoices.map((i) =>
              i.id === id ? invoice : i
            ),
          }));
          return invoice;
        } catch (error: any) {
          set((state) => ({
            errors: { ...state.errors, invoices: error.message },
          }));
          throw error;
        }
      },

      deleteInvoice: async (id) => {
        try {
          await invoicesService.deleteInvoice(id);
          set((state) => ({
            invoices: state.invoices.filter((i) => i.id !== id),
          }));
        } catch (error: any) {
          set((state) => ({
            errors: { ...state.errors, invoices: error.message },
          }));
          throw error;
        }
      },

      submitInvoiceToZRA: async (id) => {
        try {
          await invoicesService.submitToZRA(id);
          // Refresh the invoice to get updated ZRA status
          const invoice = await invoicesService.getInvoice(id);
          set((state) => ({
            invoices: state.invoices.map((i) =>
              i.id === id ? invoice : i
            ),
          }));
        } catch (error: any) {
          set((state) => ({
            errors: { ...state.errors, invoices: error.message },
          }));
          throw error;
        }
      },

      markInvoiceAsPaid: async (id, paymentDetails) => {
        try {
          const invoice = await invoicesService.markAsPaid(id, paymentDetails);
          set((state) => ({
            invoices: state.invoices.map((i) =>
              i.id === id ? invoice : i
            ),
          }));
          return invoice;
        } catch (error: any) {
          set((state) => ({
            errors: { ...state.errors, invoices: error.message },
          }));
          throw error;
        }
      },

      // Client actions
      fetchClients: async () => {
        set((state) => ({
          isLoading: { ...state.isLoading, clients: true },
          errors: { ...state.errors, clients: null },
        }));

        try {
          const clients = await clientsService.getClients();
          set((state) => ({
            clients,
            isLoading: { ...state.isLoading, clients: false },
          }));
        } catch (error: any) {
          set((state) => ({
            isLoading: { ...state.isLoading, clients: false },
            errors: { ...state.errors, clients: error.message },
          }));
          throw error;
        }
      },

      createClient: async (clientData) => {
        try {
          const client = await clientsService.createClient(clientData);
          set((state) => ({
            clients: [...state.clients, client],
          }));
          return client;
        } catch (error: any) {
          set((state) => ({
            errors: { ...state.errors, clients: error.message },
          }));
          throw error;
        }
      },

      updateClient: async (id, updates) => {
        try {
          const client = await clientsService.updateClient(id, updates);
          set((state) => ({
            clients: state.clients.map((c) =>
              c.id === id ? client : c
            ),
          }));
          return client;
        } catch (error: any) {
          set((state) => ({
            errors: { ...state.errors, clients: error.message },
          }));
          throw error;
        }
      },

      deleteClient: async (id) => {
        try {
          await clientsService.deleteClient(id);
          set((state) => ({
            clients: state.clients.filter((c) => c.id !== id),
          }));
        } catch (error: any) {
          set((state) => ({
            errors: { ...state.errors, clients: error.message },
          }));
          throw error;
        }
      },

      // Dashboard and Reports
      fetchDashboardData: async () => {
        set((state) => ({
          isLoading: { ...state.isLoading, dashboard: true },
          errors: { ...state.errors, dashboard: null },
        }));

        try {
          const dashboardData = await reportsService.getDashboardData();
          set((state) => ({
            dashboardData,
            isLoading: { ...state.isLoading, dashboard: false },
          }));
        } catch (error: any) {
          set((state) => ({
            isLoading: { ...state.isLoading, dashboard: false },
            errors: { ...state.errors, dashboard: error.message },
          }));
          throw error;
        }
      },

      fetchFinancialSummary: async (params = {}) => {
        set((state) => ({
          isLoading: { ...state.isLoading, summary: true },
          errors: { ...state.errors, summary: null },
        }));

        try {
          const financialSummary = await reportsService.getFinancialSummary(params);
          set((state) => ({
            financialSummary,
            isLoading: { ...state.isLoading, summary: false },
          }));
        } catch (error: any) {
          set((state) => ({
            isLoading: { ...state.isLoading, summary: false },
            errors: { ...state.errors, summary: error.message },
          }));
          throw error;
        }
      },

      // Utilities
      clearErrors: () => {
        set((state) => ({
          errors: {
            transactions: null,
            accounts: null,
            categories: null,
            invoices: null,
            clients: null,
            dashboard: null,
            summary: null,
          },
        }));
      },

      setLoading: (key, loading) => {
        set((state) => ({
          isLoading: { ...state.isLoading, [key]: loading },
        }));
      },

      setError: (key, error) => {
        set((state) => ({
          errors: { ...state.errors, [key]: error },
        }));
      },
    })),
    {
      name: 'financial-store',
    }
  )
);

// Selectors for common use cases
export const financialSelectors = {
  // Loading states
  isLoading: (state: FinancialState) => 
    Object.values(state.isLoading).some(loading => loading),
  isLoadingTransactions: (state: FinancialState) => state.isLoading.transactions,
  isLoadingAccounts: (state: FinancialState) => state.isLoading.accounts,
  isLoadingInvoices: (state: FinancialState) => state.isLoading.invoices,
  
  // Error states
  hasErrors: (state: FinancialState) => 
    Object.values(state.errors).some(error => error !== null),
  
  // Data availability
  hasTransactions: (state: FinancialState) => state.transactions.length > 0,
  hasAccounts: (state: FinancialState) => state.accounts.length > 0,
  hasInvoices: (state: FinancialState) => state.invoices.length > 0,
  hasClients: (state: FinancialState) => state.clients.length > 0,
  
  // Connected accounts
  connectedAccounts: (state: FinancialState) => 
    state.accounts.filter(account => account.connectionStatus === 'CONNECTED'),
  
  // Recent data
  recentTransactions: (state: FinancialState) => 
    state.transactions.slice(0, 10),
  pendingInvoices: (state: FinancialState) => 
    state.invoices.filter(invoice => 
      ['DRAFT', 'PENDING_SUBMISSION'].includes(invoice.status)
    ),
  overdueInvoices: (state: FinancialState) => 
    state.invoices.filter(invoice => invoice.status === 'OVERDUE'),
  
  // Categories by type
  incomeCategories: (state: FinancialState) => 
    state.categories.filter(category => category.type === 'INCOME'),
  expenseCategories: (state: FinancialState) => 
    state.categories.filter(category => category.type === 'EXPENSE'),
};
