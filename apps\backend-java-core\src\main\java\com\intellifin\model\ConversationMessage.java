package com.intellifin.model;

import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.CreationTimestamp;

import java.time.LocalDateTime;
import java.util.UUID;

@Entity
@Table(name = "conversation_messages")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ConversationMessage {

    @Id
    @GeneratedValue(strategy = GenerationType.UUID)
    private UUID id;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "conversation_id", nullable = false)
    private Conversation conversation;

    @Enumerated(EnumType.STRING)
    @Column(nullable = false)
    private MessageType type;

    @Enumerated(EnumType.STRING)
    @Column(nullable = false)
    private MessageRole role;

    @Column(nullable = false, columnDefinition = "TEXT")
    private String content;

    @Column(name = "processed_content", columnDefinition = "jsonb")
    private String processedContent;

    @Column(name = "intent_recognized")
    private String intentRecognized;

    @Column(name = "confidence_score")
    private Double confidenceScore;

    @Column(name = "processing_time_ms")
    private Long processingTimeMs;

    @Enumerated(EnumType.STRING)
    @Column(nullable = false)
    @Builder.Default
    private MessageStatus status = MessageStatus.PENDING;

    @Column(name = "error_message")
    private String errorMessage;

    @Column(columnDefinition = "jsonb")
    private String metadata;

    @CreationTimestamp
    @Column(nullable = false, updatable = false)
    private LocalDateTime createdAt;

    public enum MessageType {
        COMMAND,
        RESPONSE,
        ERROR,
        PROGRESS,
        SYSTEM
    }

    public enum MessageRole {
        USER,
        ASSISTANT,
        SYSTEM
    }

    public enum MessageStatus {
        PENDING,
        PROCESSING,
        COMPLETED,
        FAILED
    }

    // Helper methods
    public boolean isUserMessage() {
        return MessageRole.USER.equals(role);
    }

    public boolean isAssistantMessage() {
        return MessageRole.ASSISTANT.equals(role);
    }

    public boolean isSystemMessage() {
        return MessageRole.SYSTEM.equals(role);
    }

    public boolean isCommand() {
        return MessageType.COMMAND.equals(type);
    }

    public boolean isResponse() {
        return MessageType.RESPONSE.equals(type);
    }

    public boolean isError() {
        return MessageType.ERROR.equals(type);
    }

    public boolean isPending() {
        return MessageStatus.PENDING.equals(status);
    }

    public boolean isProcessing() {
        return MessageStatus.PROCESSING.equals(status);
    }

    public boolean isCompleted() {
        return MessageStatus.COMPLETED.equals(status);
    }

    public boolean isFailed() {
        return MessageStatus.FAILED.equals(status);
    }

    public void markAsProcessing() {
        this.status = MessageStatus.PROCESSING;
    }

    public void markAsCompleted() {
        this.status = MessageStatus.COMPLETED;
    }

    public void markAsFailed(String errorMessage) {
        this.status = MessageStatus.FAILED;
        this.errorMessage = errorMessage;
    }

    public void setProcessingResult(String intentRecognized, Double confidenceScore, Long processingTimeMs) {
        this.intentRecognized = intentRecognized;
        this.confidenceScore = confidenceScore;
        this.processingTimeMs = processingTimeMs;
    }
}
