package com.intellifin.service;

import com.intellifin.dto.auth.AuthResponse;
import com.intellifin.dto.auth.LoginRequest;
import com.intellifin.dto.auth.RegisterRequest;
import com.intellifin.dto.user.UserDto;
import com.intellifin.exception.AuthenticationException;
import com.intellifin.exception.ValidationException;
import com.intellifin.model.EmailVerificationToken;
import com.intellifin.model.PasswordResetToken;
import com.intellifin.model.User;
import com.intellifin.repository.EmailVerificationTokenRepository;
import com.intellifin.repository.PasswordResetTokenRepository;
import com.intellifin.repository.UserRepository;
import com.intellifin.security.JwtTokenUtil;
import com.intellifin.security.UserPrincipal;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.authentication.BadCredentialsException;
import org.springframework.security.authentication.DisabledException;
import org.springframework.security.authentication.LockedException;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.security.SecureRandom;
import java.time.LocalDateTime;
import java.util.Base64;
import java.util.Optional;
import java.util.UUID;

@Service
@RequiredArgsConstructor
@Slf4j
@Transactional
public class AuthService {

    private final UserRepository userRepository;
    private final EmailVerificationTokenRepository emailVerificationTokenRepository;
    private final PasswordResetTokenRepository passwordResetTokenRepository;
    private final PasswordEncoder passwordEncoder;
    private final JwtTokenUtil jwtTokenUtil;
    private final AuthenticationManager authenticationManager;
    private final EmailService emailService;

    private static final int TOKEN_LENGTH = 32;
    private static final int EMAIL_VERIFICATION_EXPIRY_HOURS = 24;
    private static final int PASSWORD_RESET_EXPIRY_HOURS = 1;

    public AuthResponse register(RegisterRequest request) {
        log.info("Attempting to register user with email: {}", request.getEmail());

        // Validate request
        validateRegistrationRequest(request);

        // Check if user already exists
        if (userRepository.existsByEmail(request.getEmail())) {
            throw new ValidationException("User with this email already exists");
        }

        // Create user
        User user = User.builder()
                .email(request.getEmail().toLowerCase().trim())
                .passwordHash(passwordEncoder.encode(request.getPassword()))
                .firstName(request.getFirstName().trim())
                .lastName(request.getLastName().trim())
                .organizationName(request.getOrganizationName() != null ? request.getOrganizationName().trim() : null)
                .tpin(request.getTpin() != null ? request.getTpin().trim() : null)
                .onboardingStatus(User.OnboardingStatus.STARTED)
                .emailVerified(true) // Temporarily set to true for testing
                .accountLocked(false)
                .failedLoginAttempts(0)
                .build();

        user = userRepository.save(user);
        log.info("User registered successfully with ID: {}", user.getId());

        // Generate email verification token
        String verificationToken = generateEmailVerificationToken(user);
        
        // Send verification email
        emailService.sendEmailVerification(user.getEmail(), user.getFullName(), verificationToken);

        // Generate JWT token
        UserPrincipal userPrincipal = UserPrincipal.create(user);
        String jwtToken = jwtTokenUtil.generateToken(
                userPrincipal, 
                user.getId(), 
                user.getEmail(), 
                user.getFullName()
        );

        return AuthResponse.registration(
                jwtToken,
                jwtTokenUtil.getExpirationTime(),
                jwtTokenUtil.getExpirationDateTime(),
                UserDto.fromUser(user)
        );
    }

    public AuthResponse login(LoginRequest request) {
        log.info("Attempting to login user with email: {}", request.getEmail());

        try {
            // Find user
            User user = userRepository.findByEmail(request.getEmail().toLowerCase().trim())
                    .orElseThrow(() -> new BadCredentialsException("Invalid email or password"));

            // Check if account is locked
            if (!user.isAccountNonLocked()) {
                throw new LockedException("Account is locked due to multiple failed login attempts");
            }

            // Authenticate
            Authentication authentication = authenticationManager.authenticate(
                    new UsernamePasswordAuthenticationToken(request.getEmail(), request.getPassword())
            );

            // Reset failed login attempts on successful authentication
            user.resetFailedLoginAttempts();
            userRepository.save(user);

            // Generate JWT token
            UserPrincipal userPrincipal = (UserPrincipal) authentication.getPrincipal();
            Long tokenExpiration = request.getRememberMe() ? 
                    jwtTokenUtil.getExpirationTime() * 7 : // 7 days for remember me
                    jwtTokenUtil.getExpirationTime(); // 24 hours default

            String jwtToken = jwtTokenUtil.generateToken(
                    userPrincipal, 
                    user.getId(), 
                    user.getEmail(), 
                    user.getFullName(),
                    tokenExpiration
            );

            log.info("User logged in successfully: {}", user.getEmail());

            return AuthResponse.success(
                    jwtToken,
                    tokenExpiration,
                    LocalDateTime.now().plusSeconds(tokenExpiration),
                    UserDto.fromUser(user)
            );

        } catch (BadCredentialsException e) {
            // Handle failed login attempt
            handleFailedLoginAttempt(request.getEmail());
            throw new AuthenticationException("Invalid email or password");
        } catch (LockedException e) {
            throw new AuthenticationException("Account is locked. Please try again later or reset your password.");
        } catch (DisabledException e) {
            throw new AuthenticationException("Account is disabled. Please verify your email address.");
        }
    }

    public void verifyEmail(String token) {
        log.info("Attempting to verify email with token");

        EmailVerificationToken verificationToken = emailVerificationTokenRepository
                .findValidToken(token, LocalDateTime.now())
                .orElseThrow(() -> new ValidationException("Invalid or expired verification token"));

        User user = verificationToken.getUser();
        user.setEmailVerified(true);
        userRepository.save(user);

        verificationToken.markAsUsed();
        emailVerificationTokenRepository.save(verificationToken);

        log.info("Email verified successfully for user: {}", user.getEmail());
    }

    public void resendEmailVerification(String email) {
        log.info("Resending email verification for: {}", email);

        User user = userRepository.findByEmail(email.toLowerCase().trim())
                .orElseThrow(() -> new ValidationException("User not found"));

        if (user.getEmailVerified()) {
            throw new ValidationException("Email is already verified");
        }

        // Invalidate existing tokens
        emailVerificationTokenRepository.deleteAllByUser(user);

        // Generate new token
        String verificationToken = generateEmailVerificationToken(user);
        
        // Send verification email
        emailService.sendEmailVerification(user.getEmail(), user.getFullName(), verificationToken);
    }

    public void requestPasswordReset(String email) {
        log.info("Password reset requested for: {}", email);

        Optional<User> userOpt = userRepository.findByEmail(email.toLowerCase().trim());
        
        // Always return success to prevent email enumeration
        if (userOpt.isEmpty()) {
            log.warn("Password reset requested for non-existent email: {}", email);
            return;
        }

        User user = userOpt.get();

        // Invalidate existing password reset tokens
        passwordResetTokenRepository.deleteAllByUser(user);

        // Generate password reset token
        String resetToken = generatePasswordResetToken(user);
        
        // Send password reset email
        emailService.sendPasswordReset(user.getEmail(), user.getFullName(), resetToken);
    }

    public void resetPassword(String token, String newPassword) {
        log.info("Attempting to reset password with token");

        PasswordResetToken resetToken = passwordResetTokenRepository
                .findValidToken(token, LocalDateTime.now())
                .orElseThrow(() -> new ValidationException("Invalid or expired reset token"));

        User user = resetToken.getUser();
        user.setPasswordHash(passwordEncoder.encode(newPassword));
        user.setAccountLocked(false);
        user.setFailedLoginAttempts(0);
        user.setLockedUntil(null);
        userRepository.save(user);

        resetToken.markAsUsed();
        passwordResetTokenRepository.save(resetToken);

        // Invalidate all other reset tokens for this user
        passwordResetTokenRepository.deleteAllByUser(user);

        log.info("Password reset successfully for user: {}", user.getEmail());
    }

    public AuthResponse refreshToken(String authHeader) {
        log.info("Refreshing JWT token");

        // Extract token from Authorization header
        String token = authHeader.replace("Bearer ", "");

        // Validate the current token and extract user information
        if (!jwtTokenUtil.validateToken(token)) {
            throw new AuthenticationException("Invalid or expired token");
        }

        String email = jwtTokenUtil.getEmailFromToken(token);
        User user = userRepository.findByEmail(email)
                .orElseThrow(() -> new AuthenticationException("User not found"));

        // Check if user account is still active
        if (!user.isAccountNonLocked()) {
            throw new AuthenticationException("Account is locked");
        }

        // Generate new JWT token
        UserPrincipal userPrincipal = UserPrincipal.create(user);
        String newJwtToken = jwtTokenUtil.generateToken(
                userPrincipal,
                user.getId(),
                user.getEmail(),
                user.getFullName()
        );

        log.info("Token refreshed successfully for user: {}", user.getEmail());

        return AuthResponse.success(
                newJwtToken,
                jwtTokenUtil.getExpirationTime(),
                jwtTokenUtil.getExpirationDateTime(),
                UserDto.fromUser(user)
        );
    }

    public void logout(String authHeader) {
        log.info("Processing logout request");

        // Extract token from Authorization header
        String token = authHeader.replace("Bearer ", "");

        // Validate the token
        if (!jwtTokenUtil.validateToken(token)) {
            log.warn("Logout attempted with invalid token");
            return; // Don't throw exception for logout
        }

        String email = jwtTokenUtil.getEmailFromToken(token);

        // TODO: Implement token blacklisting in Redis for immediate invalidation
        // For now, we'll rely on token expiration
        // redisTemplate.opsForSet().add("blacklisted_tokens", token);
        // redisTemplate.expire("blacklisted_tokens", Duration.ofSeconds(jwtTokenUtil.getExpirationTime()));

        log.info("User logged out successfully: {}", email);
    }

    private void validateRegistrationRequest(RegisterRequest request) {
        if (!request.isPasswordConfirmed()) {
            throw new ValidationException("Password confirmation does not match");
        }
    }

    private void handleFailedLoginAttempt(String email) {
        userRepository.findByEmail(email.toLowerCase().trim()).ifPresent(user -> {
            user.incrementFailedLoginAttempts();
            userRepository.save(user);
            log.warn("Failed login attempt for user: {}. Attempts: {}", email, user.getFailedLoginAttempts());
        });
    }

    private String generateEmailVerificationToken(User user) {
        String token = generateSecureToken();
        
        EmailVerificationToken verificationToken = EmailVerificationToken.builder()
                .token(token)
                .user(user)
                .expiresAt(LocalDateTime.now().plusHours(EMAIL_VERIFICATION_EXPIRY_HOURS))
                .build();

        emailVerificationTokenRepository.save(verificationToken);
        return token;
    }

    private String generatePasswordResetToken(User user) {
        String token = generateSecureToken();
        
        PasswordResetToken resetToken = PasswordResetToken.builder()
                .token(token)
                .user(user)
                .expiresAt(LocalDateTime.now().plusHours(PASSWORD_RESET_EXPIRY_HOURS))
                .build();

        passwordResetTokenRepository.save(resetToken);
        return token;
    }

    private String generateSecureToken() {
        SecureRandom random = new SecureRandom();
        byte[] bytes = new byte[TOKEN_LENGTH];
        random.nextBytes(bytes);
        return Base64.getUrlEncoder().withoutPadding().encodeToString(bytes);
    }
}
