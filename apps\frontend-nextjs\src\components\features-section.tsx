"use client"

import React from "react"
import { motion } from "framer-motion"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/components/ui/tabs"
import { Card, CardContent } from "@/components/ui/card"
import { AnimatedSection } from "@/components/ui/animated-section"
import {
  Smartphone,
  BarChart3,
  Settings,
  CheckCircle,
  TrendingUp,
  Users,
  Zap,
  Shield,
  Clock
} from "lucide-react"

const features = [
  {
    id: "track",
    title: "Track Mobile Money",
    icon: Smartphone,
    visual: {
      title: "Mobile Money Transactions",
      items: [
        { category: "Airtel Money", amount: "K2,450", status: "completed" },
        { category: "MTN Mobile Money", amount: "K1,890", status: "completed" },
        { category: "Zamtel Kwacha", amount: "K890", status: "pending" },
        { category: "Bank Transfer", amount: "K3,200", status: "completed" },
      ]
    },
    benefits: [
      { icon: CheckCircle, text: "Never miss a transaction - sync all mobile money automatically" },
      { icon: Zap, text: "Save 5+ hours weekly with AI-powered categorization" },
      { icon: Shield, text: "Bank-level security keeps your financial data safe" },
    ]
  },
  {
    id: "understand",
    title: "Understand Your Business",
    icon: BarChart3,
    visual: {
      title: "Business Insights Dashboard",
      items: [
        { category: "Revenue Growth", amount: "+23.5%", status: "positive" },
        { category: "Customer Acquisition", amount: "+15.2%", status: "positive" },
        { category: "Operating Costs", amount: "-8.1%", status: "negative" },
        { category: "Profit Margin", amount: "18.7%", status: "neutral" },
      ]
    },
    benefits: [
      { icon: TrendingUp, text: "Spot profit opportunities before your competitors do" },
      { icon: BarChart3, text: "Make confident decisions with real-time insights" },
      { icon: Clock, text: "Get business reports in seconds, not days" },
    ]
  },
  {
    id: "standardize",
    title: "Automate & Scale",
    icon: Settings,
    visual: {
      title: "ZRA-Compliant Automation",
      items: [
        { category: "Smart Invoices", amount: "ZRA Ready", status: "active" },
        { category: "VAT Calculations", amount: "Auto", status: "active" },
        { category: "TPIN Validation", amount: "Verified", status: "active" },
        { category: "Tax Returns", amount: "Generated", status: "active" },
      ]
    },
    benefits: [
      { icon: Zap, text: "Generate ZRA-compliant invoices in under 30 seconds" },
      { icon: Users, text: "Scale your business without hiring more staff" },
      { icon: Shield, text: "Never worry about ZRA compliance again" },
    ]
  }
]

export function FeaturesSection() {
  return (
    <section className="py-24 bg-gray-50">
      <div className="container mx-auto px-6">
        <AnimatedSection className="text-center mb-16">
          <h2 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
            Accelerate & automate your finances
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            Transform your financial operations with intelligent automation and real-time insights
          </p>
        </AnimatedSection>

        <AnimatedSection delay={0.2}>
          <Tabs defaultValue="track" className="max-w-6xl mx-auto">
            <TabsList className="grid w-full grid-cols-3 mb-12 bg-white/10 backdrop-blur-sm p-2 rounded-xl border border-white/20">
              {features.map((feature) => (
                <TabsTrigger
                  key={feature.id}
                  value={feature.id}
                  className="flex items-center space-x-2 px-6 py-3 rounded-lg data-[state=active]:bg-gradient-primary data-[state=active]:text-white data-[state=active]:shadow-lg text-gray-700 hover:text-gray-900 transition-all"
                >
                  <feature.icon className="w-4 h-4" />
                  <span className="font-medium">{feature.title}</span>
                </TabsTrigger>
              ))}
            </TabsList>

            {features.map((feature) => (
              <TabsContent key={feature.id} value={feature.id} className="mt-0">
                <div className="grid lg:grid-cols-2 gap-12 items-center">
                  {/* Visual */}
                  <div className="order-2 lg:order-1">
                    <Card className="shadow-xl border-0 bg-gradient-to-br from-white to-gray-50">
                      <CardContent className="p-8">
                        <h3 className="text-xl font-semibold text-gray-900 mb-6">
                          {feature.visual.title}
                        </h3>
                        <div className="space-y-4">
                          {feature.visual.items.map((item, index) => (
                            <motion.div
                              key={index}
                              initial={{ opacity: 0, x: -20 }}
                              animate={{ opacity: 1, x: 0 }}
                              transition={{ delay: index * 0.1 }}
                              className="flex items-center justify-between p-4 bg-gradient-to-r from-gray-50 to-white rounded-lg border border-gray-100 hover:shadow-md transition-all"
                            >
                              <span className="font-medium text-gray-900">
                                {item.category}
                              </span>
                              <div className="flex items-center space-x-2">
                                <span className={`font-semibold ${
                                  item.status === 'positive' ? 'text-secondary' :
                                  item.status === 'negative' ? 'text-red-500' :
                                  item.status === 'active' ? 'text-primary' :
                                  'text-gray-900'
                                }`}>
                                  {item.amount}
                                </span>
                                <div className={`w-2 h-2 rounded-full ${
                                  item.status === 'completed' || item.status === 'active' ? 'bg-secondary' :
                                  item.status === 'pending' ? 'bg-highlight' :
                                  item.status === 'positive' ? 'bg-secondary' :
                                  item.status === 'negative' ? 'bg-red-400' :
                                  'bg-accent'
                                }`} />
                              </div>
                            </motion.div>
                          ))}
                        </div>
                      </CardContent>
                    </Card>
                  </div>

                  {/* Benefits */}
                  <div className="order-1 lg:order-2">
                    <div className="space-y-6">
                      {feature.benefits.map((benefit, index) => (
                        <motion.div
                          key={index}
                          initial={{ opacity: 0, y: 20 }}
                          animate={{ opacity: 1, y: 0 }}
                          transition={{ delay: index * 0.2 }}
                          className="flex items-start space-x-4"
                        >
                          <div className="w-12 h-12 bg-gradient-primary rounded-xl flex items-center justify-center flex-shrink-0 shadow-lg">
                            <benefit.icon className="w-6 h-6 text-white" />
                          </div>
                          <div>
                            <p className="text-lg font-medium text-gray-900 leading-relaxed">
                              {benefit.text}
                            </p>
                          </div>
                        </motion.div>
                      ))}
                    </div>
                  </div>
                </div>
              </TabsContent>
            ))}
          </Tabs>


        </AnimatedSection>
      </div>
    </section>
  )
}
