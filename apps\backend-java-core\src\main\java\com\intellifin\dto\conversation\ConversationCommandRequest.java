package com.intellifin.dto.conversation;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Map;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ConversationCommandRequest {

    @NotBlank(message = "Command is required")
    @Size(min = 1, max = 1000, message = "Command must be between 1 and 1000 characters")
    private String command;

    private String sessionId;

    private String conversationId;

    @Builder.Default
    private Boolean expectResponse = true;

    @Builder.Default
    private Boolean saveToHistory = true;

    private Map<String, Object> context;

    private Map<String, Object> metadata;

    // Helper methods
    public boolean hasSessionId() {
        return sessionId != null && !sessionId.trim().isEmpty();
    }

    public boolean hasConversationId() {
        return conversationId != null && !conversationId.trim().isEmpty();
    }

    public boolean hasContext() {
        return context != null && !context.isEmpty();
    }

    public boolean hasMetadata() {
        return metadata != null && !metadata.isEmpty();
    }
}
