# IntelliFin PRD - Functional Requirements

## Functional Requirements (FR)

These describe the specific actions and capabilities IntelliFin *must* perform for our users.

### User Authentication & Account Management
*   **FR1:** The system must allow a new user to register for an account using their email address and a secure password.
*   **FR2:** The system must allow a registered user to log in securely using their email and password.

### Financial Account Integration
*   **FR3:** The system must provide a secure and clear interface for a user to authorize the connection to their MTN Mobile Money account during onboarding or from their settings.
*   **FR4:** The system must automatically fetch new transaction data from the user's connected MTN Mobile Money account in near real-time.

### AI-Powered Transaction Categorization
*   **FR5:** The system must use an AI model to automatically assign a preliminary business category (e.g., Sales, Supplies, Transport, Personal Drawing) to each fetched transaction.
*   **FR6:** The system must present the categorized transactions to the user in a simple, clean interface that flags transactions needing review.
*   **FR7:** The system must allow the user to approve the AI's suggested category for a transaction with a single action (e.g., a "Confirm" button).
*   **FR8:** The system must allow the user to easily override the AI's suggestion and re-categorize a transaction by selecting from a predefined list of business categories.

### Conversational Interface
*   **FR9:** The system must provide a persistent, text-based conversational command bar as the primary method for user interaction.

### ZRA Invoice Management
*   **FR10:** The system must be able to process a natural language command from the user to initiate the creation of a ZRA-compliant Smart Invoice.
*   **FR11:** Upon receiving an invoice creation command, the system must generate a draft of the Smart Invoice, pre-filled with the necessary details, and present it to the user for review and final approval.
*   **FR12:** Upon user approval, the system must submit the finalized invoice data to the central ZRA VSDC component.

### Financial Reporting
*   **FR13:** The system must be able to process a natural language command from the user to request a basic financial summary (Total Sales, Total Expenses, Net Profit/Loss) for a specific period (e.g., "this week," "this month").
*   **FR14:** The system must display the requested financial summary to the user in a simple, clear, and easy-to-understand format within the main workspace.

---

**Previous Section:** [Goals and Background Context](../prd/01-goals-and-background.md)  
**Next Section:** [Non-Functional Requirements](../prd/03-non-functional-requirements.md) 