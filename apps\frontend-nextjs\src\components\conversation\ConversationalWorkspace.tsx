"use client"

import React, { useState, useRef, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  Send, 
  Mic, 
  Sparkles, 
  Loader2, 
  AlertCircle, 
  User,
  MessageSquare,
  ArrowUp
} from 'lucide-react';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { useAuthStore, useConversationStore } from '@/stores';
import { formatRelativeTime } from '@/utils';
import { WorkspaceResults } from './WorkspaceResults';
import { conversationalCommandService } from '@/services/conversational-commands';

export function ConversationalWorkspace() {
  const { user } = useAuthStore();
  const {
    isConnected,
    isConnecting,
    connectionError,
    isTyping,
    awaitingResponse,
    addUserMessage,
    currentSessionId
  } = useConversationStore();
  
  const [input, setInput] = useState('');
  const [showResults, setShowResults] = useState(false);
  const [currentTask, setCurrentTask] = useState<string | null>(null);
  const inputRef = useRef<HTMLInputElement>(null);

  // Focus input on mount
  useEffect(() => {
    inputRef.current?.focus();
  }, []);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!input.trim() || awaitingResponse || !isConnected) {
      return;
    }

    const command = input.trim();

    // Add user message to conversation
    const userMessage = addUserMessage(command);

    // Show results area when user sends a message
    setShowResults(true);
    setCurrentTask(command);

    setInput('');

    try {
      // Process command through the conversational service
      const result = await conversationalCommandService.processCommand(command, user?.id || 'anonymous');

      if (result.success && result.response) {
        // Add AI response to conversation
        // TODO: This will be handled by the WebSocket message processing
        console.log('AI Response:', result.response);
        console.log('Intent:', result.intent);
        console.log('Data:', result.data);
      } else {
        console.error('Command processing failed:', result.error);
      }
    } catch (error) {
      console.error('Error processing command:', error);
    }
  };

  const handleSuggestionClick = async (suggestion: string) => {
    if (!awaitingResponse && isConnected) {
      // Add user message to conversation
      addUserMessage(suggestion);
      setShowResults(true);
      setCurrentTask(suggestion);

      try {
        // Process suggestion through the conversational service
        const result = await conversationalCommandService.processCommand(suggestion, user?.id || 'anonymous');

        if (result.success && result.response) {
          // Add AI response to conversation
          // TODO: This will be handled by the WebSocket message processing
          console.log('AI Response:', result.response);
          console.log('Intent:', result.intent);
          console.log('Data:', result.data);
        } else {
          console.error('Suggestion processing failed:', result.error);
        }
      } catch (error) {
        console.error('Error processing suggestion:', error);
      }
    }
  };

  const suggestions = conversationalCommandService.getCommandSuggestions();
  const firstName = user?.firstName || 'there';

  return (
    <div className="h-full flex flex-col bg-white rounded-lg border border-gray-200 shadow-sm">
      {/* Command Bar */}
      <div className="p-4 border-b border-gray-200">
        <form onSubmit={handleSubmit} className="flex items-center space-x-3">
          <div className="flex-1 relative">
            <Input
              ref={inputRef}
              type="text"
              value={input}
              onChange={(e) => setInput(e.target.value)}
              placeholder={isConnected ? "Ask me anything about your business..." : "Connecting..."}
              disabled={!isConnected || awaitingResponse}
              className="pr-12 h-12 text-base"
            />
            <Button
              type="button"
              variant="ghost"
              size="sm"
              className="absolute right-2 top-1/2 transform -translate-y-1/2 h-8 w-8 p-0"
              disabled={!isConnected}
            >
              <Mic className="h-4 w-4" />
            </Button>
          </div>
          <Button
            type="submit"
            disabled={!input.trim() || !isConnected || awaitingResponse}
            className="bg-gradient-primary hover:opacity-90 h-12 px-6"
          >
            {awaitingResponse ? (
              <Loader2 className="h-4 w-4 animate-spin" />
            ) : (
              <ArrowUp className="h-4 w-4" />
            )}
          </Button>
        </form>
        
        {!isConnected && (
          <div className="text-center mt-2">
            <span className="text-sm text-red-600">
              {isConnecting ? 'Connecting to AI assistant...' : 'Connection lost. Attempting to reconnect...'}
            </span>
          </div>
        )}
      </div>

      {/* Main Content Area */}
      <div className="flex-1 min-h-0">
        {/* Connection Error */}
        {connectionError && (
          <motion.div
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            className="m-4 bg-red-50 border border-red-200 rounded-lg p-3"
          >
            <div className="flex items-center space-x-2">
              <AlertCircle className="w-4 h-4 text-red-500" />
              <span className="text-sm text-red-700">Connection Error: {connectionError}</span>
            </div>
          </motion.div>
        )}

        {/* Welcome State - When no active task */}
        {!showResults && !connectionError && (
            <motion.div
              initial={{ opacity: 0, scale: 0.95 }}
              animate={{ opacity: 1, scale: 1 }}
              className="flex-1 flex items-center justify-center p-8"
            >
              <div className="text-center max-w-2xl">
                <div className="w-20 h-20 bg-gradient-primary/10 rounded-full flex items-center justify-center mx-auto mb-6">
                  <Sparkles className="w-10 h-10 text-primary" />
                </div>
                <h2 className="text-3xl font-bold text-gray-900 mb-4">
                  Hi {firstName}! 👋
                </h2>
                <p className="text-lg text-gray-600 mb-8">
                  I'm your AI accounting assistant. Ask me anything about your business finances!
                </p>
                
                {/* Command Suggestions */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-3 max-w-2xl mx-auto">
                  {suggestions.slice(0, 6).map((suggestion, index) => (
                    <motion.button
                      key={suggestion.text}
                      initial={{ opacity: 0, y: 10 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ delay: index * 0.1 }}
                      onClick={() => handleSuggestionClick(suggestion.text)}
                      disabled={!isConnected || awaitingResponse}
                      className="text-left p-4 bg-gray-50 hover:bg-gray-100 rounded-lg border border-gray-200 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                      <div className="flex items-center space-x-3">
                        <span className="text-2xl">{suggestion.icon}</span>
                        <div>
                          <p className="font-medium text-gray-900">{suggestion.text}</p>
                          <p className="text-sm text-gray-500">{suggestion.description}</p>
                        </div>
                      </div>
                    </motion.button>
                  ))}
                </div>
              </div>
            </motion.div>
          )}

        {/* Active Task Results Area */}
        <AnimatePresence>
          {showResults && (
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
              transition={{ duration: 0.3 }}
              className="flex-1 p-4"
            >
              {/* Current Task Header */}
              <div className="mb-4">
                <div className="flex items-center space-x-3 mb-2">
                  <div className="w-8 h-8 bg-gradient-primary rounded-full flex items-center justify-center">
                    <Sparkles className="w-4 h-4 text-white" />
                  </div>
                  <div>
                    <h3 className="font-semibold text-gray-900">Working on your request</h3>
                    <p className="text-sm text-gray-600">{currentTask}</p>
                  </div>
                </div>

                {(isTyping || awaitingResponse) && (
                  <div className="flex items-center space-x-2 text-sm text-gray-500">
                    <div className="flex space-x-1">
                      <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" />
                      <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0.1s' }} />
                      <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0.2s' }} />
                    </div>
                    <span>AI is processing your request...</span>
                  </div>
                )}
              </div>

              {/* Results Content */}
              <WorkspaceResults currentTask={currentTask} />
            </motion.div>
          )}
        </AnimatePresence>
      </div>
    </div>
  );
}
