/**
 * API Services Index
 * Central export point for all API services
 */

// Export API client and utilities
export { apiClient, TokenManager } from './client';

// Export authentication services
export { AuthService, authService } from './auth';

// Export transaction services
export { TransactionsService, CategoriesService, transactionsService, categoriesService } from './transactions';

// Export financial account services
export { AccountsService, accountsService } from './accounts';

// Export invoice and client services
export { InvoicesService, ClientsService, invoicesService, clientsService } from './invoices';

// Export reporting services
export { ReportsService, reportsService } from './reports';

// Export all types
export type * from '@/types/api';
export type * from '@/types/conversation';
export type * from '@/types/financial';

/**
 * Centralized API service object for easy access
 * Usage: import { api } from '@/services/api';
 */
// Temporarily commented out to debug
// export const api = {
//   auth: AuthService,
//   transactions: TransactionsService,
//   categories: CategoriesService,
//   accounts: AccountsService,
//   invoices: InvoicesService,
//   clients: ClientsService,
//   reports: ReportsService,
// };

/**
 * API Configuration
 */
export const API_CONFIG = {
  BASE_URL: process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:8080',
  TIMEOUT: 30000,
  RETRY_ATTEMPTS: 3,
  RETRY_DELAY: 1000,
} as const;

/**
 * API Error Codes
 */
export const API_ERROR_CODES = {
  // Authentication errors
  INVALID_CREDENTIALS: 'INVALID_CREDENTIALS',
  TOKEN_EXPIRED: 'TOKEN_EXPIRED',
  TOKEN_INVALID: 'TOKEN_INVALID',
  UNAUTHORIZED: 'UNAUTHORIZED',
  FORBIDDEN: 'FORBIDDEN',
  
  // Validation errors
  VALIDATION_ERROR: 'VALIDATION_ERROR',
  MISSING_REQUIRED_FIELD: 'MISSING_REQUIRED_FIELD',
  INVALID_FORMAT: 'INVALID_FORMAT',
  
  // Business logic errors
  DUPLICATE_ENTRY: 'DUPLICATE_ENTRY',
  RESOURCE_NOT_FOUND: 'RESOURCE_NOT_FOUND',
  INSUFFICIENT_PERMISSIONS: 'INSUFFICIENT_PERMISSIONS',
  OPERATION_NOT_ALLOWED: 'OPERATION_NOT_ALLOWED',
  
  // External service errors
  EXTERNAL_SERVICE_ERROR: 'EXTERNAL_SERVICE_ERROR',
  MTN_API_ERROR: 'MTN_API_ERROR',
  STITCH_API_ERROR: 'STITCH_API_ERROR',
  ZRA_API_ERROR: 'ZRA_API_ERROR',
  
  // System errors
  INTERNAL_SERVER_ERROR: 'INTERNAL_SERVER_ERROR',
  SERVICE_UNAVAILABLE: 'SERVICE_UNAVAILABLE',
  NETWORK_ERROR: 'NETWORK_ERROR',
  TIMEOUT_ERROR: 'TIMEOUT_ERROR',
  
  // Rate limiting
  RATE_LIMIT_EXCEEDED: 'RATE_LIMIT_EXCEEDED',
  
  // Data integrity
  DATA_INTEGRITY_ERROR: 'DATA_INTEGRITY_ERROR',
  CONCURRENT_MODIFICATION: 'CONCURRENT_MODIFICATION',
} as const;

/**
 * HTTP Status Codes
 */
export const HTTP_STATUS = {
  OK: 200,
  CREATED: 201,
  NO_CONTENT: 204,
  BAD_REQUEST: 400,
  UNAUTHORIZED: 401,
  FORBIDDEN: 403,
  NOT_FOUND: 404,
  CONFLICT: 409,
  UNPROCESSABLE_ENTITY: 422,
  TOO_MANY_REQUESTS: 429,
  INTERNAL_SERVER_ERROR: 500,
  BAD_GATEWAY: 502,
  SERVICE_UNAVAILABLE: 503,
  GATEWAY_TIMEOUT: 504,
} as const;

/**
 * API Response Status
 */
export const API_STATUS = {
  SUCCESS: 'success',
  ERROR: 'error',
  LOADING: 'loading',
  IDLE: 'idle',
} as const;

/**
 * Utility functions for API handling
 */
export const apiUtils = {
  /**
   * Check if error is a network error
   */
  isNetworkError: (error: any): boolean => {
    return error?.code === API_ERROR_CODES.NETWORK_ERROR || 
           error?.message?.includes('Network Error') ||
           error?.message?.includes('ERR_NETWORK');
  },

  /**
   * Check if error is an authentication error
   */
  isAuthError: (error: any): boolean => {
    return [
      API_ERROR_CODES.INVALID_CREDENTIALS,
      API_ERROR_CODES.TOKEN_EXPIRED,
      API_ERROR_CODES.TOKEN_INVALID,
      API_ERROR_CODES.UNAUTHORIZED
    ].includes(error?.code) || error?.status === HTTP_STATUS.UNAUTHORIZED;
  },

  /**
   * Check if error is a validation error
   */
  isValidationError: (error: any): boolean => {
    return error?.code === API_ERROR_CODES.VALIDATION_ERROR ||
           error?.status === HTTP_STATUS.BAD_REQUEST ||
           error?.status === HTTP_STATUS.UNPROCESSABLE_ENTITY;
  },

  /**
   * Check if error is a server error
   */
  isServerError: (error: any): boolean => {
    return error?.status >= 500 || 
           error?.code === API_ERROR_CODES.INTERNAL_SERVER_ERROR;
  },

  /**
   * Check if operation should be retried
   */
  shouldRetry: (error: any, attempt: number, maxAttempts: number): boolean => {
    if (attempt >= maxAttempts) return false;
    
    // Don't retry auth errors or validation errors
    if (apiUtils.isAuthError(error) || apiUtils.isValidationError(error)) {
      return false;
    }
    
    // Retry network errors and server errors
    return apiUtils.isNetworkError(error) || apiUtils.isServerError(error);
  },

  /**
   * Get user-friendly error message
   */
  getErrorMessage: (error: any): string => {
    if (apiUtils.isNetworkError(error)) {
      return 'Network error. Please check your internet connection and try again.';
    }
    
    if (apiUtils.isAuthError(error)) {
      return 'Authentication failed. Please log in again.';
    }
    
    if (apiUtils.isServerError(error)) {
      return 'Server error. Please try again later.';
    }
    
    return error?.message || 'An unexpected error occurred.';
  },

  /**
   * Format API error for display
   */
  formatError: (error: any) => ({
    message: apiUtils.getErrorMessage(error),
    code: error?.code || 'UNKNOWN_ERROR',
    status: error?.status,
    details: error?.details,
    retryable: apiUtils.shouldRetry(error, 0, 1),
  }),
};

/**
 * Default export for convenience
 */
// export default api;
