# Development-specific Docker Compose overrides
# This file is automatically loaded by docker-compose for development

version: '3.8'

services:
  # Development overrides for backend core
  backend-core:
    build:
      target: runtime
    volumes:
      - ./apps/backend-java-core/src:/app/src:ro
      - maven_cache:/root/.m2
    environment:
      - SPRING_PROFILES_ACTIVE=development
      - SPRING_DEVTOOLS_RESTART_ENABLED=true
      - LOGGING_LEVEL_COM_INTELLIFIN=DEBUG
      - LOGGING_LEVEL_ORG_SPRINGFRAMEWORK_WEB=DEBUG
    command: ["sh", "-c", "java -agentlib:jdwp=transport=dt_socket,server=y,suspend=n,address=*:5005 -jar app.jar"]

  # Development overrides for AI service
  ai-service:
    build:
      target: development
    volumes:
      - ./apps/backend-python-ai:/app
      - python_cache:/root/.cache
    environment:
      - ENVIRONMENT=development
      - LOG_LEVEL=DEBUG
      - PYTHONPATH=/app
      - WATCHFILES_FORCE_POLLING=true
    command: ["uvicorn", "src.main:app", "--host", "0.0.0.0", "--port", "8000", "--reload", "--log-level", "debug"]

  # Development overrides for ZRA service
  zra-service:
    build:
      target: runtime
    volumes:
      - ./apps/backend-java-zra/src:/app/src:ro
      - maven_cache:/root/.m2
    environment:
      - SPRING_PROFILES_ACTIVE=development
      - SPRING_DEVTOOLS_RESTART_ENABLED=true
      - LOGGING_LEVEL_COM_INTELLIFIN=DEBUG
    command: ["sh", "-c", "java -agentlib:jdwp=transport=dt_socket,server=y,suspend=n,address=*:5006 -jar app.jar"]

  # Development overrides for frontend
  frontend:
    build:
      target: runner
    volumes:
      - ./apps/frontend-nextjs:/app
      - /app/node_modules
      - /app/.next
    environment:
      - NODE_ENV=development
      - NEXT_TELEMETRY_DISABLED=1
      - WATCHPACK_POLLING=true
    command: ["npm", "run", "dev"]

  # Development database with exposed port
  postgres:
    ports:
      - "5432:5432"
    environment:
      - POSTGRES_DB=intellifin_dev
      - POSTGRES_USER=intellifin_user
      - POSTGRES_PASSWORD=intellifin_dev_password
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./docs/database/migrations:/docker-entrypoint-initdb.d:ro

  # Development Redis with exposed port
  redis:
    ports:
      - "6379:6379"
    command: redis-server --appendonly yes --appendfsync everysec

  # Development Ollama with model persistence
  ollama:
    ports:
      - "11434:11434"
    volumes:
      - ollama_data:/root/.ollama
    environment:
      - OLLAMA_HOST=0.0.0.0
      - OLLAMA_KEEP_ALIVE=24h

  # pgAdmin for database management (development only)
  pgadmin:
    ports:
      - "5050:80"
    environment:
      - PGADMIN_DEFAULT_EMAIL=<EMAIL>
      - PGADMIN_DEFAULT_PASSWORD=admin123
      - PGADMIN_CONFIG_SERVER_MODE=False
    volumes:
      - pgadmin_data:/var/lib/pgadmin

volumes:
  postgres_data:
  redis_data:
  ollama_data:
  maven_cache:
  python_cache:
  node_modules_cache:
  pgadmin_data:

networks:
  intellifin-network:
    driver: bridge
