#!/bin/bash

# IntelliFin Backend Test Script
# This script tests the backend authentication endpoints

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
API_BASE_URL="http://localhost:8080/api/v1"
TEST_EMAIL="<EMAIL>"
TEST_PASSWORD="TestPassword123!"
TEST_FIRST_NAME="Test"
TEST_LAST_NAME="User"
TEST_ORG_NAME="Test Organization"

# Function to print colored output
print_status() {
    echo -e "${BLUE}[TEST]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[PASS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

print_error() {
    echo -e "${RED}[FAIL]${NC} $1"
}

# Function to make HTTP request and check response
test_endpoint() {
    local method=$1
    local endpoint=$2
    local data=$3
    local expected_status=$4
    local description=$5
    local auth_header=$6

    print_status "Testing: $description"
    
    local curl_cmd="curl -s -w '%{http_code}' -X $method"
    
    if [ ! -z "$auth_header" ]; then
        curl_cmd="$curl_cmd -H 'Authorization: Bearer $auth_header'"
    fi
    
    if [ ! -z "$data" ]; then
        curl_cmd="$curl_cmd -H 'Content-Type: application/json' -d '$data'"
    fi
    
    curl_cmd="$curl_cmd $API_BASE_URL$endpoint"
    
    local response=$(eval $curl_cmd)
    local status_code="${response: -3}"
    local body="${response%???}"
    
    if [ "$status_code" = "$expected_status" ]; then
        print_success "$description (Status: $status_code)"
        return 0
    else
        print_error "$description (Expected: $expected_status, Got: $status_code)"
        if [ ! -z "$body" ]; then
            echo "Response: $body"
        fi
        return 1
    fi
}

# Function to extract token from response
extract_token() {
    local response=$1
    echo "$response" | grep -o '"token":"[^"]*"' | cut -d'"' -f4
}

# Main test function
main() {
    echo "=================================================="
    echo "       IntelliFin Backend API Tests"
    echo "=================================================="
    echo

    local passed=0
    local failed=0
    local jwt_token=""

    # Test 1: Health Check
    if test_endpoint "GET" "/actuator/health" "" "200" "Health check"; then
        ((passed++))
    else
        ((failed++))
    fi

    # Test 2: API Documentation
    if curl -s "http://localhost:8080/swagger-ui.html" > /dev/null; then
        print_success "API documentation is accessible"
        ((passed++))
    else
        print_error "API documentation is not accessible"
        ((failed++))
    fi

    # Test 3: User Registration
    local register_data="{
        \"email\": \"$TEST_EMAIL\",
        \"password\": \"$TEST_PASSWORD\",
        \"confirmPassword\": \"$TEST_PASSWORD\",
        \"firstName\": \"$TEST_FIRST_NAME\",
        \"lastName\": \"$TEST_LAST_NAME\",
        \"organizationName\": \"$TEST_ORG_NAME\"
    }"
    
    if test_endpoint "POST" "/auth/register" "$register_data" "201" "User registration"; then
        ((passed++))
    else
        ((failed++))
    fi

    # Test 4: User Login
    local login_data="{
        \"email\": \"$TEST_EMAIL\",
        \"password\": \"$TEST_PASSWORD\"
    }"
    
    print_status "Testing: User login"
    local login_response=$(curl -s -X POST \
        -H "Content-Type: application/json" \
        -d "$login_data" \
        "$API_BASE_URL/auth/login")
    
    local login_status=$(curl -s -w '%{http_code}' -o /dev/null -X POST \
        -H "Content-Type: application/json" \
        -d "$login_data" \
        "$API_BASE_URL/auth/login")
    
    if [ "$login_status" = "200" ]; then
        print_success "User login (Status: 200)"
        jwt_token=$(echo "$login_response" | grep -o '"token":"[^"]*"' | cut -d'"' -f4)
        if [ ! -z "$jwt_token" ]; then
            print_success "JWT token extracted successfully"
        else
            print_warning "JWT token not found in response"
        fi
        ((passed++))
    else
        print_error "User login (Expected: 200, Got: $login_status)"
        ((failed++))
    fi

    # Test 5: Token Validation (if we have a token)
    if [ ! -z "$jwt_token" ]; then
        if test_endpoint "GET" "/auth/validate-token" "" "200" "Token validation" "$jwt_token"; then
            ((passed++))
        else
            ((failed++))
        fi
    else
        print_warning "Skipping token validation (no token available)"
    fi

    # Test 6: Token Refresh (if we have a token)
    if [ ! -z "$jwt_token" ]; then
        if test_endpoint "POST" "/auth/refresh" "" "200" "Token refresh" "$jwt_token"; then
            ((passed++))
        else
            ((failed++))
        fi
    else
        print_warning "Skipping token refresh (no token available)"
    fi

    # Test 7: Forgot Password
    local forgot_data="{\"email\": \"$TEST_EMAIL\"}"
    if test_endpoint "POST" "/auth/forgot-password?email=$TEST_EMAIL" "" "200" "Forgot password"; then
        ((passed++))
    else
        ((failed++))
    fi

    # Test 8: Logout (if we have a token)
    if [ ! -z "$jwt_token" ]; then
        if test_endpoint "POST" "/auth/logout" "" "200" "User logout" "$jwt_token"; then
            ((passed++))
        else
            ((failed++))
        fi
    else
        print_warning "Skipping logout (no token available)"
    fi

    # Test 9: OAuth Endpoints (should return placeholder responses)
    if test_endpoint "GET" "/auth/oauth/google" "" "200" "Google OAuth initiation"; then
        ((passed++))
    else
        ((failed++))
    fi

    if test_endpoint "GET" "/auth/oauth/apple" "" "200" "Apple OAuth initiation"; then
        ((passed++))
    else
        ((failed++))
    fi

    # Display results
    echo
    echo "=================================================="
    echo "           Test Results"
    echo "=================================================="
    echo
    print_success "Tests passed: $passed"
    if [ $failed -gt 0 ]; then
        print_error "Tests failed: $failed"
    else
        print_success "Tests failed: $failed"
    fi
    echo
    
    local total=$((passed + failed))
    local success_rate=$((passed * 100 / total))
    
    if [ $success_rate -ge 80 ]; then
        print_success "Overall success rate: $success_rate% - Backend is ready!"
    elif [ $success_rate -ge 60 ]; then
        print_warning "Overall success rate: $success_rate% - Backend has some issues"
    else
        print_error "Overall success rate: $success_rate% - Backend needs attention"
    fi
    
    echo
    echo "Next steps:"
    echo "  • Start frontend: cd apps/frontend-nextjs && npm run dev"
    echo "  • Test full integration: http://localhost:3000/login"
    echo "  • Configure OAuth providers for social login"
    echo "  • Set up email service for production"
    
    return $failed
}

# Check if backend is running
if ! curl -s http://localhost:8080/actuator/health > /dev/null; then
    print_error "Backend is not running on http://localhost:8080"
    echo "Please start the backend first:"
    echo "  ./scripts/setup-backend.sh"
    echo "  or"
    echo "  docker-compose up -d"
    exit 1
fi

# Run tests
main "$@"
