package com.intellifin.dto.conversation;

import com.intellifin.model.ConversationMessage;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.Map;
import java.util.UUID;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ConversationResponse {

    private UUID messageId;
    
    private String sessionId;
    
    private UUID conversationId;
    
    private String content;
    
    private ConversationMessage.MessageType type;
    
    private ConversationMessage.MessageRole role;
    
    private ConversationMessage.MessageStatus status;
    
    private String intentRecognized;
    
    private Double confidenceScore;
    
    private Long processingTimeMs;
    
    private String errorMessage;
    
    private Map<String, Object> metadata;
    
    private LocalDateTime timestamp;
    
    @Builder.Default
    private Boolean isComplete = false;
    
    @Builder.Default
    private Boolean requiresFollowUp = false;
    
    private String followUpPrompt;

    // Static factory methods
    public static ConversationResponse success(UUID messageId, String sessionId, UUID conversationId, 
                                             String content, String intent, Double confidence, Long processingTime) {
        return ConversationResponse.builder()
                .messageId(messageId)
                .sessionId(sessionId)
                .conversationId(conversationId)
                .content(content)
                .type(ConversationMessage.MessageType.RESPONSE)
                .role(ConversationMessage.MessageRole.ASSISTANT)
                .status(ConversationMessage.MessageStatus.COMPLETED)
                .intentRecognized(intent)
                .confidenceScore(confidence)
                .processingTimeMs(processingTime)
                .timestamp(LocalDateTime.now())
                .isComplete(true)
                .build();
    }

    public static ConversationResponse error(UUID messageId, String sessionId, UUID conversationId, 
                                           String errorMessage) {
        return ConversationResponse.builder()
                .messageId(messageId)
                .sessionId(sessionId)
                .conversationId(conversationId)
                .content("I apologize, but I encountered an error processing your request.")
                .type(ConversationMessage.MessageType.ERROR)
                .role(ConversationMessage.MessageRole.ASSISTANT)
                .status(ConversationMessage.MessageStatus.FAILED)
                .errorMessage(errorMessage)
                .timestamp(LocalDateTime.now())
                .isComplete(true)
                .build();
    }

    public static ConversationResponse progress(UUID messageId, String sessionId, UUID conversationId, 
                                              String progressMessage) {
        return ConversationResponse.builder()
                .messageId(messageId)
                .sessionId(sessionId)
                .conversationId(conversationId)
                .content(progressMessage)
                .type(ConversationMessage.MessageType.PROGRESS)
                .role(ConversationMessage.MessageRole.ASSISTANT)
                .status(ConversationMessage.MessageStatus.PROCESSING)
                .timestamp(LocalDateTime.now())
                .isComplete(false)
                .build();
    }

    public static ConversationResponse followUp(UUID messageId, String sessionId, UUID conversationId, 
                                               String content, String followUpPrompt) {
        return ConversationResponse.builder()
                .messageId(messageId)
                .sessionId(sessionId)
                .conversationId(conversationId)
                .content(content)
                .type(ConversationMessage.MessageType.RESPONSE)
                .role(ConversationMessage.MessageRole.ASSISTANT)
                .status(ConversationMessage.MessageStatus.COMPLETED)
                .timestamp(LocalDateTime.now())
                .isComplete(true)
                .requiresFollowUp(true)
                .followUpPrompt(followUpPrompt)
                .build();
    }

    // Helper methods
    public boolean isSuccess() {
        return ConversationMessage.MessageStatus.COMPLETED.equals(status) && 
               !ConversationMessage.MessageType.ERROR.equals(type);
    }

    public boolean isError() {
        return ConversationMessage.MessageType.ERROR.equals(type) || 
               ConversationMessage.MessageStatus.FAILED.equals(status);
    }

    public boolean isProgress() {
        return ConversationMessage.MessageType.PROGRESS.equals(type);
    }

    public boolean isProcessing() {
        return ConversationMessage.MessageStatus.PROCESSING.equals(status);
    }
}
