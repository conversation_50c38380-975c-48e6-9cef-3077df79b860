# IntelliFin Architecture - High Level Architecture

## 2. High Level Architecture

This section establishes the foundational architectural decisions for IntelliFin.

### 2.1 Technical Summary

IntelliFin will adopt a polyglot microservices architecture, orchestrated by a central API Gateway. The frontend, built with Next.js, will communicate with the backend via a hybrid API approach: a WebSocket conversational gateway for real-time AI interactions and RESTful APIs for resource management. Backend services will utilize Java Spring Boot for core financial logic and Python FastAPI for AI/ML components. The entire system will be containerized with Docker, deployed on a cloud platform, and managed via GitHub Actions for CI/CD, ensuring scalability, maintainability, and rapid iteration aligned with PRD goals.

### 2.2 Platform and Infrastructure Choice

**Platform Recommendation:** Google Cloud Platform (GCP)
**Rationale:** Given the heavy emphasis on Conversational AI and the explicit mention of Google Gemini as the AI backbone, GCP offers superior native integration, optimized AI/ML services (Vertex AI, Cloud Functions for Python FastAPI), and robust infrastructure for microservices deployment (Cloud Run, Kubernetes Engine). It provides a strong ecosystem for scalable, AI-driven applications.

**Alternative 1: Microsoft Azure**
**Pros:** Strong enterprise features, good for .NET ecosystems, comprehensive monitoring tools.
**Cons:** AI/ML integration might not be as seamless with Google Gemini as GCP, potentially higher learning curve for Python AI services.

**Alternative 2: AWS Full Stack**
**Pros:** Broadest set of services, very scalable, mature ecosystem.
**Cons:** Can be more complex to manage, AI/ML services might require more setup for a Google-centric AI solution.

**Selected Platform:** Google Cloud Platform
**Key Services:** Google Kubernetes Engine (GKE) or Cloud Run (for microservices deployment), Cloud SQL (PostgreSQL), Vertex AI (for AI model serving), Cloud Functions/Cloud Run (for Python FastAPI services), Cloud CDN, Cloud Load Balancing, Cloud Logging, Cloud Monitoring, Cloud Identity and Access Management (IAM).
**Deployment Host and Regions:** Global deployment with initial focus on a single region (e.g., `europe-west1` or `us-central1`) for MVP, expanding to multi-region for resilience and reduced latency as user base grows in Africa.

### 2.3 Repository Structure

**Structure:** Monorepo
**Monorepo Tool:** Nx (or similar, to be confirmed based on deeper evaluation during implementation phase)
**Package Organization:**
- `apps/`: Contains individual applications (e.g., `frontend-nextjs`, `backend-java-core`, `backend-python-ai`).
- `libs/`: Contains shared libraries and packages (e.g., `data-models`, `api-interfaces`, `shared-utils`, `auth-client`). This will facilitate code sharing, particularly TypeScript interfaces for data models and API contracts between frontend and backend.

### 2.4 High Level Architecture Diagram

```mermaid
graph TD
    A[User_Entry_Point] --> B[Login/Signup]
    B --> C[Main_Conversational_Workspace]

    C --> C1[Display_Financial_Summary]
    C --> C2[View_Transactions_Review/Categorize]
    C --> C3[View_Invoice_Draft_Approval]
    C --> C4[Settings_Account_Connection]

    C1 --> |Triggered by command like 'show profit'| C
    C2 --> |Triggered by command or system alert| C
    C3 --> |Triggered by command like 'create invoice'| C
    C4 --> |Triggered by command or settings icon| C

    C2 --- |Associated Data| D[Transaction_Data]
    C3 --- |Associated Data| E[Invoice_Data_&_Client_Data]
    C4 --- |Associated Data| F[User_Profile_&_Financial_Account_Data]

    style C fill:#ADD8E6,stroke:#333,stroke-width:2px
    style C1 fill:#FFE4B5,stroke:#333,stroke-width:2px
    style C2 fill:#FFE4B5,stroke:#333,stroke-width:2px
    style C3 fill:#FFE4B5,stroke:#333,stroke-width:2px
    style C4 fill:#FFE4B5,stroke:#333,stroke-width:2px
    style D fill:#90EE90,stroke:#333,stroke-width:2px
    style E fill:#90EE90,stroke:#333,stroke-width:2px
    style F fill:#90EE90,stroke:#333,stroke-width:2px
```

### 2.5 Architectural Patterns

- **Polyglot Microservices:** IntelliFin's backend will be composed of small, independent services written in different languages (Java for core financial logic, Python for AI/ML). - _Rationale:_ Allows for choosing the best tool for each specific job, promotes team autonomy, and enhances scalability and resilience.
- **API Gateway Pattern:** A single entry point for all client requests, routing them to appropriate microservices. - _Rationale:_ Centralized authentication, rate limiting, logging, and load balancing, simplifying client-side complexity and enhancing security.
- **Backend For Frontend (BFF) Pattern (Conversational Gateway):** A specialized service (WebSocket Conversational Gateway) tailored for the frontend's conversational needs. - _Rationale:_ Optimizes communication between the frontend and backend, handles real-time conversational state, and decouples frontend from core business logic.
- **Event-Driven Architecture (for Integrations):** Utilizing webhooks and internal event queues for asynchronous communication with external systems (e.g., MTN Mobile Money). - _Rationale:_ Enhances responsiveness, scalability, and fault tolerance for external integrations.
- **Component-Based UI (Frontend):** Building the frontend with reusable React components (leveraging Shadcn/ui). - _Rationale:_ Promotes modularity, reusability, maintainability, and accelerates development.
- **Repository Pattern (Backend):** Abstracting data access logic from business logic. - _Rationale:_ Decouples services from specific database implementations, facilitating easier data source changes and improving testability.
- **Double-Entry Bookkeeping (Core Financial Engine):** Core accounting logic will be based on the double-entry system. - _Rationale:_ Ensures financial accuracy, integrity, and compliance, providing a robust foundation for all accounting operations.

---

**Previous Section:** [Introduction](01-introduction.md)  
**Next Section:** [Tech Stack](03-tech-stack.md) 