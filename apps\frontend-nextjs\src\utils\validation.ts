/**
 * Validation Utilities
 * Common validation functions for forms and data
 */

/**
 * Email validation
 */
export const isValidEmail = (email: string): boolean => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
};

/**
 * Password validation
 */
export const validatePassword = (password: string): {
  isValid: boolean;
  errors: string[];
} => {
  const errors: string[] = [];
  
  if (password.length < 8) {
    errors.push('Password must be at least 8 characters long');
  }
  
  if (!/[A-Z]/.test(password)) {
    errors.push('Password must contain at least one uppercase letter');
  }
  
  if (!/[a-z]/.test(password)) {
    errors.push('Password must contain at least one lowercase letter');
  }
  
  if (!/\d/.test(password)) {
    errors.push('Password must contain at least one number');
  }
  
  if (!/[!@#$%^&*(),.?":{}|<>]/.test(password)) {
    errors.push('Password must contain at least one special character');
  }
  
  return {
    isValid: errors.length === 0,
    errors,
  };
};

/**
 * Zambian phone number validation
 */
export const isValidZambianPhone = (phone: string): boolean => {
  // Remove all non-digit characters
  const cleaned = phone.replace(/\D/g, '');
  
  // Check for international format (+260XXXXXXXXX)
  if (cleaned.startsWith('260') && cleaned.length === 12) {
    return true;
  }
  
  // Check for local format (0XXXXXXXXX)
  if (cleaned.startsWith('0') && cleaned.length === 10) {
    return true;
  }
  
  // Check for mobile format without leading 0 (XXXXXXXXX)
  if (cleaned.length === 9 && /^[79]/.test(cleaned)) {
    return true;
  }
  
  return false;
};

/**
 * TPIN (Tax Payer Identification Number) validation for Zambia
 */
export const isValidTPIN = (tpin: string): boolean => {
  // Remove all non-digit characters
  const cleaned = tpin.replace(/\D/g, '');
  
  // TPIN should be exactly 10 digits
  return cleaned.length === 10 && /^\d{10}$/.test(cleaned);
};

/**
 * Amount validation (positive numbers with up to 2 decimal places)
 */
export const isValidAmount = (amount: string | number): boolean => {
  const numAmount = typeof amount === 'string' ? parseFloat(amount) : amount;
  
  if (isNaN(numAmount) || numAmount <= 0) {
    return false;
  }
  
  // Check for maximum 2 decimal places
  const decimalPlaces = (numAmount.toString().split('.')[1] || '').length;
  return decimalPlaces <= 2;
};

/**
 * Date validation
 */
export const isValidDate = (date: string): boolean => {
  const dateObj = new Date(date);
  return dateObj instanceof Date && !isNaN(dateObj.getTime());
};

/**
 * Future date validation
 */
export const isFutureDate = (date: string): boolean => {
  if (!isValidDate(date)) return false;
  
  const dateObj = new Date(date);
  const now = new Date();
  
  return dateObj > now;
};

/**
 * Past date validation
 */
export const isPastDate = (date: string): boolean => {
  if (!isValidDate(date)) return false;
  
  const dateObj = new Date(date);
  const now = new Date();
  
  return dateObj < now;
};

/**
 * Invoice number validation
 */
export const isValidInvoiceNumber = (invoiceNumber: string): boolean => {
  // Allow formats like INV-0001, INV-001, or just numbers
  const patterns = [
    /^INV-\d{3,}$/i,
    /^\d{1,}$/,
  ];
  
  return patterns.some(pattern => pattern.test(invoiceNumber));
};

/**
 * Required field validation
 */
export const isRequired = (value: any): boolean => {
  if (value === null || value === undefined) return false;
  if (typeof value === 'string') return value.trim().length > 0;
  if (Array.isArray(value)) return value.length > 0;
  return true;
};

/**
 * Minimum length validation
 */
export const hasMinLength = (value: string, minLength: number): boolean => {
  return value.length >= minLength;
};

/**
 * Maximum length validation
 */
export const hasMaxLength = (value: string, maxLength: number): boolean => {
  return value.length <= maxLength;
};

/**
 * Numeric validation
 */
export const isNumeric = (value: string): boolean => {
  return !isNaN(Number(value)) && !isNaN(parseFloat(value));
};

/**
 * Integer validation
 */
export const isInteger = (value: string | number): boolean => {
  const num = typeof value === 'string' ? parseInt(value, 10) : value;
  return Number.isInteger(num);
};

/**
 * Range validation
 */
export const isInRange = (value: number, min: number, max: number): boolean => {
  return value >= min && value <= max;
};

/**
 * URL validation
 */
export const isValidURL = (url: string): boolean => {
  try {
    new URL(url);
    return true;
  } catch {
    return false;
  }
};

/**
 * File type validation
 */
export const isValidFileType = (file: File, allowedTypes: string[]): boolean => {
  return allowedTypes.includes(file.type);
};

/**
 * File size validation (in bytes)
 */
export const isValidFileSize = (file: File, maxSize: number): boolean => {
  return file.size <= maxSize;
};

/**
 * Credit card number validation (basic Luhn algorithm)
 */
export const isValidCreditCard = (cardNumber: string): boolean => {
  const cleaned = cardNumber.replace(/\D/g, '');
  
  if (cleaned.length < 13 || cleaned.length > 19) {
    return false;
  }
  
  // Luhn algorithm
  let sum = 0;
  let isEven = false;
  
  for (let i = cleaned.length - 1; i >= 0; i--) {
    let digit = parseInt(cleaned[i], 10);
    
    if (isEven) {
      digit *= 2;
      if (digit > 9) {
        digit -= 9;
      }
    }
    
    sum += digit;
    isEven = !isEven;
  }
  
  return sum % 10 === 0;
};

/**
 * Form validation helper
 */
export interface ValidationRule {
  required?: boolean;
  minLength?: number;
  maxLength?: number;
  pattern?: RegExp;
  custom?: (value: any) => boolean | string;
  email?: boolean;
  phone?: boolean;
  tpin?: boolean;
  amount?: boolean;
  date?: boolean;
  futureDate?: boolean;
  pastDate?: boolean;
}

export interface ValidationResult {
  isValid: boolean;
  errors: string[];
}

export const validateField = (
  value: any,
  rules: ValidationRule,
  fieldName: string = 'Field'
): ValidationResult => {
  const errors: string[] = [];
  
  // Required validation
  if (rules.required && !isRequired(value)) {
    errors.push(`${fieldName} is required`);
    return { isValid: false, errors };
  }
  
  // Skip other validations if field is empty and not required
  if (!isRequired(value) && !rules.required) {
    return { isValid: true, errors: [] };
  }
  
  const stringValue = String(value);
  
  // Length validations
  if (rules.minLength && !hasMinLength(stringValue, rules.minLength)) {
    errors.push(`${fieldName} must be at least ${rules.minLength} characters`);
  }
  
  if (rules.maxLength && !hasMaxLength(stringValue, rules.maxLength)) {
    errors.push(`${fieldName} must be no more than ${rules.maxLength} characters`);
  }
  
  // Pattern validation
  if (rules.pattern && !rules.pattern.test(stringValue)) {
    errors.push(`${fieldName} format is invalid`);
  }
  
  // Email validation
  if (rules.email && !isValidEmail(stringValue)) {
    errors.push(`${fieldName} must be a valid email address`);
  }
  
  // Phone validation
  if (rules.phone && !isValidZambianPhone(stringValue)) {
    errors.push(`${fieldName} must be a valid Zambian phone number`);
  }
  
  // TPIN validation
  if (rules.tpin && !isValidTPIN(stringValue)) {
    errors.push(`${fieldName} must be a valid 10-digit TPIN`);
  }
  
  // Amount validation
  if (rules.amount && !isValidAmount(stringValue)) {
    errors.push(`${fieldName} must be a valid positive amount`);
  }
  
  // Date validations
  if (rules.date && !isValidDate(stringValue)) {
    errors.push(`${fieldName} must be a valid date`);
  }
  
  if (rules.futureDate && !isFutureDate(stringValue)) {
    errors.push(`${fieldName} must be a future date`);
  }
  
  if (rules.pastDate && !isPastDate(stringValue)) {
    errors.push(`${fieldName} must be a past date`);
  }
  
  // Custom validation
  if (rules.custom) {
    const customResult = rules.custom(value);
    if (typeof customResult === 'string') {
      errors.push(customResult);
    } else if (!customResult) {
      errors.push(`${fieldName} is invalid`);
    }
  }
  
  return {
    isValid: errors.length === 0,
    errors,
  };
};

/**
 * Validate entire form object
 */
export const validateForm = (
  data: Record<string, any>,
  rules: Record<string, ValidationRule>
): Record<string, ValidationResult> => {
  const results: Record<string, ValidationResult> = {};
  
  Object.keys(rules).forEach(fieldName => {
    results[fieldName] = validateField(
      data[fieldName],
      rules[fieldName],
      fieldName
    );
  });
  
  return results;
};

/**
 * Check if form validation results are all valid
 */
export const isFormValid = (
  validationResults: Record<string, ValidationResult>
): boolean => {
  return Object.values(validationResults).every(result => result.isValid);
};
