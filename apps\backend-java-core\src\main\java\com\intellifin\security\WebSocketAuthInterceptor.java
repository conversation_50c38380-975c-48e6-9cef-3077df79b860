package com.intellifin.security;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.messaging.Message;
import org.springframework.messaging.MessageChannel;
import org.springframework.messaging.simp.stomp.StompCommand;
import org.springframework.messaging.simp.stomp.StompHeaderAccessor;
import org.springframework.messaging.support.ChannelInterceptor;
import org.springframework.messaging.support.MessageHeaderAccessor;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.util.UUID;

@Component
@RequiredArgsConstructor
@Slf4j
public class WebSocketAuthInterceptor implements ChannelInterceptor {

    private final JwtTokenUtil jwtTokenUtil;
    private final CustomUserDetailsService userDetailsService;

    @Override
    public Message<?> preSend(Message<?> message, MessageChannel channel) {
        StompHeaderAccessor accessor = MessageHeaderAccessor.getAccessor(message, StompHeaderAccessor.class);
        
        if (accessor != null && StompCommand.CONNECT.equals(accessor.getCommand())) {
            // Extract JWT token from WebSocket connection headers
            String token = extractTokenFromHeaders(accessor);
            
            if (StringUtils.hasText(token) && jwtTokenUtil.validateToken(token)) {
                try {
                    UUID userId = jwtTokenUtil.getUserIdFromToken(token);
                    
                    if (userId != null) {
                        UserDetails userDetails = userDetailsService.loadUserById(userId.toString());
                        
                        if (jwtTokenUtil.validateToken(token, userDetails)) {
                            UsernamePasswordAuthenticationToken authentication = 
                                    new UsernamePasswordAuthenticationToken(userDetails, null, userDetails.getAuthorities());
                            
                            // Set authentication in accessor for this WebSocket session
                            accessor.setUser(authentication);
                            
                            // Store user information in session attributes
                            accessor.getSessionAttributes().put("userId", userId.toString());
                            accessor.getSessionAttributes().put("userEmail", userDetails.getUsername());
                            
                            log.debug("WebSocket authentication successful for user: {}", userDetails.getUsername());
                        } else {
                            log.warn("Invalid JWT token for WebSocket connection");
                            throw new SecurityException("Invalid JWT token");
                        }
                    } else {
                        log.warn("No user ID found in JWT token for WebSocket connection");
                        throw new SecurityException("Invalid JWT token");
                    }
                } catch (Exception e) {
                    log.error("WebSocket authentication failed: {}", e.getMessage());
                    throw new SecurityException("WebSocket authentication failed");
                }
            } else {
                log.warn("No valid JWT token provided for WebSocket connection");
                throw new SecurityException("Authentication required for WebSocket connection");
            }
        }
        
        return message;
    }

    private String extractTokenFromHeaders(StompHeaderAccessor accessor) {
        // Try to get token from Authorization header
        String authHeader = accessor.getFirstNativeHeader("Authorization");
        if (StringUtils.hasText(authHeader) && authHeader.startsWith("Bearer ")) {
            return authHeader.substring(7);
        }
        
        // Try to get token from X-Auth-Token header (alternative)
        String tokenHeader = accessor.getFirstNativeHeader("X-Auth-Token");
        if (StringUtils.hasText(tokenHeader)) {
            return tokenHeader;
        }
        
        // Try to get token from query parameters (for browser WebSocket connections)
        String token = accessor.getFirstNativeHeader("token");
        if (StringUtils.hasText(token)) {
            return token;
        }
        
        return null;
    }

    @Override
    public void postSend(Message<?> message, MessageChannel channel, boolean sent) {
        StompHeaderAccessor accessor = MessageHeaderAccessor.getAccessor(message, StompHeaderAccessor.class);
        
        if (accessor != null && StompCommand.DISCONNECT.equals(accessor.getCommand())) {
            String userId = (String) accessor.getSessionAttributes().get("userId");
            if (userId != null) {
                log.debug("WebSocket disconnection for user: {}", userId);
                // Clean up any user-specific resources if needed
            }
        }
    }
}
