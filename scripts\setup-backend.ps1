# IntelliFin Backend Setup Script (PowerShell)
# This script helps configure and start the backend services on Windows

param(
    [switch]$SkipChecks,
    [switch]$DockerOnly
)

# Function to print colored output
function Write-Status {
    param([string]$Message)
    Write-Host "[INFO] $Message" -ForegroundColor Blue
}

function Write-Success {
    param([string]$Message)
    Write-Host "[SUCCESS] $Message" -ForegroundColor Green
}

function Write-Warning {
    param([string]$Message)
    Write-Host "[WARNING] $Message" -ForegroundColor Yellow
}

function Write-Error {
    param([string]$Message)
    Write-Host "[ERROR] $Message" -ForegroundColor Red
}

# Function to check if command exists
function Test-Command {
    param([string]$Command)
    try {
        Get-Command $Command -ErrorAction Stop | Out-Null
        return $true
    }
    catch {
        return $false
    }
}

# Function to check if port is available
function Test-Port {
    param([int]$Port)
    try {
        $connection = New-Object System.Net.Sockets.TcpClient
        $connection.Connect("localhost", $Port)
        $connection.Close()
        return $false  # Port is in use
    }
    catch {
        return $true   # Port is available
    }
}

# Function to wait for service to be ready
function Wait-ForService {
    param(
        [string]$ServiceName,
        [int]$Port,
        [int]$MaxAttempts = 30
    )
    
    Write-Status "Waiting for $ServiceName to be ready on port $Port..."
    
    for ($attempt = 1; $attempt -le $MaxAttempts; $attempt++) {
        if (-not (Test-Port $Port)) {
            Write-Success "$ServiceName is ready!"
            return $true
        }
        
        Write-Host "." -NoNewline
        Start-Sleep -Seconds 2
    }
    
    Write-Host ""
    Write-Error "$ServiceName failed to start within expected time"
    return $false
}

# Function to generate JWT secret
function New-JwtSecret {
    $bytes = New-Object byte[] 32
    [System.Security.Cryptography.RNGCryptoServiceProvider]::Create().GetBytes($bytes)
    return [Convert]::ToBase64String($bytes)
}

# Main setup function
function Start-Setup {
    Write-Host "==================================================" -ForegroundColor Cyan
    Write-Host "       IntelliFin Backend Setup Script" -ForegroundColor Cyan
    Write-Host "==================================================" -ForegroundColor Cyan
    Write-Host ""

    # Check prerequisites
    if (-not $SkipChecks) {
        Write-Status "Checking prerequisites..."
        
        if (-not (Test-Command "docker")) {
            Write-Error "Docker is not installed. Please install Docker Desktop first."
            exit 1
        }
        
        if (-not (Test-Command "docker-compose")) {
            Write-Error "Docker Compose is not installed. Please install Docker Compose first."
            exit 1
        }
        
        Write-Success "Prerequisites check passed"
    }

    # Check if .env file exists
    if (-not (Test-Path ".env")) {
        Write-Warning ".env file not found. Creating from .env.example..."
        
        if (Test-Path ".env.example") {
            Copy-Item ".env.example" ".env"
            
            # Generate JWT secret
            $jwtSecret = New-JwtSecret
            $content = Get-Content ".env" -Raw
            $content = $content -replace "your-local-jwt-secret-key-min-256-bits-change-this", $jwtSecret
            Set-Content ".env" $content
            
            Write-Success ".env file created with generated JWT secret"
            Write-Warning "Please review and update .env file with your specific configuration"
        }
        else {
            Write-Error ".env.example file not found. Please create .env file manually."
            exit 1
        }
    }
    else {
        Write-Success ".env file found"
    }

    # Check if ports are available
    if (-not $SkipChecks) {
        Write-Status "Checking port availability..."
        
        $ports = @(
            @{Port = 5432; Name = "PostgreSQL"},
            @{Port = 6379; Name = "Redis"},
            @{Port = 8080; Name = "Backend"}
        )
        
        foreach ($portInfo in $ports) {
            if (-not (Test-Port $portInfo.Port)) {
                Write-Warning "Port $($portInfo.Port) ($($portInfo.Name)) is already in use"
                $response = Read-Host "Do you want to continue anyway? (y/N)"
                if ($response -ne "y" -and $response -ne "Y") {
                    Write-Error "Setup cancelled"
                    exit 1
                }
            }
        }
    }

    # Start infrastructure services
    Write-Status "Starting infrastructure services (PostgreSQL, Redis)..."
    docker-compose up -d postgres redis

    # Wait for services to be ready
    if (-not (Wait-ForService "PostgreSQL" 5432)) { exit 1 }
    if (-not (Wait-ForService "Redis" 6379)) { exit 1 }

    # Run database migrations
    Write-Status "Running database migrations..."
    Push-Location "apps\backend-java-core"
    
    try {
        if (Test-Path "mvnw.cmd") {
            & .\mvnw.cmd flyway:migrate "-Dflyway.configFiles=src/main/resources/flyway.conf"
        }
        elseif (Test-Command "mvn") {
            mvn flyway:migrate "-Dflyway.configFiles=src/main/resources/flyway.conf"
        }
        else {
            Write-Warning "Maven not found. Please run database migrations manually."
        }
    }
    finally {
        Pop-Location
    }

    # Start the backend application
    Write-Status "Starting backend application..."
    
    if ($DockerOnly) {
        $startMethod = "d"
    }
    else {
        $startMethod = Read-Host "Start backend with Docker (d) or locally with Maven (m)? [d/m]"
    }
    
    if ($startMethod -eq "m" -or $startMethod -eq "M") {
        # Start locally with Maven
        Write-Status "Starting backend locally with Maven..."
        Push-Location "apps\backend-java-core"
        
        try {
            if (Test-Path "mvnw.cmd") {
                Start-Process -FilePath ".\mvnw.cmd" -ArgumentList "spring-boot:run" -NoNewWindow
            }
            elseif (Test-Command "mvn") {
                Start-Process -FilePath "mvn" -ArgumentList "spring-boot:run" -NoNewWindow
            }
            else {
                Write-Error "Maven not found. Please install Maven or use Docker option."
                exit 1
            }
        }
        finally {
            Pop-Location
        }
        
        Write-Success "Backend started locally"
    }
    else {
        # Start with Docker
        Write-Status "Starting backend with Docker..."
        docker-compose up -d backend-core
        
        Write-Success "Backend started with Docker"
    }

    # Wait for backend to be ready
    if (-not (Wait-ForService "Backend" 8080)) { exit 1 }

    # Test the backend
    Write-Status "Testing backend endpoints..."
    
    try {
        $healthResponse = Invoke-WebRequest -Uri "http://localhost:8080/actuator/health" -UseBasicParsing -TimeoutSec 10
        if ($healthResponse.StatusCode -eq 200) {
            Write-Success "Health endpoint is responding"
        }
    }
    catch {
        Write-Error "Health endpoint is not responding"
    }
    
    try {
        $swaggerResponse = Invoke-WebRequest -Uri "http://localhost:8080/swagger-ui.html" -UseBasicParsing -TimeoutSec 10
        if ($swaggerResponse.StatusCode -eq 200) {
            Write-Success "API documentation is available"
        }
    }
    catch {
        Write-Warning "API documentation may not be available"
    }

    # Display summary
    Write-Host ""
    Write-Host "==================================================" -ForegroundColor Cyan
    Write-Host "           Setup Complete!" -ForegroundColor Cyan
    Write-Host "==================================================" -ForegroundColor Cyan
    Write-Host ""
    Write-Success "Backend services are running:"
    Write-Host "  • PostgreSQL: http://localhost:5432"
    Write-Host "  • Redis: http://localhost:6379"
    Write-Host "  • Backend API: http://localhost:8080"
    Write-Host "  • API Documentation: http://localhost:8080/swagger-ui.html"
    Write-Host "  • Health Check: http://localhost:8080/actuator/health"
    Write-Host ""
    Write-Status "Next steps:"
    Write-Host "  1. Start the frontend: cd apps\frontend-nextjs && npm run dev"
    Write-Host "  2. Test authentication: http://localhost:3000/login"
    Write-Host "  3. Configure OAuth providers (see BACKEND_CONFIGURATION.md)"
    Write-Host "  4. Set up email service for production"
    Write-Host ""
    Write-Warning "Remember to:"
    Write-Host "  • Update .env with production values"
    Write-Host "  • Configure OAuth client IDs"
    Write-Host "  • Set up email service for production"
    Write-Host ""
}

# Run the setup
try {
    Start-Setup
}
catch {
    Write-Error "Setup failed: $($_.Exception.Message)"
    exit 1
}
