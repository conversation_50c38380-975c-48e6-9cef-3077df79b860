# IntelliFin Architecture - Core Financial Engine & Accounting Logic

## 8. Core Financial Engine & Accounting Logic

### 8.1 Double-Entry Bookkeeping

*   **Principle:** The system strictly follows the double-entry bookkeeping principle, where every financial transaction must have a corresponding debit and credit entry.
*   **Purpose:** Ensures financial accuracy, integrity, and compliance with accounting standards.
*   **Example:** A transaction of 100 ZMW from `Cash` (Asset) to `Sales Revenue` (Revenue) would be recorded as:
    *   Debit: `Cash` (100 ZMW)
    *   Credit: `Sales Revenue` (100 ZMW)
*   **Validation:** Automated checks ensure that the sum of debits equals the sum of credits for each `JournalEntry`.

### 8.2 Transaction Processing

*   **Flow:**
    1.  User initiates a transaction (e.g., "Paid ZESCO for electricity").
    2.  The frontend sends a raw text command to the WebSocket gateway.
    3.  The WebSocket gateway forwards the command to the AI Service.
    4.  The AI Service uses LangChain4j and Gemini to parse the command and identify `Transaction` and `FinancialAccount` entities.
    5.  The AI Service proposes a `Category` and `Amount`.
    6.  The frontend receives the proposed `Transaction` object and displays it for user confirmation.
    7.  Upon user approval, the frontend sends a `POST` request to the `Transaction` endpoint.
    8.  The backend validates the transaction, creates `JournalEntry` for debits and credits, updates `FinancialAccount` balances, and triggers reconciliation.
*   **Error Handling:** Comprehensive error handling for invalid transactions, insufficient funds, and connection issues.

### 8.3 Category Classification

*   **Purpose:** To automatically categorize transactions based on their descriptions and context.
*   **Flow:**
    1.  A new `Transaction` is created with an `originalDescription`.
    2.  The `AI Service` receives this `Transaction` and uses LangChain4j and Gemini to analyze the `originalDescription`.
    3.  The AI Service proposes a `Category` and `Justification`.
    4.  The frontend displays the proposed `Category` and `Justification` for user review.
    5.  Upon user approval, the frontend sends a `PUT` request to update the `Transaction`'s `Category` and `Justification`.
    6.  The backend validates the classification, updates the `JournalEntry`, and triggers reconciliation.
*   **Error Handling:** Robust error handling for invalid classifications and connection issues.

### 8.4 Financial Reporting

*   **Real-time Reporting:** The system will support real-time generation of key financial statements by querying `JournalEntry` and `Account` data.
*   **Key Reports (MVP):**
    *   **Income Statement (Profit & Loss):** Summarizes revenues and expenses over a period to show profitability.
    *   **Balance Sheet:** Presents a snapshot of assets, liabilities, and equity at a specific point in time.
    *   **General Ledger/Journal:** Provides detailed, chronological records of all transactions.
*   **Customizable Reporting:** Future iterations may include customizable report builders and advanced analytics dashboards.

### 8.5 AI's Role & Explainability (XAI)

*   **AI's Primary Role:** The AI's job is to analyze the `originalDescription` of a `Transaction` and propose the most likely `Category`.
*   **Explainability ("Why?"):** To fulfill our promise of educating the user, the AI's response will include a justification field. For a "ZESCO" payment categorized as "Utilities," the justification might be: "ZESCO is a provider of electricity, which is typically classified as a 'Utilities' business expense."

---

**Previous Section:** [Security & Compliance](07-security-compliance.md)  
**Next Section:** [Frontend Architecture](09-frontend-architecture.md) 