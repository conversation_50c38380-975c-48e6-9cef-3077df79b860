# IntelliFin WebSocket API Specification

## Overview

This document specifies the WebSocket API for IntelliFin's conversational interface, enabling real-time communication between the frontend and backend services.

## 1. Connection Management

### 1.1 WebSocket Endpoint

```
ws://localhost:8080/ws/conversation
wss://api.intellifin.com/ws/conversation
```

### 1.2 Authentication

WebSocket connections require JWT authentication via query parameter:

```javascript
const ws = new WebSocket(`ws://localhost:8080/ws/conversation?token=${jwtToken}`);
```

### 1.3 Connection Lifecycle

```javascript
// Connection establishment
ws.onopen = (event) => {
  console.log('Connected to IntelliFin conversation service');
  // Send initial session setup
  ws.send(JSON.stringify({
    type: 'SESSION_INIT',
    sessionId: generateSessionId(),
    userId: getCurrentUserId()
  }));
};

// Connection close
ws.onclose = (event) => {
  if (event.code === 1000) {
    console.log('Connection closed normally');
  } else {
    console.log('Connection closed unexpectedly:', event.code, event.reason);
    // Implement reconnection logic
  }
};

// Error handling
ws.onerror = (error) => {
  console.error('WebSocket error:', error);
};
```

## 2. Message Format

### 2.1 Base Message Structure

All WebSocket messages follow this structure:

```typescript
interface WebSocketMessage {
  type: MessageType;
  sessionId: string;
  messageId: string;
  timestamp: string;
  payload: any;
}

enum MessageType {
  // Client to Server
  SESSION_INIT = 'SESSION_INIT',
  COMMAND = 'COMMAND',
  CONFIRMATION = 'CONFIRMATION',
  HEARTBEAT = 'HEARTBEAT',
  
  // Server to Client
  SESSION_READY = 'SESSION_READY',
  PROCESSING = 'PROCESSING',
  RESPONSE = 'RESPONSE',
  ERROR = 'ERROR',
  HEARTBEAT_ACK = 'HEARTBEAT_ACK'
}
```

### 2.2 Client Messages

#### Session Initialization

```json
{
  "type": "SESSION_INIT",
  "sessionId": "session-uuid-here",
  "messageId": "msg-uuid-here",
  "timestamp": "2024-07-30T10:00:00Z",
  "payload": {
    "userId": "user-uuid-here",
    "clientInfo": {
      "userAgent": "Mozilla/5.0...",
      "platform": "web",
      "version": "1.0.0"
    }
  }
}
```

#### Command Message

```json
{
  "type": "COMMAND",
  "sessionId": "session-uuid-here",
  "messageId": "msg-uuid-here",
  "timestamp": "2024-07-30T10:00:00Z",
  "payload": {
    "command": "Show me my profit this month",
    "context": {
      "currentPage": "/dashboard",
      "previousCommands": ["create invoice for John Doe"]
    }
  }
}
```

#### Confirmation Message

```json
{
  "type": "CONFIRMATION",
  "sessionId": "session-uuid-here",
  "messageId": "msg-uuid-here",
  "timestamp": "2024-07-30T10:00:00Z",
  "payload": {
    "confirmationId": "confirmation-uuid-here",
    "action": "APPROVE",
    "data": {
      "invoiceId": "invoice-uuid-here",
      "submitToZRA": true
    }
  }
}
```

### 2.3 Server Messages

#### Session Ready

```json
{
  "type": "SESSION_READY",
  "sessionId": "session-uuid-here",
  "messageId": "msg-uuid-here",
  "timestamp": "2024-07-30T10:00:00Z",
  "payload": {
    "status": "ready",
    "capabilities": [
      "transaction_management",
      "invoice_creation",
      "financial_reporting",
      "account_management"
    ],
    "userPreferences": {
      "currency": "ZMW",
      "dateFormat": "DD/MM/YYYY"
    }
  }
}
```

#### Processing Message

```json
{
  "type": "PROCESSING",
  "sessionId": "session-uuid-here",
  "messageId": "msg-uuid-here",
  "timestamp": "2024-07-30T10:00:00Z",
  "payload": {
    "status": "processing",
    "stage": "intent_recognition",
    "estimatedTime": 2000,
    "message": "Understanding your request..."
  }
}
```

#### Response Message

```json
{
  "type": "RESPONSE",
  "sessionId": "session-uuid-here",
  "messageId": "msg-uuid-here",
  "timestamp": "2024-07-30T10:00:00Z",
  "payload": {
    "intent": "VIEW_FINANCIAL_SUMMARY",
    "confidence": 0.95,
    "response": {
      "type": "financial_summary",
      "data": {
        "period": "2024-07",
        "totalIncome": 15000.00,
        "totalExpenses": 8500.00,
        "netProfit": 6500.00,
        "currency": "ZMW"
      },
      "message": "Here's your profit summary for July 2024. You made K6,500 profit with K15,000 income and K8,500 expenses.",
      "actions": [
        {
          "type": "view_details",
          "label": "View Detailed Report",
          "url": "/reports/profit-loss?month=2024-07"
        }
      ]
    }
  }
}
```

#### Error Message

```json
{
  "type": "ERROR",
  "sessionId": "session-uuid-here",
  "messageId": "msg-uuid-here",
  "timestamp": "2024-07-30T10:00:00Z",
  "payload": {
    "errorCode": "AI_SERVICE_UNAVAILABLE",
    "message": "I'm having trouble understanding your request right now. Please try again in a moment.",
    "details": "AI service is temporarily unavailable",
    "retryable": true,
    "fallbackActions": [
      {
        "type": "manual_navigation",
        "label": "Go to Transactions",
        "url": "/transactions"
      }
    ]
  }
}
```

## 3. Conversation Flows

### 3.1 Financial Summary Request

```mermaid
sequenceDiagram
    participant C as Client
    participant S as Server
    participant AI as AI Service
    participant DB as Database

    C->>S: COMMAND: "Show me my profit this month"
    S->>C: PROCESSING: "Understanding your request..."
    S->>AI: Parse intent and entities
    AI->>S: Intent: VIEW_FINANCIAL_SUMMARY, entities: {period: "this month"}
    S->>C: PROCESSING: "Calculating your financial summary..."
    S->>DB: Query transactions for current month
    DB->>S: Transaction data
    S->>AI: Generate explanation
    AI->>S: Human-friendly explanation
    S->>C: RESPONSE: Financial summary with explanation
```

### 3.2 Invoice Creation Flow

```mermaid
sequenceDiagram
    participant C as Client
    participant S as Server
    participant AI as AI Service

    C->>S: COMMAND: "Create invoice for John Doe K5000"
    S->>C: PROCESSING: "Creating invoice draft..."
    S->>AI: Extract invoice details
    AI->>S: Entities: {client: "John Doe", amount: 5000}
    S->>C: RESPONSE: Invoice draft with confirmation request
    C->>S: CONFIRMATION: Approve invoice creation
    S->>C: PROCESSING: "Submitting to ZRA..."
    S->>C: RESPONSE: Invoice created successfully
```

### 3.3 Transaction Categorization

```mermaid
sequenceDiagram
    participant C as Client
    participant S as Server
    participant AI as AI Service

    C->>S: COMMAND: "Categorize my ZESCO payment as utilities"
    S->>C: PROCESSING: "Finding ZESCO transactions..."
    S->>AI: Find matching transactions
    AI->>S: Matching transactions found
    S->>C: RESPONSE: List of transactions to categorize
    C->>S: CONFIRMATION: Confirm categorization
    S->>C: PROCESSING: "Updating categories..."
    S->>C: RESPONSE: Transactions categorized successfully
```

## 4. Error Handling

### 4.1 Connection Errors

```javascript
// Reconnection logic
class ConversationWebSocket {
  constructor(url, token) {
    this.url = url;
    this.token = token;
    this.reconnectAttempts = 0;
    this.maxReconnectAttempts = 5;
    this.reconnectDelay = 1000;
    this.connect();
  }

  connect() {
    this.ws = new WebSocket(`${this.url}?token=${this.token}`);
    
    this.ws.onopen = () => {
      this.reconnectAttempts = 0;
      this.onConnected();
    };

    this.ws.onclose = (event) => {
      if (event.code !== 1000 && this.reconnectAttempts < this.maxReconnectAttempts) {
        setTimeout(() => {
          this.reconnectAttempts++;
          this.connect();
        }, this.reconnectDelay * Math.pow(2, this.reconnectAttempts));
      }
    };

    this.ws.onerror = (error) => {
      console.error('WebSocket error:', error);
    };
  }
}
```

### 4.2 Message Timeout

```javascript
class MessageManager {
  constructor(ws) {
    this.ws = ws;
    this.pendingMessages = new Map();
    this.messageTimeout = 30000; // 30 seconds
  }

  sendMessage(message) {
    const messageId = generateUUID();
    message.messageId = messageId;
    
    return new Promise((resolve, reject) => {
      // Set timeout
      const timeoutId = setTimeout(() => {
        this.pendingMessages.delete(messageId);
        reject(new Error('Message timeout'));
      }, this.messageTimeout);

      this.pendingMessages.set(messageId, { resolve, reject, timeoutId });
      this.ws.send(JSON.stringify(message));
    });
  }

  handleResponse(response) {
    const pending = this.pendingMessages.get(response.messageId);
    if (pending) {
      clearTimeout(pending.timeoutId);
      this.pendingMessages.delete(response.messageId);
      pending.resolve(response);
    }
  }
}
```

## 5. Rate Limiting

### 5.1 Client-Side Rate Limiting

```javascript
class RateLimiter {
  constructor(maxRequests = 10, windowMs = 60000) {
    this.maxRequests = maxRequests;
    this.windowMs = windowMs;
    this.requests = [];
  }

  canMakeRequest() {
    const now = Date.now();
    this.requests = this.requests.filter(time => now - time < this.windowMs);
    return this.requests.length < this.maxRequests;
  }

  recordRequest() {
    this.requests.push(Date.now());
  }
}
```

### 5.2 Server-Side Rate Limiting

Server implements rate limiting per user:
- 60 commands per minute
- 1000 commands per hour
- Burst capacity of 10 commands

## 6. Security Considerations

### 6.1 Authentication Validation

```java
// WebSocket authentication interceptor
@Component
public class WebSocketAuthInterceptor implements HandlerInterceptor {
    
    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) {
        String token = request.getParameter("token");
        
        if (token == null || !jwtTokenUtil.validateToken(token)) {
            response.setStatus(HttpStatus.UNAUTHORIZED.value());
            return false;
        }
        
        // Store user context for session
        String userId = jwtTokenUtil.getUserIdFromToken(token);
        request.setAttribute("userId", userId);
        
        return true;
    }
}
```

### 6.2 Message Validation

```java
// Message validation
@Component
public class MessageValidator {
    
    public void validateMessage(WebSocketMessage message) {
        // Validate message structure
        if (message.getType() == null || message.getSessionId() == null) {
            throw new InvalidMessageException("Missing required fields");
        }
        
        // Validate payload based on message type
        switch (message.getType()) {
            case COMMAND:
                validateCommandPayload(message.getPayload());
                break;
            case CONFIRMATION:
                validateConfirmationPayload(message.getPayload());
                break;
        }
        
        // Rate limiting check
        if (!rateLimiter.allowRequest(message.getSessionId())) {
            throw new RateLimitExceededException("Too many requests");
        }
    }
}
```

This WebSocket specification provides a comprehensive foundation for implementing real-time conversational interactions in the IntelliFin platform.
