package com.intellifin.controller;

import com.intellifin.dto.conversation.ConversationCommandRequest;
import com.intellifin.dto.conversation.ConversationResponse;
import com.intellifin.security.UserPrincipal;
import com.intellifin.service.ConversationService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.messaging.handler.annotation.MessageMapping;
import org.springframework.messaging.handler.annotation.Payload;
import org.springframework.messaging.simp.SimpMessageHeaderAccessor;
import org.springframework.messaging.simp.annotation.SendToUser;
import org.springframework.security.core.Authentication;
import org.springframework.stereotype.Controller;

import java.security.Principal;
import java.util.UUID;

@Controller
@RequiredArgsConstructor
@Slf4j
public class ConversationWebSocketController {

    private final ConversationService conversationService;

    @MessageMapping("/conversation/command")
    @SendToUser("/queue/conversation")
    public ConversationResponse processCommand(
            @Payload ConversationCommandRequest request,
            Principal principal,
            SimpMessageHeaderAccessor headerAccessor) {
        
        try {
            // Extract user information from authenticated principal
            UUID userId = extractUserId(principal);
            String sessionId = extractSessionId(headerAccessor);
            
            log.info("Received WebSocket command from user: {}, session: {}, command: {}", 
                    userId, sessionId, request.getCommand());
            
            // Set session ID if not provided in request
            if (!request.hasSessionId() && sessionId != null) {
                request.setSessionId(sessionId);
            }
            
            // Process the command
            ConversationResponse response = conversationService.processCommand(request, userId);
            
            log.debug("WebSocket command processed for user: {}, response type: {}", 
                    userId, response.getType());
            
            return response;
            
        } catch (Exception e) {
            log.error("Error processing WebSocket command", e);
            
            return ConversationResponse.error(
                null,
                request.getSessionId(),
                null,
                "Failed to process your request. Please try again."
            );
        }
    }

    @MessageMapping("/conversation/typing")
    public void handleTypingIndicator(
            @Payload TypingIndicatorMessage message,
            Principal principal,
            SimpMessageHeaderAccessor headerAccessor) {
        
        try {
            UUID userId = extractUserId(principal);
            String sessionId = extractSessionId(headerAccessor);
            
            log.debug("Typing indicator from user: {}, session: {}, typing: {}", 
                    userId, sessionId, message.isTyping());
            
            // Could broadcast typing indicators to other participants if needed
            // For now, just log the activity
            
        } catch (Exception e) {
            log.error("Error handling typing indicator", e);
        }
    }

    @MessageMapping("/conversation/ping")
    @SendToUser("/queue/conversation")
    public PongMessage handlePing(
            @Payload PingMessage ping,
            Principal principal) {
        
        try {
            UUID userId = extractUserId(principal);
            log.debug("Ping received from user: {}", userId);
            
            return new PongMessage(ping.getTimestamp(), System.currentTimeMillis());
            
        } catch (Exception e) {
            log.error("Error handling ping", e);
            return new PongMessage(0L, System.currentTimeMillis());
        }
    }

    private UUID extractUserId(Principal principal) {
        if (principal instanceof Authentication auth) {
            if (auth.getPrincipal() instanceof UserPrincipal userPrincipal) {
                return userPrincipal.getId();
            }
        }
        throw new IllegalStateException("Unable to extract user ID from principal");
    }

    private String extractSessionId(SimpMessageHeaderAccessor headerAccessor) {
        // Try to get session ID from WebSocket session attributes
        Object sessionId = headerAccessor.getSessionAttributes().get("sessionId");
        if (sessionId != null) {
            return sessionId.toString();
        }
        
        // Fallback to WebSocket session ID
        return headerAccessor.getSessionId();
    }

    // Helper classes for WebSocket messages
    public static class TypingIndicatorMessage {
        private boolean typing;
        private String sessionId;
        
        public TypingIndicatorMessage() {}
        
        public TypingIndicatorMessage(boolean typing, String sessionId) {
            this.typing = typing;
            this.sessionId = sessionId;
        }
        
        public boolean isTyping() { return typing; }
        public void setTyping(boolean typing) { this.typing = typing; }
        public String getSessionId() { return sessionId; }
        public void setSessionId(String sessionId) { this.sessionId = sessionId; }
    }

    public static class PingMessage {
        private Long timestamp;
        
        public PingMessage() {}
        
        public PingMessage(Long timestamp) {
            this.timestamp = timestamp;
        }
        
        public Long getTimestamp() { return timestamp; }
        public void setTimestamp(Long timestamp) { this.timestamp = timestamp; }
    }

    public static class PongMessage {
        private Long pingTimestamp;
        private Long pongTimestamp;
        
        public PongMessage() {}
        
        public PongMessage(Long pingTimestamp, Long pongTimestamp) {
            this.pingTimestamp = pingTimestamp;
            this.pongTimestamp = pongTimestamp;
        }
        
        public Long getPingTimestamp() { return pingTimestamp; }
        public void setPingTimestamp(Long pingTimestamp) { this.pingTimestamp = pingTimestamp; }
        public Long getPongTimestamp() { return pongTimestamp; }
        public void setPongTimestamp(Long pongTimestamp) { this.pongTimestamp = pongTimestamp; }
    }
}
