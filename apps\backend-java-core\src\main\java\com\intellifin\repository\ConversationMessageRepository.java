package com.intellifin.repository;

import com.intellifin.model.Conversation;
import com.intellifin.model.ConversationMessage;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

@Repository
public interface ConversationMessageRepository extends JpaRepository<ConversationMessage, UUID> {

    /**
     * Find messages by conversation
     */
    List<ConversationMessage> findByConversationOrderByCreatedAtAsc(Conversation conversation);

    /**
     * Find messages by conversation with pagination
     */
    Page<ConversationMessage> findByConversationOrderByCreatedAtAsc(Conversation conversation, Pageable pageable);

    /**
     * Find recent messages by conversation
     */
    @Query("SELECT m FROM ConversationMessage m WHERE m.conversation = :conversation ORDER BY m.createdAt DESC")
    List<ConversationMessage> findRecentByConversation(@Param("conversation") Conversation conversation, Pageable pageable);

    /**
     * Find messages by conversation and type
     */
    List<ConversationMessage> findByConversationAndTypeOrderByCreatedAtAsc(Conversation conversation, ConversationMessage.MessageType type);

    /**
     * Find messages by conversation and role
     */
    List<ConversationMessage> findByConversationAndRoleOrderByCreatedAtAsc(Conversation conversation, ConversationMessage.MessageRole role);

    /**
     * Find messages by conversation and status
     */
    List<ConversationMessage> findByConversationAndStatusOrderByCreatedAtAsc(Conversation conversation, ConversationMessage.MessageStatus status);

    /**
     * Find last message in conversation
     */
    @Query("SELECT m FROM ConversationMessage m WHERE m.conversation = :conversation ORDER BY m.createdAt DESC LIMIT 1")
    Optional<ConversationMessage> findLastByConversation(@Param("conversation") Conversation conversation);

    /**
     * Find pending messages for processing
     */
    @Query("SELECT m FROM ConversationMessage m WHERE m.status = 'PENDING' ORDER BY m.createdAt ASC")
    List<ConversationMessage> findPendingMessages();

    /**
     * Find processing messages (for timeout detection)
     */
    @Query("SELECT m FROM ConversationMessage m WHERE m.status = 'PROCESSING' AND m.createdAt < :timeout")
    List<ConversationMessage> findStuckProcessingMessages(@Param("timeout") LocalDateTime timeout);

    /**
     * Count messages by conversation
     */
    long countByConversation(Conversation conversation);

    /**
     * Count messages by conversation and type
     */
    long countByConversationAndType(Conversation conversation, ConversationMessage.MessageType type);

    /**
     * Count messages by conversation and role
     */
    long countByConversationAndRole(Conversation conversation, ConversationMessage.MessageRole role);

    /**
     * Update message status
     */
    @Modifying
    @Query("UPDATE ConversationMessage m SET m.status = :status WHERE m.id = :messageId")
    void updateStatus(@Param("messageId") UUID messageId, @Param("status") ConversationMessage.MessageStatus status);

    /**
     * Update message processing result
     */
    @Modifying
    @Query("UPDATE ConversationMessage m SET m.intentRecognized = :intent, m.confidenceScore = :confidence, m.processingTimeMs = :processingTime, m.status = 'COMPLETED' WHERE m.id = :messageId")
    void updateProcessingResult(@Param("messageId") UUID messageId, @Param("intent") String intent, @Param("confidence") Double confidence, @Param("processingTime") Long processingTime);

    /**
     * Mark message as failed
     */
    @Modifying
    @Query("UPDATE ConversationMessage m SET m.status = 'FAILED', m.errorMessage = :errorMessage WHERE m.id = :messageId")
    void markAsFailed(@Param("messageId") UUID messageId, @Param("errorMessage") String errorMessage);

    /**
     * Delete old messages
     */
    @Modifying
    @Query("DELETE FROM ConversationMessage m WHERE m.createdAt < :cutoff")
    int deleteOldMessages(@Param("cutoff") LocalDateTime cutoff);

    /**
     * Find messages with content search
     */
    @Query("SELECT m FROM ConversationMessage m WHERE m.conversation = :conversation AND LOWER(m.content) LIKE LOWER(CONCAT('%', :searchTerm, '%')) ORDER BY m.createdAt ASC")
    List<ConversationMessage> findByConversationAndContentContaining(@Param("conversation") Conversation conversation, @Param("searchTerm") String searchTerm);

    /**
     * Find messages by intent
     */
    @Query("SELECT m FROM ConversationMessage m WHERE m.conversation = :conversation AND m.intentRecognized = :intent ORDER BY m.createdAt DESC")
    List<ConversationMessage> findByConversationAndIntent(@Param("conversation") Conversation conversation, @Param("intent") String intent);

    /**
     * Get message statistics for conversation
     */
    @Query("SELECT " +
           "COUNT(m) as totalMessages, " +
           "COUNT(CASE WHEN m.role = 'USER' THEN 1 END) as userMessages, " +
           "COUNT(CASE WHEN m.role = 'ASSISTANT' THEN 1 END) as assistantMessages, " +
           "AVG(m.confidenceScore) as avgConfidence, " +
           "AVG(m.processingTimeMs) as avgProcessingTime " +
           "FROM ConversationMessage m WHERE m.conversation = :conversation")
    Object[] getMessageStats(@Param("conversation") Conversation conversation);

    /**
     * Find messages created after specified time
     */
    @Query("SELECT m FROM ConversationMessage m WHERE m.conversation = :conversation AND m.createdAt > :since ORDER BY m.createdAt ASC")
    List<ConversationMessage> findByConversationAndCreatedAfter(@Param("conversation") Conversation conversation, @Param("since") LocalDateTime since);
}
