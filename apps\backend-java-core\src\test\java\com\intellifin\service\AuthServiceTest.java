package com.intellifin.service;

import com.intellifin.dto.auth.AuthResponse;
import com.intellifin.dto.auth.LoginRequest;
import com.intellifin.dto.auth.RegisterRequest;
import com.intellifin.exception.AuthenticationException;
import com.intellifin.exception.ValidationException;
import com.intellifin.model.EmailVerificationToken;
import com.intellifin.model.User;
import com.intellifin.repository.EmailVerificationTokenRepository;
import com.intellifin.repository.PasswordResetTokenRepository;
import com.intellifin.repository.UserRepository;
import com.intellifin.security.JwtTokenUtil;
import com.intellifin.security.UserPrincipal;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.authentication.BadCredentialsException;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.security.crypto.password.PasswordEncoder;

import java.time.LocalDateTime;
import java.util.Optional;
import java.util.UUID;

import static org.assertj.core.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class AuthServiceTest {

    @Mock
    private UserRepository userRepository;

    @Mock
    private EmailVerificationTokenRepository emailVerificationTokenRepository;

    @Mock
    private PasswordResetTokenRepository passwordResetTokenRepository;

    @Mock
    private PasswordEncoder passwordEncoder;

    @Mock
    private JwtTokenUtil jwtTokenUtil;

    @Mock
    private AuthenticationManager authenticationManager;

    @Mock
    private EmailService emailService;

    @InjectMocks
    private AuthService authService;

    private RegisterRequest validRegisterRequest;
    private LoginRequest validLoginRequest;
    private User testUser;

    @BeforeEach
    void setUp() {
        validRegisterRequest = RegisterRequest.builder()
                .email("<EMAIL>")
                .password("SecurePass123!")
                .confirmPassword("SecurePass123!")
                .firstName("Grace")
                .lastName("Phiri")
                .organizationName("Phiri Catering Services")
                .tpin("**********")
                .build();

        validLoginRequest = LoginRequest.builder()
                .email("<EMAIL>")
                .password("SecurePass123!")
                .rememberMe(false)
                .build();

        testUser = User.builder()
                .id(UUID.randomUUID())
                .email("<EMAIL>")
                .passwordHash("hashedPassword")
                .firstName("Grace")
                .lastName("Phiri")
                .organizationName("Phiri Catering Services")
                .tpin("**********")
                .onboardingStatus(User.OnboardingStatus.STARTED)
                .emailVerified(false)
                .accountLocked(false)
                .failedLoginAttempts(0)
                .createdAt(LocalDateTime.now())
                .updatedAt(LocalDateTime.now())
                .build();
    }

    @Test
    @DisplayName("Should register user successfully with valid data")
    void shouldRegisterUserSuccessfully() {
        // Given
        when(userRepository.existsByEmail(anyString())).thenReturn(false);
        when(passwordEncoder.encode(anyString())).thenReturn("hashedPassword");
        when(userRepository.save(any(User.class))).thenReturn(testUser);
        when(emailVerificationTokenRepository.save(any(EmailVerificationToken.class)))
                .thenReturn(EmailVerificationToken.builder().token("verification-token").build());
        when(jwtTokenUtil.generateToken(any(), any(UUID.class), anyString(), anyString()))
                .thenReturn("jwt-token");
        when(jwtTokenUtil.getExpirationTime()).thenReturn(86400L);
        when(jwtTokenUtil.getExpirationDateTime()).thenReturn(LocalDateTime.now().plusDays(1));

        // When
        AuthResponse response = authService.register(validRegisterRequest);

        // Then
        assertThat(response).isNotNull();
        assertThat(response.getToken()).isEqualTo("jwt-token");
        assertThat(response.getUser().getEmail()).isEqualTo("<EMAIL>");
        assertThat(response.getMessage()).contains("Please check your email");

        verify(userRepository).existsByEmail("<EMAIL>");
        verify(userRepository).save(any(User.class));
        verify(emailService).sendEmailVerification(eq("<EMAIL>"), eq("Grace Phiri"), anyString());
    }

    @Test
    @DisplayName("Should throw exception when user already exists")
    void shouldThrowExceptionWhenUserAlreadyExists() {
        // Given
        when(userRepository.existsByEmail(anyString())).thenReturn(true);

        // When & Then
        assertThatThrownBy(() -> authService.register(validRegisterRequest))
                .isInstanceOf(ValidationException.class)
                .hasMessageContaining("User with this email already exists");

        verify(userRepository, never()).save(any(User.class));
    }

    @Test
    @DisplayName("Should throw exception when passwords don't match")
    void shouldThrowExceptionWhenPasswordsDontMatch() {
        // Given
        validRegisterRequest.setConfirmPassword("DifferentPassword");

        // When & Then
        assertThatThrownBy(() -> authService.register(validRegisterRequest))
                .isInstanceOf(ValidationException.class)
                .hasMessageContaining("Password confirmation does not match");
    }

    @Test
    @DisplayName("Should login user successfully with valid credentials")
    void shouldLoginUserSuccessfully() {
        // Given
        when(userRepository.findByEmail(anyString())).thenReturn(Optional.of(testUser));
        
        Authentication mockAuth = mock(Authentication.class);
        UserPrincipal userPrincipal = UserPrincipal.create(testUser);
        when(mockAuth.getPrincipal()).thenReturn(userPrincipal);
        when(authenticationManager.authenticate(any(UsernamePasswordAuthenticationToken.class)))
                .thenReturn(mockAuth);
        
        when(userRepository.save(any(User.class))).thenReturn(testUser);
        when(jwtTokenUtil.generateToken(any(), any(UUID.class), anyString(), anyString(), anyLong()))
                .thenReturn("jwt-token");
        when(jwtTokenUtil.getExpirationTime()).thenReturn(86400L);

        // When
        AuthResponse response = authService.login(validLoginRequest);

        // Then
        assertThat(response).isNotNull();
        assertThat(response.getToken()).isEqualTo("jwt-token");
        assertThat(response.getUser().getEmail()).isEqualTo("<EMAIL>");
        assertThat(response.getMessage()).isEqualTo("Authentication successful");

        verify(authenticationManager).authenticate(any(UsernamePasswordAuthenticationToken.class));
        verify(userRepository).save(any(User.class));
    }

    @Test
    @DisplayName("Should throw exception for invalid credentials")
    void shouldThrowExceptionForInvalidCredentials() {
        // Given
        when(userRepository.findByEmail(anyString())).thenReturn(Optional.of(testUser));
        when(authenticationManager.authenticate(any(UsernamePasswordAuthenticationToken.class)))
                .thenThrow(new BadCredentialsException("Invalid credentials"));

        // When & Then
        assertThatThrownBy(() -> authService.login(validLoginRequest))
                .isInstanceOf(AuthenticationException.class)
                .hasMessageContaining("Invalid email or password");

        verify(userRepository).findByEmail("<EMAIL>");
    }

    @Test
    @DisplayName("Should handle account lockout after failed attempts")
    void shouldHandleAccountLockoutAfterFailedAttempts() {
        // Given
        testUser.setFailedLoginAttempts(4); // One more attempt will lock the account
        when(userRepository.findByEmail(anyString())).thenReturn(Optional.of(testUser));
        when(authenticationManager.authenticate(any(UsernamePasswordAuthenticationToken.class)))
                .thenThrow(new BadCredentialsException("Invalid credentials"));

        // When & Then
        assertThatThrownBy(() -> authService.login(validLoginRequest))
                .isInstanceOf(AuthenticationException.class);

        verify(userRepository).save(argThat(user -> 
            user.getFailedLoginAttempts() == 5 && user.getAccountLocked()));
    }

    @Test
    @DisplayName("Should verify email successfully with valid token")
    void shouldVerifyEmailSuccessfully() {
        // Given
        String token = "valid-token";
        EmailVerificationToken verificationToken = EmailVerificationToken.builder()
                .token(token)
                .user(testUser)
                .expiresAt(LocalDateTime.now().plusHours(1))
                .used(false)
                .build();

        when(emailVerificationTokenRepository.findValidToken(eq(token), any(LocalDateTime.class)))
                .thenReturn(Optional.of(verificationToken));
        when(userRepository.save(any(User.class))).thenReturn(testUser);
        when(emailVerificationTokenRepository.save(any(EmailVerificationToken.class)))
                .thenReturn(verificationToken);

        // When
        authService.verifyEmail(token);

        // Then
        verify(userRepository).save(argThat(user -> user.getEmailVerified()));
        verify(emailVerificationTokenRepository).save(argThat(EmailVerificationToken::getUsed));
    }

    @Test
    @DisplayName("Should throw exception for invalid verification token")
    void shouldThrowExceptionForInvalidVerificationToken() {
        // Given
        String token = "invalid-token";
        when(emailVerificationTokenRepository.findValidToken(eq(token), any(LocalDateTime.class)))
                .thenReturn(Optional.empty());

        // When & Then
        assertThatThrownBy(() -> authService.verifyEmail(token))
                .isInstanceOf(ValidationException.class)
                .hasMessageContaining("Invalid or expired verification token");
    }

    @Test
    @DisplayName("Should resend email verification successfully")
    void shouldResendEmailVerificationSuccessfully() {
        // Given
        String email = "<EMAIL>";
        testUser.setEmailVerified(false);
        when(userRepository.findByEmail(email)).thenReturn(Optional.of(testUser));
        when(emailVerificationTokenRepository.save(any(EmailVerificationToken.class)))
                .thenReturn(EmailVerificationToken.builder().token("new-token").build());

        // When
        authService.resendEmailVerification(email);

        // Then
        verify(emailVerificationTokenRepository).deleteAllByUser(testUser);
        verify(emailService).sendEmailVerification(eq(email), eq("Grace Phiri"), anyString());
    }

    @Test
    @DisplayName("Should throw exception when trying to resend verification for verified email")
    void shouldThrowExceptionWhenResendingForVerifiedEmail() {
        // Given
        String email = "<EMAIL>";
        testUser.setEmailVerified(true);
        when(userRepository.findByEmail(email)).thenReturn(Optional.of(testUser));

        // When & Then
        assertThatThrownBy(() -> authService.resendEmailVerification(email))
                .isInstanceOf(ValidationException.class)
                .hasMessageContaining("Email is already verified");
    }

    @Test
    @DisplayName("Should handle password reset request gracefully for non-existent user")
    void shouldHandlePasswordResetForNonExistentUser() {
        // Given
        String email = "<EMAIL>";
        when(userRepository.findByEmail(email)).thenReturn(Optional.empty());

        // When
        authService.requestPasswordReset(email);

        // Then
        // Should not throw exception (to prevent email enumeration)
        verify(passwordResetTokenRepository, never()).save(any());
        verify(emailService, never()).sendPasswordReset(anyString(), anyString(), anyString());
    }
}
