# Story 2.1: Basic Conversational Command Processing

**Epic:** Conversational Interface Foundation  
**Status:** In Progress
**Priority:** High  
**Story Points:** 8

## User Story

**As a** user  
**I want** to type natural language commands in a conversational interface  
**So that** I can interact with IntelliFin as if talking to a financial assistant

## Acceptance Criteria

- [x] Persistent command bar is displayed in the main workspace
- [x] User can type and submit natural language commands
- [x] Commands are sent via WebSocket to the conversational gateway
- [x] System provides immediate visual feedback (typing indicator)
- [x] Commands are processed by the AI service for intent recognition
- [x] System responds with appropriate actions or information
- [x] Command history is maintained and displayed
- [x] Error handling for network failures and invalid commands
- [x] Graceful degradation when AI service is unavailable
- [x] API contract is defined and documented
- [ ] Command processing events are published via messaging abstraction layer
- [ ] AI service responses are handled through messaging events
- [ ] Command orchestration works consistently across RabbitMQ (local) and Azure Service Bus (production)
- [ ] Failed command processing events are routed to dead-letter queues

## Technical Implementation

### Frontend Changes
- `src/components/conversation/CommandBar.tsx` - Main command input component
- `src/components/conversation/ChatHistory.tsx` - Command and response history
- `src/components/conversation/MessageBubble.tsx` - Individual message display
- `src/hooks/useWebSocket.ts` - WebSocket connection management
- `src/stores/conversationStore.ts` - Conversational state management
- `src/services/websocket.ts` - WebSocket service implementation

### API Gateway Changes
- `src/main/java/com/intellifin/gateway/websocket/ConversationController.java` - WebSocket endpoint
- `src/main/java/com/intellifin/gateway/websocket/WebSocketConfig.java` - WebSocket configuration
- `src/main/java/com/intellifin/gateway/middleware/WebSocketAuthInterceptor.java` - WebSocket authentication

### Service Changes
- **Core Backend**: `src/main/java/com/intellifin/conversation/` - Command processing and orchestration via Spring Cloud Stream
- **Core Backend**: `src/main/java/com/intellifin/messaging/` - Command event publishing and consumption
- **AI Service**: `src/main.py` - Intent recognition and natural language processing with MessagingService integration
- **AI Service**: `src/services/messaging_service.py` - Python messaging abstraction implementation
- **Database**: Conversation history storage and retrieval

### Database Changes
- Conversation table for storing command history
- Message table for individual messages and responses
- User session tracking for conversation context

## API Contracts

```typescript
// WebSocket message contracts
interface CommandMessage {
  type: 'COMMAND';
  command: string;
  timestamp: string;
  sessionId: string;
}

interface ResponseMessage {
  type: 'RESPONSE' | 'ERROR' | 'PROGRESS';
  content: any;
  timestamp: string;
  sessionId: string;
  messageId: string;
}

interface WebSocketAPI {
  // Client to Server
  '/app/conversation/command': CommandMessage;

  // Server to Client
  '/user/queue/responses': ResponseMessage;
  '/user/queue/errors': ErrorMessage;
}

// Internal messaging events for command processing (abstracted across RabbitMQ/Azure Service Bus)
interface ConversationEvents {
  CommandReceived: {
    commandId: string;
    userId: string;
    command: string;
    sessionId: string;
    timestamp: string;
  };
  CommandProcessed: {
    commandId: string;
    intent: string;
    entities: any[];
    confidence: number;
    processingTime: number;
  };
  CommandFailed: {
    commandId: string;
    error: string;
    retryCount: number;
    timestamp: string;
  };
  AIResponseGenerated: {
    commandId: string;
    response: any;
    responseType: string;
    generationTime: number;
  };
}
```

## Error Handling

- **Network Failures:** Automatic reconnection with exponential backoff
- **Invalid Commands:** Clear error messages with suggestions
- **AI Service Unavailable:** Fallback responses and retry mechanisms via messaging dead-letter queues
- **WebSocket Connection Loss:** Graceful reconnection and message queuing
- **Authentication Failures:** Redirect to login with session preservation
- **Messaging Broker Connectivity:** Issues handled by abstraction layer with automatic failover
- **Failed Command Processing:** Events routed to dead-letter queues (RabbitMQ DLX or Azure Service Bus DLQ)
- **Event Processing Failures:** Retry mechanisms with exponential backoff and manual intervention queues

## Definition of Done

- [x] User can type and submit commands through the command bar
- [x] Commands are processed and responses are displayed
- [x] WebSocket connection is stable and handles reconnection
- [x] Command history is maintained and accessible
- [x] Error scenarios are handled gracefully with user-friendly messages
- [x] Performance meets real-time interaction requirements (< 500ms feedback)
- [x] Authentication is properly integrated with WebSocket
- [ ] Tests cover command processing and error scenarios
- [x] Service can be deployed independently
- [x] No breaking changes to other services
- [ ] Command events work consistently on both RabbitMQ (local) and Azure Service Bus (production)
- [ ] Messaging abstraction layer handles AI service communication failures gracefully
- [ ] Dead-letter queue processing is implemented for failed command events
- [ ] Event-driven command orchestration maintains business logic environment independence

## Dependencies

- [Story 1.1: User Registration and Authentication Flow](../epic-01/story-1.1-user-auth.md)
- [Story 1.2: Monorepo Setup and CI/CD Pipeline](../epic-01/story-1.2-monorepo-cicd.md)

## Notes

This story establishes the core conversational interface that all other user interactions will build upon. The WebSocket connection must be reliable and the command processing must be fast to provide a smooth user experience.

---

**Related Stories:**
- [Story 2.2: Intent Recognition and Entity Extraction](story-2.2-intent-recognition.md)

**Epic:** [Conversational Interface Foundation](../../epics-and-stories.md#epic-2-conversational-interface-foundation) 