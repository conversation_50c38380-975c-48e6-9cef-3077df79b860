# IntelliFin Architecture - Tech Stack

## 3. Tech Stack

This section defines the definitive technology selection for the entire project.

### 3.1 Technology Stack Table

| Category | Technology | Version | Purpose | Rationale |
| :--------- | :--------- | :--------- | :--------- | :--------- |
| Frontend Language | TypeScript | 5.x | Primary language for frontend development, ensuring type safety and robust code. | Enhanced developer experience, reduced runtime errors, improved code maintainability. |
| Frontend Framework | Next.js | 14.x | React framework for building server-rendered and static web applications. | Performance optimization (SSR/SSG), SEO benefits, simplified routing, robust ecosystem. |
| UI Component Library | Shadcn/ui | Latest | Collection of customizable UI components built with Radix UI and Tailwind CSS. | Shadcn/ui is not a traditional component library; it's a collection of reusable components that we copy into our project. This aligns perfectly with our "greenfield, full control" philosophy. It gives us maximum customizability and avoids dependency bloat, while being built on modern, accessible primitives (Tailwind CSS and Radix UI). This choice ensures a modern, trustworthy look and feel from day one. |
| State Management | Zustand | Latest | Lightweight and flexible state management for local and global states. | For our MVP, we must avoid over-engineering. Zustand is a powerful yet minimalist state management solution that provides just what we need without the heavy boilerplate of other libraries. Its simplicity will improve developer velocity and is a perfect fit for the focused, conversational UI we are building. |
| Backend Language (Core) | Java | 17/21 LTS | Robust, mature language for building scalable and secure financial services. | Enterprise-grade reliability, strong ecosystem, high performance, excellent security features. |
| Backend Framework (Core) | Spring Boot | 3.x | Opinionated framework for building production-ready, stand-alone Java applications. | Rapid development, strong convention over configuration, extensive features (security, data access). |
| Backend Language (AI/ML) | Python | 3.10+ | Widely used for AI/ML, rich ecosystem of libraries. | Access to cutting-edge AI/ML libraries (LangChain, TensorFlow, PyTorch), rapid prototyping. |
| Backend Framework (AI/ML) | FastAPI | Latest | Modern, fast (high-performance) web framework for building APIs with Python. | Asynchronous support, automatic OpenAPI/Swagger documentation, Pydantic for data validation. |
| API Style | Hybrid (WebSocket & REST) | N/A | WebSocket for real-time conversational gateway, RESTful for resource management. | Optimizes real-time chat interactions while providing structured access to financial data. |
| Database | PostgreSQL | 15.x | Robust, open-source relational database. | ACID compliance, strong support for complex queries, extensibility, widely adopted. |
| Cache | Redis | Latest | In-memory data store for caching and real-time data needs. | High performance, supports various data structures (e.g., for session management, real-time analytics). |
| File Storage | Google Cloud Storage | N/A | Scalable, highly available object storage. | Securely store documents (invoices, receipts), backups, and other large data. |
| Authentication | Spring Security with JWT | N/A | Industry-standard security framework for user authentication and authorization. | Leveraging the robust, industry-standard security framework built into our core backend platform is the most secure and capital-efficient choice. Implementing a stateless JWT (JSON Web Token) authentication flow is the standard for modern PWAs interacting with microservices and ensures we build a secure, scalable solution in-house without incurring third-party costs initially. |
| Frontend Testing | React Testing Library / Jest | Latest | User-centric testing for React components. | Encourages testing actual user behavior, fast execution. |
| Backend Testing | JUnit / Mockito (Java); Pytest (Python) | Latest | Unit and integration testing frameworks for respective backend languages. | Comprehensive test coverage, robust test capabilities. |
| E2E Testing | Playwright | Latest | End-to-end testing for web applications. | Cross-browser support, reliable, faster than Selenium. |
| Build Tool | Gradle (Java); Poetry (Python); npm/Yarn (Next.js) | Latest | Build automation for multi-language monorepo. | Efficient dependency management, build processes across services. |
| Bundler | Webpack (via Next.js) | N/A | JavaScript module bundler. | Optimized asset delivery for frontend. |
| IaC Tool | Terraform | Latest | Infrastructure as Code for provisioning and managing cloud resources. | Version-controlled, reproducible infrastructure, reduces manual errors. |
| CI/CD | GitHub Actions | N/A | Automated workflows for continuous integration and continuous deployment. | Streamlined development pipeline, automated testing and deployment. |
| Monitoring | Azure Monitor / Application Insights | N/A | Centralized monitoring for application health, performance, and resource utilization. | We are all-in on Azure. Leveraging the native, first-party monitoring solution is the most strategic choice. It will provide seamless integration with our entire stack (App Services, Databases, etc.), giving us a single pane of glass for distributed tracing, performance metrics, and log analysis with minimal setup and operational overhead. |
| Logging | SLF4J / Logback (JSON) | Latest | Centralized application logging and error reporting in structured JSON format. | Using the standard is efficient. The key is outputting structured JSON logs, which will allow for easy, automated ingestion and searching by our monitoring platform. |
| CSS Framework | Tailwind CSS | 3.x | Utility-first CSS framework. | Rapid UI development, highly customizable, small production CSS file sizes. |

---

**Previous Section:** [High Level Architecture](02-high-level-architecture.md)  
**Next Section:** [Data Models](04-data-models.md) 