# IntelliFin Architecture - External Integrations

## 6. External Integrations

This section outlines IntelliFin's strategy for integrating with key external financial services and regulatory bodies. Our approach prioritizes reliability, security, and compliance, leveraging asynchronous communication patterns where appropriate.

### 6.1 MTN Mobile Money API

*   **Purpose:** To enable users to connect their MTN Mobile Money accounts, fetch transaction data, and potentially initiate payments (future phase).
*   **Integration Method:** Primarily via **Webhooks (preferred)** and API polling (as a fallback/supplementary mechanism).
    *   **Webhook Prioritization:** We will strongly prioritize integrating with MTN's webhook capabilities (if available) for real-time transaction notifications. This minimizes the need for frequent polling, reduces API call costs, and ensures timely data synchronization. **Our initial technical due diligence must focus on confirming the availability and robustness of MTN's webhook service. If webhooks are not available, our backup polling strategy must be designed for maximum efficiency to minimize cost and system load.**
    *   **Fallback/Polling:** If comprehensive webhooks are not available or sufficient, a robust polling mechanism will be implemented for periodic transaction fetching.
*   **Security:** OAuth 2.0 or API key-based authentication with MTN, secure storage of credentials, and strict validation of incoming webhook payloads.
*   **Data Flow:**
        1.  User initiates connection via IntelliFin UI (triggers `POST /api/v1/financial-accounts/connect/mtn`).
        2.  IntelliFin backend completes authentication/authorization with MTN.
        3.  MTN pushes transaction notifications via webhook to IntelliFin's secure endpoint.
        4.  IntelliFin polls MTN API for historical transactions and reconciliation.
        5.  Fetched transactions are processed, categorized (via AI Service), and stored in our database.
*   **Error Handling:** Robust retry mechanisms for API calls, dead-letter queues for webhook processing failures, and clear alerting for integration issues.

### 6.2 Stitch Aggregator API

*   **Purpose:** To connect to a broader range of banks and financial institutions in Zambia for fetching transaction data, enhancing financial visibility beyond mobile money.
*   **Integration Method:** Standard RESTful API integration.
*   **Security:** OAuth 2.0 for user consent and secure data access, ensuring compliance with data privacy regulations. All sensitive data (e.g., access tokens) will be encrypted at rest and in transit.
*   **Data Flow:**
    1.  User initiates bank connection via IntelliFin UI.
    2.  IntelliFin redirects user to Stitch for bank authentication (OAuth flow).
    3.  Stitch provides API tokens to IntelliFin after successful authorization.
    4.  IntelliFin uses Stitch APIs to fetch account balances and transaction histories.
    5.  Fetched data is processed, categorized, and stored.
*   **Considerations:** Handling varying data formats and latency across different bank integrations via Stitch.

### 6.3 ZRA VSDC API (Zambia Revenue Authority Virtual Sales Device Controller)

*   **Purpose:** To ensure all invoices generated by IntelliFin users are officially registered and compliant with the Zambia Revenue Authority's electronic invoicing requirements.
*   **Integration Method:** Internal API Contract with a dedicated **ZRA Compliance Service**.
    *   **Internal API Contract:** The core Spring Boot services will *not* directly call the external ZRA VSDC API. Instead, they will communicate with a dedicated `ZRA Compliance Service` (a separate Java microservice, as identified in the Tech Stack), which will encapsulate all ZRA-specific logic, data formatting, and external API calls. **This is not a simple library call; it's a service-to-service communication link (e.g., via a private REST or gRPC API). Defining this internal API clearly will ensure a clean separation of concerns, allowing the ZRA component to be managed and updated independently without impacting the core backend logic.**
    *   **Rationale:** This promotes strong decoupling, isolates ZRA compliance logic, simplifies testing, and allows the `ZRA Compliance Service` to handle specific ZRA requirements (e.g., Java application, specific libraries, local VSDC client communication) without burdening other services.
*   **Security & Compliance:** Adherence to ZRA's stringent security protocols, proper handling of TPINs, and secure communication with the VSDC API.
*   **Data Flow (as per User Flow 3.1.1 for Invoice):**
    1.  IntelliFin user drafts and approves an invoice.
    2.  Core backend orchestrator calls the internal `ZRA Compliance Service` API (e.g., `POST /internal/zra/invoices/submit`).
    3.  `ZRA Compliance Service` formats invoice data according to ZRA VSDC specifications.
    4.  `ZRA Compliance Service` communicates with the external ZRA VSDC API.
    5.  `ZRA Compliance Service` receives response from ZRA, updates invoice status (`zraStatus`) and `zraInvoiceId` in the database, and pushes confirmation/error to frontend via WebSocket.
*   **Error Handling:** Robust error reporting from the `ZRA Compliance Service` back to the core backend, providing user-friendly guidance on resolution (e.g., invalid TPIN, ZRA service unavailable).

### 6.4 General Integration Principles

*   **Idempotency:** Implement idempotency for all external API calls where possible to prevent duplicate processing of events or transactions.
*   **Circuit Breaker Pattern:** Utilize circuit breakers to prevent cascading failures when external services are unavailable or slow.
*   **Data Mapping & Transformation:** Implement clear data mapping layers to translate external API responses into IntelliFin's internal data models and vice-versa.
*   **Rate Limiting:** Adhere to and manage external API rate limits to avoid throttling or service interruptions.
*   **Monitoring & Alerting:** Comprehensive monitoring and alerting for all integration points to quickly detect and respond to failures or anomalies.

---

**Previous Section:** [API Specification](05-api-specification.md)  
**Next Section:** [Security & Compliance](07-security-compliance.md) 