package com.intellifin.repository;

import com.intellifin.model.Conversation;
import com.intellifin.model.User;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.Nested;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.orm.jpa.DataJpaTest;
import org.springframework.boot.test.autoconfigure.orm.jpa.TestEntityManager;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.test.context.ActiveProfiles;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

import static org.assertj.core.api.Assertions.*;

@DataJpaTest
@ActiveProfiles("test")
class ConversationRepositoryTest {

    @Autowired
    private TestEntityManager entityManager;

    @Autowired
    private ConversationRepository conversationRepository;

    private User testUser1;
    private User testUser2;
    private Conversation activeConversation1;
    private Conversation activeConversation2;
    private Conversation archivedConversation;
    private Conversation deletedConversation;

    @BeforeEach
    void setUp() {
        // Create test users
        testUser1 = User.builder()
                .email("<EMAIL>")
                .firstName("John")
                .lastName("Doe")
                .passwordHash("hashedPassword")
                .emailVerified(true)
                .accountLocked(false)
                .failedLoginAttempts(0)
                .build();
        testUser1 = entityManager.persistAndFlush(testUser1);

        testUser2 = User.builder()
                .email("<EMAIL>")
                .firstName("Jane")
                .lastName("Smith")
                .passwordHash("hashedPassword")
                .emailVerified(true)
                .accountLocked(false)
                .failedLoginAttempts(0)
                .build();
        testUser2 = entityManager.persistAndFlush(testUser2);

        // Create test conversations
        LocalDateTime now = LocalDateTime.now();
        
        activeConversation1 = Conversation.builder()
                .user(testUser1)
                .title("Active Conversation 1")
                .sessionId("session-1")
                .status(Conversation.ConversationStatus.ACTIVE)
                .lastActivityAt(now.minusHours(1))
                .build();
        activeConversation1 = entityManager.persistAndFlush(activeConversation1);

        activeConversation2 = Conversation.builder()
                .user(testUser1)
                .title("Active Conversation 2")
                .sessionId("session-2")
                .status(Conversation.ConversationStatus.ACTIVE)
                .lastActivityAt(now.minusHours(2))
                .build();
        activeConversation2 = entityManager.persistAndFlush(activeConversation2);

        archivedConversation = Conversation.builder()
                .user(testUser1)
                .title("Archived Conversation")
                .sessionId("session-archived")
                .status(Conversation.ConversationStatus.ARCHIVED)
                .lastActivityAt(now.minusDays(1))
                .build();
        archivedConversation = entityManager.persistAndFlush(archivedConversation);

        deletedConversation = Conversation.builder()
                .user(testUser2)
                .title("Deleted Conversation")
                .sessionId("session-deleted")
                .status(Conversation.ConversationStatus.DELETED)
                .lastActivityAt(now.minusDays(2))
                .build();
        deletedConversation = entityManager.persistAndFlush(deletedConversation);

        entityManager.clear();
    }

    @Nested
    @DisplayName("Basic CRUD Operations")
    class BasicCrudOperations {

        @Test
        @DisplayName("Should save and find conversation by ID")
        void shouldSaveAndFindConversationById() {
            // Given
            Conversation newConversation = Conversation.builder()
                    .user(testUser1)
                    .title("New Conversation")
                    .sessionId("new-session")
                    .status(Conversation.ConversationStatus.ACTIVE)
                    .lastActivityAt(LocalDateTime.now())
                    .build();

            // When
            Conversation saved = conversationRepository.save(newConversation);
            Optional<Conversation> found = conversationRepository.findById(saved.getId());

            // Then
            assertThat(found).isPresent();
            assertThat(found.get().getTitle()).isEqualTo("New Conversation");
            assertThat(found.get().getSessionId()).isEqualTo("new-session");
            assertThat(found.get().getStatus()).isEqualTo(Conversation.ConversationStatus.ACTIVE);
        }

        @Test
        @DisplayName("Should delete conversation")
        void shouldDeleteConversation() {
            // Given
            UUID conversationId = activeConversation1.getId();

            // When
            conversationRepository.deleteById(conversationId);

            // Then
            Optional<Conversation> found = conversationRepository.findById(conversationId);
            assertThat(found).isEmpty();
        }

        @Test
        @DisplayName("Should find all conversations")
        void shouldFindAllConversations() {
            // When
            List<Conversation> allConversations = conversationRepository.findAll();

            // Then
            assertThat(allConversations).hasSize(4);
        }
    }

    @Nested
    @DisplayName("Session ID Queries")
    class SessionIdQueries {

        @Test
        @DisplayName("Should find conversation by session ID")
        void shouldFindConversationBySessionId() {
            // When
            Optional<Conversation> found = conversationRepository.findBySessionId("session-1");

            // Then
            assertThat(found).isPresent();
            assertThat(found.get().getTitle()).isEqualTo("Active Conversation 1");
            assertThat(found.get().getStatus()).isEqualTo(Conversation.ConversationStatus.ACTIVE);
        }

        @Test
        @DisplayName("Should return empty when session ID not found")
        void shouldReturnEmptyWhenSessionIdNotFound() {
            // When
            Optional<Conversation> found = conversationRepository.findBySessionId("non-existent");

            // Then
            assertThat(found).isEmpty();
        }

        @Test
        @DisplayName("Should find active conversation by session ID")
        void shouldFindActiveConversationBySessionId() {
            // When
            Optional<Conversation> found = conversationRepository.findActiveBySessionId("session-1");

            // Then
            assertThat(found).isPresent();
            assertThat(found.get().getStatus()).isEqualTo(Conversation.ConversationStatus.ACTIVE);
        }

        @Test
        @DisplayName("Should not find archived conversation when searching for active by session ID")
        void shouldNotFindArchivedConversationWhenSearchingForActiveBySessionId() {
            // When
            Optional<Conversation> found = conversationRepository.findActiveBySessionId("session-archived");

            // Then
            assertThat(found).isEmpty();
        }
    }

    @Nested
    @DisplayName("User-based Queries")
    class UserBasedQueries {

        @Test
        @DisplayName("Should find conversations by user ordered by last activity")
        void shouldFindConversationsByUserOrderedByLastActivity() {
            // When
            List<Conversation> conversations = conversationRepository.findByUserOrderByLastActivityAtDesc(testUser1);

            // Then
            assertThat(conversations).hasSize(3); // 2 active + 1 archived
            assertThat(conversations.get(0).getTitle()).isEqualTo("Active Conversation 1"); // Most recent
            assertThat(conversations.get(1).getTitle()).isEqualTo("Active Conversation 2");
            assertThat(conversations.get(2).getTitle()).isEqualTo("Archived Conversation"); // Oldest
        }

        @Test
        @DisplayName("Should find active conversations by user")
        void shouldFindActiveConversationsByUser() {
            // When
            List<Conversation> activeConversations = conversationRepository.findActiveByUser(testUser1);

            // Then
            assertThat(activeConversations).hasSize(2);
            assertThat(activeConversations)
                    .extracting(Conversation::getStatus)
                    .containsOnly(Conversation.ConversationStatus.ACTIVE);
            assertThat(activeConversations.get(0).getTitle()).isEqualTo("Active Conversation 1"); // Most recent
        }

        @Test
        @DisplayName("Should find conversations by user with pagination")
        void shouldFindConversationsByUserWithPagination() {
            // Given
            Pageable pageable = PageRequest.of(0, 2);

            // When
            Page<Conversation> conversationPage = conversationRepository.findByUserOrderByLastActivityAtDesc(testUser1, pageable);

            // Then
            assertThat(conversationPage.getContent()).hasSize(2);
            assertThat(conversationPage.getTotalElements()).isEqualTo(3);
            assertThat(conversationPage.getTotalPages()).isEqualTo(2);
            assertThat(conversationPage.getContent().get(0).getTitle()).isEqualTo("Active Conversation 1");
        }

        @Test
        @DisplayName("Should find conversations by user and status")
        void shouldFindConversationsByUserAndStatus() {
            // When
            List<Conversation> archivedConversations = conversationRepository
                    .findByUserAndStatusOrderByLastActivityAtDesc(testUser1, Conversation.ConversationStatus.ARCHIVED);

            // Then
            assertThat(archivedConversations).hasSize(1);
            assertThat(archivedConversations.get(0).getTitle()).isEqualTo("Archived Conversation");
        }
    }

    @Nested
    @DisplayName("Time-based Queries")
    class TimeBasedQueries {

        @Test
        @DisplayName("Should find recent conversations by user")
        void shouldFindRecentConversationsByUser() {
            // Given
            LocalDateTime since = LocalDateTime.now().minusHours(3);

            // When
            List<Conversation> recentConversations = conversationRepository.findRecentByUser(testUser1, since);

            // Then
            assertThat(recentConversations).hasSize(2); // Only the two active conversations from last 3 hours
            assertThat(recentConversations)
                    .extracting(Conversation::getTitle)
                    .containsExactly("Active Conversation 1", "Active Conversation 2");
        }

        @Test
        @DisplayName("Should find conversations with activity since specified time")
        void shouldFindConversationsWithActivitySinceSpecifiedTime() {
            // Given
            LocalDateTime since = LocalDateTime.now().minusHours(3);

            // When
            List<Conversation> conversations = conversationRepository.findWithActivitySince(since);

            // Then
            assertThat(conversations).hasSize(2); // Only conversations from last 3 hours
        }

        @Test
        @DisplayName("Should find inactive conversations for cleanup")
        void shouldFindInactiveConversationsForCleanup() {
            // Given
            LocalDateTime cutoff = LocalDateTime.now().minusHours(1).minusMinutes(30);

            // When
            List<Conversation> inactiveConversations = conversationRepository.findInactiveConversations(cutoff);

            // Then
            assertThat(inactiveConversations).hasSize(1); // Only activeConversation2 is older than cutoff
            assertThat(inactiveConversations.get(0).getTitle()).isEqualTo("Active Conversation 2");
        }
    }

    @Nested
    @DisplayName("Counting Operations")
    class CountingOperations {

        @Test
        @DisplayName("Should count conversations by user")
        void shouldCountConversationsByUser() {
            // When
            long count = conversationRepository.countByUser(testUser1);

            // Then
            assertThat(count).isEqualTo(3); // 2 active + 1 archived
        }

        @Test
        @DisplayName("Should count active conversations by user")
        void shouldCountActiveConversationsByUser() {
            // When
            long activeCount = conversationRepository.countActiveByUser(testUser1);

            // Then
            assertThat(activeCount).isEqualTo(2); // Only active conversations
        }

        @Test
        @DisplayName("Should return zero count for user with no conversations")
        void shouldReturnZeroCountForUserWithNoConversations() {
            // Given
            User newUser = User.builder()
                    .email("<EMAIL>")
                    .firstName("New")
                    .lastName("User")
                    .passwordHash("hashedPassword")
                    .emailVerified(true)
                    .accountLocked(false)
                    .failedLoginAttempts(0)
                    .build();
            newUser = entityManager.persistAndFlush(newUser);

            // When
            long count = conversationRepository.countByUser(newUser);
            long activeCount = conversationRepository.countActiveByUser(newUser);

            // Then
            assertThat(count).isZero();
            assertThat(activeCount).isZero();
        }
    }

    @Nested
    @DisplayName("Modifying Operations")
    class ModifyingOperations {

        @Test
        @DisplayName("Should update conversation status")
        void shouldUpdateConversationStatus() {
            // Given
            UUID conversationId = activeConversation1.getId();

            // When
            conversationRepository.updateStatus(conversationId, Conversation.ConversationStatus.ARCHIVED);
            entityManager.flush();
            entityManager.clear();

            // Then
            Optional<Conversation> updated = conversationRepository.findById(conversationId);
            assertThat(updated).isPresent();
            assertThat(updated.get().getStatus()).isEqualTo(Conversation.ConversationStatus.ARCHIVED);
        }

        @Test
        @DisplayName("Should update last activity time")
        void shouldUpdateLastActivityTime() {
            // Given
            UUID conversationId = activeConversation1.getId();
            LocalDateTime newActivityTime = LocalDateTime.now().plusHours(1);

            // When
            conversationRepository.updateLastActivity(conversationId, newActivityTime);
            entityManager.flush();
            entityManager.clear();

            // Then
            Optional<Conversation> updated = conversationRepository.findById(conversationId);
            assertThat(updated).isPresent();
            assertThat(updated.get().getLastActivityAt()).isEqualToIgnoringNanos(newActivityTime);
        }

        @Test
        @DisplayName("Should archive old conversations")
        void shouldArchiveOldConversations() {
            // Given
            LocalDateTime cutoff = LocalDateTime.now().minusHours(1).minusMinutes(30);

            // When
            int archivedCount = conversationRepository.archiveOldConversations(cutoff);
            entityManager.flush();
            entityManager.clear();

            // Then
            assertThat(archivedCount).isEqualTo(1); // Only activeConversation2 should be archived

            // Verify the conversation was actually archived
            Optional<Conversation> archived = conversationRepository.findById(activeConversation2.getId());
            assertThat(archived).isPresent();
            assertThat(archived.get().getStatus()).isEqualTo(Conversation.ConversationStatus.ARCHIVED);

            // Verify other active conversation remains active
            Optional<Conversation> stillActive = conversationRepository.findById(activeConversation1.getId());
            assertThat(stillActive).isPresent();
            assertThat(stillActive.get().getStatus()).isEqualTo(Conversation.ConversationStatus.ACTIVE);
        }

        @Test
        @DisplayName("Should delete conversations by status and updated before cutoff")
        void shouldDeleteConversationsByStatusAndUpdatedBeforeCutoff() {
            // Given
            LocalDateTime cutoff = LocalDateTime.now().minusDays(1).minusHours(1);

            // When
            int deletedCount = conversationRepository.deleteByStatusAndUpdatedBefore(
                    Conversation.ConversationStatus.DELETED, cutoff);
            entityManager.flush();
            entityManager.clear();

            // Then
            assertThat(deletedCount).isEqualTo(1); // Only the deleted conversation should be removed

            // Verify the conversation was actually deleted
            Optional<Conversation> deleted = conversationRepository.findById(deletedConversation.getId());
            assertThat(deleted).isEmpty();

            // Verify other conversations remain
            assertThat(conversationRepository.findById(activeConversation1.getId())).isPresent();
            assertThat(conversationRepository.findById(archivedConversation.getId())).isPresent();
        }
    }

    @Nested
    @DisplayName("Search Operations")
    class SearchOperations {

        @Test
        @DisplayName("Should find conversations by title search")
        void shouldFindConversationsByTitleSearch() {
            // When
            List<Conversation> found = conversationRepository.findByUserAndTitleContaining(testUser1, "Active");

            // Then
            assertThat(found).hasSize(2);
            assertThat(found)
                    .extracting(Conversation::getTitle)
                    .containsExactly("Active Conversation 1", "Active Conversation 2");
        }

        @Test
        @DisplayName("Should find conversations by case-insensitive title search")
        void shouldFindConversationsByCaseInsensitiveTitleSearch() {
            // When
            List<Conversation> found = conversationRepository.findByUserAndTitleContaining(testUser1, "ACTIVE");

            // Then
            assertThat(found).hasSize(2);
        }

        @Test
        @DisplayName("Should return empty list when no conversations match search")
        void shouldReturnEmptyListWhenNoConversationsMatchSearch() {
            // When
            List<Conversation> found = conversationRepository.findByUserAndTitleContaining(testUser1, "NonExistent");

            // Then
            assertThat(found).isEmpty();
        }

        @Test
        @DisplayName("Should find conversations with partial title match")
        void shouldFindConversationsWithPartialTitleMatch() {
            // When
            List<Conversation> found = conversationRepository.findByUserAndTitleContaining(testUser1, "Conversation 1");

            // Then
            assertThat(found).hasSize(1);
            assertThat(found.get(0).getTitle()).isEqualTo("Active Conversation 1");
        }
    }

    @Nested
    @DisplayName("Statistics Operations")
    class StatisticsOperations {

        @Test
        @DisplayName("Should get conversation statistics for user")
        void shouldGetConversationStatisticsForUser() {
            // When
            Object[] stats = conversationRepository.getConversationStats(testUser1);

            // Then
            assertThat(stats).isNotNull();
            assertThat(stats).hasSize(4);

            // Total conversations
            assertThat(((Number) stats[0]).longValue()).isEqualTo(3L);

            // Active conversations
            assertThat(((Number) stats[1]).longValue()).isEqualTo(2L);

            // Archived conversations
            assertThat(((Number) stats[2]).longValue()).isEqualTo(1L);

            // Last activity should be from activeConversation1 (most recent)
            assertThat(stats[3]).isNotNull();
        }

        @Test
        @DisplayName("Should return zero statistics for user with no conversations")
        void shouldReturnZeroStatisticsForUserWithNoConversations() {
            // Given
            User newUser = User.builder()
                    .email("<EMAIL>")
                    .firstName("New")
                    .lastName("User")
                    .passwordHash("hashedPassword")
                    .emailVerified(true)
                    .accountLocked(false)
                    .failedLoginAttempts(0)
                    .build();
            newUser = entityManager.persistAndFlush(newUser);

            // When
            Object[] stats = conversationRepository.getConversationStats(newUser);

            // Then
            assertThat(stats).isNotNull();
            assertThat(((Number) stats[0]).longValue()).isZero(); // Total conversations
            assertThat(((Number) stats[1]).longValue()).isZero(); // Active conversations
            assertThat(((Number) stats[2]).longValue()).isZero(); // Archived conversations
            assertThat(stats[3]).isNull(); // Last activity
        }
    }

    @Nested
    @DisplayName("Edge Cases and Error Scenarios")
    class EdgeCasesAndErrorScenarios {

        @Test
        @DisplayName("Should handle null parameters gracefully")
        void shouldHandleNullParametersGracefully() {
            // When & Then
            assertThatThrownBy(() -> conversationRepository.findByUserOrderByLastActivityAtDesc(null))
                    .isInstanceOf(Exception.class);

            assertThatThrownBy(() -> conversationRepository.findActiveByUser(null))
                    .isInstanceOf(Exception.class);

            assertThatThrownBy(() -> conversationRepository.countByUser(null))
                    .isInstanceOf(Exception.class);
        }

        @Test
        @DisplayName("Should handle empty session ID")
        void shouldHandleEmptySessionId() {
            // When
            Optional<Conversation> found = conversationRepository.findBySessionId("");

            // Then
            assertThat(found).isEmpty();
        }

        @Test
        @DisplayName("Should handle very old date parameters")
        void shouldHandleVeryOldDateParameters() {
            // Given
            LocalDateTime veryOldDate = LocalDateTime.of(1900, 1, 1, 0, 0);

            // When
            List<Conversation> recent = conversationRepository.findRecentByUser(testUser1, veryOldDate);
            List<Conversation> withActivity = conversationRepository.findWithActivitySince(veryOldDate);

            // Then
            assertThat(recent).hasSize(3); // All conversations should be found
            assertThat(withActivity).hasSize(4); // All conversations should be found
        }

        @Test
        @DisplayName("Should handle future date parameters")
        void shouldHandleFutureDateParameters() {
            // Given
            LocalDateTime futureDate = LocalDateTime.now().plusDays(1);

            // When
            List<Conversation> recent = conversationRepository.findRecentByUser(testUser1, futureDate);
            List<Conversation> withActivity = conversationRepository.findWithActivitySince(futureDate);

            // Then
            assertThat(recent).isEmpty(); // No conversations should be found
            assertThat(withActivity).isEmpty(); // No conversations should be found
        }

        @Test
        @DisplayName("Should handle non-existent conversation ID in update operations")
        void shouldHandleNonExistentConversationIdInUpdateOperations() {
            // Given
            UUID nonExistentId = UUID.randomUUID();

            // When & Then - These should not throw exceptions but simply not update anything
            assertThatCode(() -> {
                conversationRepository.updateStatus(nonExistentId, Conversation.ConversationStatus.ARCHIVED);
                conversationRepository.updateLastActivity(nonExistentId, LocalDateTime.now());
            }).doesNotThrowAnyException();
        }
    }
}
