import type { Metada<PERSON> } from "next"
import { Inter } from "next/font/google"
import "./globals.css"

const inter = Inter({ 
  subsets: ["latin"],
  variable: "--font-inter",
})

export const metadata: Metadata = {
  title: "IntelliFin - Accounting as easy as a Conversation",
  description: "Transform your financial operations with AI-powered conversational accounting. Powered by Agentic AI.",
  keywords: ["accounting", "AI", "financial management", "automation", "business intelligence"],
  authors: [{ name: "IntelliFin" }],
  robots: "index, follow",
  openGraph: {
    title: "IntelliFin - Accounting as easy as a Conversation",
    description: "Transform your financial operations with AI-powered conversational accounting. Powered by Agentic AI.",
    type: "website",
    locale: "en_US",
  },
  twitter: {
    card: "summary_large_image",
    title: "IntelliFin - Accounting as easy as a Conversation",
    description: "Transform your financial operations with AI-powered conversational accounting. Powered by Agentic AI.",
  },
}

export const viewport = {
  width: "device-width",
  initialScale: 1,
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="en" className={inter.variable}>
      <body className={`${inter.className} antialiased`}>
        {children}
      </body>
    </html>
  )
}
