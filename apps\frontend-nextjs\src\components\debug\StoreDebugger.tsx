"use client"

import React from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { useAuthStore, useConversationStore, useFinancialStore } from '@/stores';

/**
 * Debug component to test store functionality
 * Only visible in development mode
 */
export function StoreDebugger() {
  const authStore = useAuthStore();
  const conversationStore = useConversationStore();
  const financialStore = useFinancialStore();

  if (process.env.NODE_ENV !== 'development') {
    return null;
  }

  const testAuthStore = () => {
    console.log('Auth Store State:', {
      isAuthenticated: authStore.isAuthenticated,
      user: authStore.user,
      isLoading: authStore.isLoading,
      error: authStore.error,
    });
  };

  const testConversationStore = () => {
    console.log('Conversation Store State:', {
      messages: conversationStore.messages,
      isConnected: conversationStore.isConnected,
      connectionState: conversationStore.connectionState,
      awaitingResponse: conversationStore.awaitingResponse,
    });
    
    // Test adding a message
    conversationStore.addUserMessage('Test message from debugger');
  };

  const testFinancialStore = () => {
    console.log('Financial Store State:', {
      transactions: financialStore.transactions.length,
      accounts: financialStore.accounts.length,
      isLoading: financialStore.isLoading,
      errors: financialStore.errors,
    });
  };

  const simulateAIResponse = () => {
    // Simulate an AI response
    const mockResponse = {
      messageId: `ai_${Date.now()}`,
      content: 'This is a simulated AI response for testing purposes.',
      role: 'ASSISTANT' as const,
      type: 'RESPONSE' as const,
      timestamp: new Date().toISOString(),
      isComplete: true,
      requiresFollowUp: false,
      intentRecognized: 'test_intent',
      confidenceScore: 0.95,
      status: 'COMPLETED' as const,
    };

    conversationStore.processWebSocketMessage(mockResponse);
  };

  return (
    <Card className="fixed bottom-4 right-4 w-80 z-50 bg-yellow-50 border-yellow-200">
      <CardHeader className="pb-2">
        <CardTitle className="text-sm text-yellow-800">🐛 Store Debugger</CardTitle>
      </CardHeader>
      <CardContent className="space-y-2">
        <div className="grid grid-cols-2 gap-2">
          <Button
            size="sm"
            variant="outline"
            onClick={testAuthStore}
            className="text-xs"
          >
            Test Auth
          </Button>
          <Button
            size="sm"
            variant="outline"
            onClick={testConversationStore}
            className="text-xs"
          >
            Test Chat
          </Button>
          <Button
            size="sm"
            variant="outline"
            onClick={testFinancialStore}
            className="text-xs"
          >
            Test Financial
          </Button>
          <Button
            size="sm"
            variant="outline"
            onClick={simulateAIResponse}
            className="text-xs"
          >
            Mock AI Response
          </Button>
        </div>
        <div className="text-xs text-yellow-700">
          Check browser console for output
        </div>
      </CardContent>
    </Card>
  );
}
