# IntelliFin Test Specifications

## Overview

This document provides comprehensive testing specifications for the IntelliFin platform, covering unit tests, integration tests, end-to-end tests, and performance testing.

## 1. Testing Strategy

### 1.1 Testing Pyramid

```
    /\
   /  \     E2E Tests (10%)
  /____\    - Critical user journeys
 /      \   - Cross-service workflows
/________\  Integration Tests (20%)
           - API contracts
           - Service interactions
           - Database operations
___________
           Unit Tests (70%)
           - Business logic
           - Individual components
           - Utility functions
```

### 1.2 Test Coverage Requirements

- **Unit Tests:** Minimum 80% code coverage
- **Integration Tests:** All API endpoints and service interactions
- **E2E Tests:** Critical user workflows (happy paths and error scenarios)
- **Performance Tests:** All public APIs under expected load

## 2. Unit Test Specifications

### 2.1 Backend Core (Java Spring Boot)

```java
// TransactionServiceTest.java
@ExtendWith(MockitoExtension.class)
class TransactionServiceTest {

    @Mock
    private TransactionRepository transactionRepository;
    
    @Mock
    private CategoryService categoryService;
    
    @Mock
    private AIService aiService;
    
    @InjectMocks
    private TransactionService transactionService;

    @Test
    @DisplayName("Should create transaction with AI categorization")
    void shouldCreateTransactionWithAICategorization() {
        // Given
        CreateTransactionRequest request = CreateTransactionRequest.builder()
            .description("Payment to ZESCO for electricity")
            .amount(new BigDecimal("450.00"))
            .type(TransactionType.EXPENSE)
            .date(LocalDate.now())
            .financialAccountId(UUID.randomUUID())
            .build();

        CategorySuggestion aiSuggestion = CategorySuggestion.builder()
            .categoryId(UUID.randomUUID())
            .confidence(0.95)
            .explanation("ZESCO is the national electricity provider")
            .build();

        when(aiService.categorizeTransaction(any())).thenReturn(aiSuggestion);
        when(transactionRepository.save(any())).thenAnswer(invocation -> invocation.getArgument(0));

        // When
        Transaction result = transactionService.createTransaction(request, "user-id");

        // Then
        assertThat(result.getDescription()).isEqualTo(request.getDescription());
        assertThat(result.getAmount()).isEqualTo(request.getAmount());
        assertThat(result.getStatus()).isEqualTo(TransactionStatus.PENDING_CLASSIFICATION);
        assertThat(result.getAiConfidence()).isEqualTo(0.95);
        
        verify(aiService).categorizeTransaction(any());
        verify(transactionRepository).save(any());
    }

    @Test
    @DisplayName("Should handle AI service failure gracefully")
    void shouldHandleAIServiceFailureGracefully() {
        // Given
        CreateTransactionRequest request = createValidTransactionRequest();
        when(aiService.categorizeTransaction(any())).thenThrow(new AIServiceException("Service unavailable"));

        // When
        Transaction result = transactionService.createTransaction(request, "user-id");

        // Then
        assertThat(result.getStatus()).isEqualTo(TransactionStatus.PENDING_CLASSIFICATION);
        assertThat(result.getAiConfidence()).isNull();
        assertThat(result.getAiExplanation()).contains("AI service temporarily unavailable");
    }

    @ParameterizedTest
    @ValueSource(strings = {"", " ", "a".repeat(256)})
    @DisplayName("Should reject invalid transaction descriptions")
    void shouldRejectInvalidDescriptions(String invalidDescription) {
        // Given
        CreateTransactionRequest request = CreateTransactionRequest.builder()
            .description(invalidDescription)
            .amount(new BigDecimal("100.00"))
            .type(TransactionType.EXPENSE)
            .date(LocalDate.now())
            .build();

        // When & Then
        assertThatThrownBy(() -> transactionService.createTransaction(request, "user-id"))
            .isInstanceOf(ValidationException.class)
            .hasMessageContaining("description");
    }
}
```

### 2.2 AI Service (Python FastAPI)

```python
# test_categorization.py
import pytest
from unittest.mock import Mock, patch
from src.services.categorization import TransactionCategorizationService
from src.models.schemas import TransactionCategorizationRequest, CategorySuggestion

class TestTransactionCategorizationService:
    
    @pytest.fixture
    def categorization_service(self):
        return TransactionCategorizationService()
    
    @pytest.mark.asyncio
    async def test_categorize_zesco_payment(self, categorization_service):
        """Test categorization of ZESCO electricity payment"""
        # Given
        request = TransactionCategorizationRequest(
            description="Payment to ZESCO for electricity bill",
            amount=450.00,
            transaction_type="EXPENSE"
        )
        
        # When
        result = await categorization_service.categorize_transaction(request)
        
        # Then
        assert result.category == "Utilities"
        assert result.confidence >= 0.85
        assert "ZESCO" in result.explanation
        assert "electricity" in result.explanation.lower()
    
    @pytest.mark.asyncio
    async def test_categorize_mtn_airtime(self, categorization_service):
        """Test categorization of MTN airtime purchase"""
        # Given
        request = TransactionCategorizationRequest(
            description="MTN airtime purchase K50",
            amount=50.00,
            transaction_type="EXPENSE"
        )
        
        # When
        result = await categorization_service.categorize_transaction(request)
        
        # Then
        assert result.category == "Telecommunications"
        assert result.confidence >= 0.80
        assert "MTN" in result.explanation
    
    @pytest.mark.asyncio
    async def test_categorize_catering_service_income(self, categorization_service):
        """Test categorization of catering service income"""
        # Given
        request = TransactionCategorizationRequest(
            description="Catering service for wedding event",
            amount=2500.00,
            transaction_type="INCOME"
        )
        
        # When
        result = await categorization_service.categorize_transaction(request)
        
        # Then
        assert result.category == "Service Revenue"
        assert result.confidence >= 0.75
        assert "catering" in result.explanation.lower()
    
    @pytest.mark.asyncio
    async def test_low_confidence_categorization(self, categorization_service):
        """Test handling of ambiguous transaction descriptions"""
        # Given
        request = TransactionCategorizationRequest(
            description="Payment K100",
            amount=100.00,
            transaction_type="EXPENSE"
        )
        
        # When
        result = await categorization_service.categorize_transaction(request)
        
        # Then
        assert result.confidence < 0.60
        assert result.category in ["Miscellaneous", "Uncategorized"]
    
    @pytest.mark.asyncio
    @patch('src.services.categorization.get_llm_client')
    async def test_llm_service_failure_fallback(self, mock_llm, categorization_service):
        """Test fallback behavior when LLM service fails"""
        # Given
        mock_llm.side_effect = Exception("LLM service unavailable")
        request = TransactionCategorizationRequest(
            description="Test transaction",
            amount=100.00,
            transaction_type="EXPENSE"
        )
        
        # When
        result = await categorization_service.categorize_transaction(request)
        
        # Then
        assert result.category == "Uncategorized"
        assert result.confidence == 0.0
        assert "service temporarily unavailable" in result.explanation.lower()

# Performance tests
@pytest.mark.performance
class TestCategorizationPerformance:
    
    @pytest.mark.asyncio
    async def test_categorization_latency(self, categorization_service):
        """Test that categorization completes within acceptable time"""
        import time
        
        request = TransactionCategorizationRequest(
            description="Payment to ZESCO for electricity",
            amount=450.00,
            transaction_type="EXPENSE"
        )
        
        start_time = time.time()
        result = await categorization_service.categorize_transaction(request)
        end_time = time.time()
        
        latency = end_time - start_time
        assert latency < 2.0  # Should complete within 2 seconds
        assert result.confidence > 0.0
```

### 2.3 Frontend (Next.js/React)

```typescript
// TransactionList.test.tsx
import { render, screen, waitFor, fireEvent } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { TransactionList } from '@/components/financial/TransactionList';
import { mockTransactions } from '@/tests/mocks/transactions';

const createTestQueryClient = () => new QueryClient({
  defaultOptions: {
    queries: { retry: false },
    mutations: { retry: false },
  },
});

const renderWithProviders = (component: React.ReactElement) => {
  const queryClient = createTestQueryClient();
  return render(
    <QueryClientProvider client={queryClient}>
      {component}
    </QueryClientProvider>
  );
};

describe('TransactionList', () => {
  beforeEach(() => {
    // Mock API calls
    global.fetch = jest.fn();
  });

  afterEach(() => {
    jest.resetAllMocks();
  });

  it('should display transactions when loaded', async () => {
    // Given
    (global.fetch as jest.Mock).mockResolvedValueOnce({
      ok: true,
      json: async () => ({
        transactions: mockTransactions,
        total: mockTransactions.length,
        uncategorizedCount: 2
      }),
    });

    // When
    renderWithProviders(<TransactionList />);

    // Then
    await waitFor(() => {
      expect(screen.getByText('Payment to ZESCO for electricity')).toBeInTheDocument();
      expect(screen.getByText('K450.00')).toBeInTheDocument();
      expect(screen.getByText('Utilities')).toBeInTheDocument();
    });
  });

  it('should handle transaction categorization', async () => {
    // Given
    (global.fetch as jest.Mock)
      .mockResolvedValueOnce({
        ok: true,
        json: async () => ({ transactions: mockTransactions, total: 1, uncategorizedCount: 1 }),
      })
      .mockResolvedValueOnce({
        ok: true,
        json: async () => ({ transaction: { ...mockTransactions[0], status: 'CLASSIFIED' } }),
      });

    renderWithProviders(<TransactionList />);

    // When
    await waitFor(() => screen.getByText('Confirm Category'));
    fireEvent.click(screen.getByText('Confirm Category'));

    // Then
    await waitFor(() => {
      expect(global.fetch).toHaveBeenCalledWith(
        expect.stringContaining('/api/v1/transactions/'),
        expect.objectContaining({
          method: 'PUT',
          headers: expect.objectContaining({
            'Content-Type': 'application/json',
          }),
        })
      );
    });
  });
});
```
