spring:
  datasource:
    url: jdbc:h2:mem:testdb;DB_CLOSE_DELAY=-1;DB_CLOSE_ON_EXIT=FALSE
    driver-class-name: org.h2.Driver
    username: sa
    password: 
  
  jpa:
    hibernate:
      ddl-auto: create-drop
    show-sql: true
    properties:
      hibernate:
        dialect: org.hibernate.dialect.H2Dialect
        format_sql: true
        use_sql_comments: true
  
  h2:
    console:
      enabled: true
  
  flyway:
    enabled: false
  
  data:
    redis:
      url: redis://localhost:6379
      timeout: 2000ms
      lettuce:
        pool:
          max-active: 8
          max-idle: 8
          min-idle: 0

logging:
  level:
    com.intellifin: DEBUG
    org.springframework.web: DEBUG
    org.hibernate.SQL: DEBUG
    org.hibernate.type.descriptor.sql.BasicBinder: TRACE
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} - %msg%n"

# JWT Configuration for tests
jwt:
  secret: test-secret-key-for-testing-only
  expiration: 86400

# External Services (mocked in tests)
external:
  ai-service:
    url: http://localhost:8000
    timeout: 30s
  zra-service:
    url: http://localhost:8081
    timeout: 30s

# Disable email service for tests
app:
  email:
    enabled: false
