package com.intellifin.service;

import com.intellifin.dto.conversation.ConversationCommandRequest;
import com.intellifin.dto.conversation.ConversationResponse;
import com.intellifin.model.Conversation;
import com.intellifin.model.ConversationMessage;
import com.intellifin.model.User;
import com.intellifin.repository.ConversationMessageRepository;
import com.intellifin.repository.ConversationRepository;
import com.intellifin.repository.UserRepository;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.messaging.simp.SimpMessagingTemplate;

import java.time.LocalDateTime;
import java.util.Optional;
import java.util.UUID;

import static org.assertj.core.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class ConversationServiceTest {

    @Mock
    private ConversationRepository conversationRepository;

    @Mock
    private ConversationMessageRepository messageRepository;

    @Mock
    private UserRepository userRepository;

    @Mock
    private SimpMessagingTemplate messagingTemplate;

    @Mock
    private AiCommandProcessingService aiCommandProcessingService;

    @InjectMocks
    private ConversationService conversationService;

    private User testUser;
    private ConversationCommandRequest testRequest;

    @BeforeEach
    void setUp() {
        testUser = User.builder()
                .id(UUID.randomUUID())
                .email("<EMAIL>")
                .firstName("Test")
                .lastName("User")
                .organizationName("Test Org")
                .emailVerified(true)
                .accountLocked(false)
                .build();

        testRequest = ConversationCommandRequest.builder()
                .command("What's my account balance?")
                .sessionId("test-session-123")
                .expectResponse(true)
                .saveToHistory(true)
                .build();
    }

    @Test
    @DisplayName("Should process command successfully with new conversation")
    void shouldProcessCommandWithNewConversation() {
        // Given
        when(userRepository.findById(testUser.getId())).thenReturn(Optional.of(testUser));
        when(conversationRepository.findActiveBySessionId(testRequest.getSessionId()))
                .thenReturn(Optional.empty());
        
        Conversation newConversation = Conversation.builder()
                .id(UUID.randomUUID())
                .user(testUser)
                .sessionId(testRequest.getSessionId())
                .title("What's my account balance?")
                .status(Conversation.ConversationStatus.ACTIVE)
                .lastActivityAt(LocalDateTime.now())
                .build();
        
        when(conversationRepository.save(any(Conversation.class))).thenReturn(newConversation);
        
        ConversationMessage savedMessage = ConversationMessage.builder()
                .id(UUID.randomUUID())
                .conversation(newConversation)
                .type(ConversationMessage.MessageType.COMMAND)
                .role(ConversationMessage.MessageRole.USER)
                .content(testRequest.getCommand())
                .status(ConversationMessage.MessageStatus.PENDING)
                .build();
        
        when(messageRepository.save(any(ConversationMessage.class))).thenReturn(savedMessage);

        // When
        ConversationResponse response = conversationService.processCommand(testRequest, testUser.getId());

        // Then
        assertThat(response).isNotNull();
        assertThat(response.getSessionId()).isEqualTo(testRequest.getSessionId());
        assertThat(response.getConversationId()).isEqualTo(newConversation.getId());
        assertThat(response.getType()).isEqualTo(ConversationMessage.MessageType.PROGRESS);
        assertThat(response.getContent()).contains("Processing");

        verify(conversationRepository).save(any(Conversation.class));
        verify(messageRepository).save(any(ConversationMessage.class));
        verify(messagingTemplate).convertAndSendToUser(
                eq(testUser.getId().toString()),
                eq("/queue/conversation"),
                any(ConversationResponse.class)
        );
    }

    @Test
    @DisplayName("Should use existing conversation when session ID matches")
    void shouldUseExistingConversation() {
        // Given
        when(userRepository.findById(testUser.getId())).thenReturn(Optional.of(testUser));
        
        Conversation existingConversation = Conversation.builder()
                .id(UUID.randomUUID())
                .user(testUser)
                .sessionId(testRequest.getSessionId())
                .title("Previous conversation")
                .status(Conversation.ConversationStatus.ACTIVE)
                .lastActivityAt(LocalDateTime.now().minusMinutes(5))
                .build();
        
        when(conversationRepository.findActiveBySessionId(testRequest.getSessionId()))
                .thenReturn(Optional.of(existingConversation));
        when(conversationRepository.save(any(Conversation.class))).thenReturn(existingConversation);
        
        ConversationMessage savedMessage = ConversationMessage.builder()
                .id(UUID.randomUUID())
                .conversation(existingConversation)
                .type(ConversationMessage.MessageType.COMMAND)
                .role(ConversationMessage.MessageRole.USER)
                .content(testRequest.getCommand())
                .status(ConversationMessage.MessageStatus.PENDING)
                .build();
        
        when(messageRepository.save(any(ConversationMessage.class))).thenReturn(savedMessage);

        // When
        ConversationResponse response = conversationService.processCommand(testRequest, testUser.getId());

        // Then
        assertThat(response).isNotNull();
        assertThat(response.getConversationId()).isEqualTo(existingConversation.getId());
        
        verify(conversationRepository).findActiveBySessionId(testRequest.getSessionId());
        verify(conversationRepository).save(existingConversation);
    }

    @Test
    @DisplayName("Should handle user not found error")
    void shouldHandleUserNotFoundError() {
        // Given
        when(userRepository.findById(testUser.getId())).thenReturn(Optional.empty());

        // When
        ConversationResponse response = conversationService.processCommand(testRequest, testUser.getId());

        // Then
        assertThat(response).isNotNull();
        assertThat(response.getType()).isEqualTo(ConversationMessage.MessageType.ERROR);
        assertThat(response.getContent()).contains("Failed to process command");
        
        verify(conversationRepository, never()).save(any());
        verify(messageRepository, never()).save(any());
    }

    @Test
    @DisplayName("Should retrieve user conversations")
    void shouldRetrieveUserConversations() {
        // Given
        when(userRepository.findById(testUser.getId())).thenReturn(Optional.of(testUser));
        when(conversationRepository.findActiveByUser(testUser)).thenReturn(java.util.List.of());

        // When
        var conversations = conversationService.getUserConversations(testUser.getId());

        // Then
        assertThat(conversations).isNotNull();
        verify(conversationRepository).findActiveByUser(testUser);
    }

    @Test
    @DisplayName("Should archive conversation")
    void shouldArchiveConversation() {
        // Given
        UUID conversationId = UUID.randomUUID();
        Conversation conversation = Conversation.builder()
                .id(conversationId)
                .user(testUser)
                .status(Conversation.ConversationStatus.ACTIVE)
                .build();
        
        when(conversationRepository.findById(conversationId)).thenReturn(Optional.of(conversation));
        when(conversationRepository.save(any(Conversation.class))).thenReturn(conversation);

        // When
        conversationService.archiveConversation(conversationId, testUser.getId());

        // Then
        verify(conversationRepository).save(argThat(conv -> 
            conv.getStatus() == Conversation.ConversationStatus.ARCHIVED));
    }

    @Test
    @DisplayName("Should delete conversation")
    void shouldDeleteConversation() {
        // Given
        UUID conversationId = UUID.randomUUID();
        Conversation conversation = Conversation.builder()
                .id(conversationId)
                .user(testUser)
                .status(Conversation.ConversationStatus.ACTIVE)
                .build();
        
        when(conversationRepository.findById(conversationId)).thenReturn(Optional.of(conversation));
        when(conversationRepository.save(any(Conversation.class))).thenReturn(conversation);

        // When
        conversationService.deleteConversation(conversationId, testUser.getId());

        // Then
        verify(conversationRepository).save(argThat(conv -> 
            conv.getStatus() == Conversation.ConversationStatus.DELETED));
    }
}
