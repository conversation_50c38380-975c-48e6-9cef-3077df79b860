openapi: 3.0.3
info:
  title: IntelliFin API
  description: Comprehensive API specification for IntelliFin financial platform
  version: 1.0.0
  contact:
    name: IntelliFin Development Team
    email: <EMAIL>

servers:
  - url: http://localhost:8080
    description: Development server
  - url: https://api.intellifin.com
    description: Production server

security:
  - BearerAuth: []

paths:
  # Authentication Endpoints
  /api/v1/auth/register:
    post:
      tags:
        - Authentication
      summary: Register a new user
      security: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - email
                - password
                - firstName
                - lastName
                - organizationName
              properties:
                email:
                  type: string
                  format: email
                  example: <EMAIL>
                password:
                  type: string
                  minLength: 8
                  example: SecurePass123!
                firstName:
                  type: string
                  minLength: 1
                  maxLength: 100
                  example: Grace
                lastName:
                  type: string
                  minLength: 1
                  maxLength: 100
                  example: Phiri
                organizationName:
                  type: string
                  minLength: 1
                  maxLength: 255
                  example: Phiri Catering Services
                tpin:
                  type: string
                  maxLength: 20
                  example: **********
      responses:
        '201':
          description: User registered successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  user:
                    $ref: '#/components/schemas/User'
                  token:
                    type: string
                    example: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
        '400':
          description: Invalid input data
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '409':
          description: User already exists
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /api/v1/auth/login:
    post:
      tags:
        - Authentication
      summary: Authenticate user
      security: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - email
                - password
              properties:
                email:
                  type: string
                  format: email
                  example: <EMAIL>
                password:
                  type: string
                  example: SecurePass123!
      responses:
        '200':
          description: Login successful
          content:
            application/json:
              schema:
                type: object
                properties:
                  user:
                    $ref: '#/components/schemas/User'
                  token:
                    type: string
                    example: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
        '401':
          description: Invalid credentials
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  # User Management
  /api/v1/users/me:
    get:
      tags:
        - Users
      summary: Get current user profile
      responses:
        '200':
          description: User profile retrieved
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/User'
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  # Transaction Management
  /api/v1/transactions:
    get:
      tags:
        - Transactions
      summary: Get user transactions
      parameters:
        - name: status
          in: query
          schema:
            type: string
            enum: [PENDING_CLASSIFICATION, CLASSIFIED, RECONCILED]
          description: Filter by transaction status
        - name: type
          in: query
          schema:
            type: string
            enum: [INCOME, EXPENSE]
          description: Filter by transaction type
        - name: categoryId
          in: query
          schema:
            type: string
            format: uuid
          description: Filter by category
        - name: startDate
          in: query
          schema:
            type: string
            format: date
          description: Start date for filtering (YYYY-MM-DD)
        - name: endDate
          in: query
          schema:
            type: string
            format: date
          description: End date for filtering (YYYY-MM-DD)
        - name: limit
          in: query
          schema:
            type: integer
            minimum: 1
            maximum: 100
            default: 20
          description: Number of transactions to return
        - name: offset
          in: query
          schema:
            type: integer
            minimum: 0
            default: 0
          description: Number of transactions to skip
      responses:
        '200':
          description: Transactions retrieved successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  transactions:
                    type: array
                    items:
                      $ref: '#/components/schemas/Transaction'
                  total:
                    type: integer
                    example: 150
                  uncategorizedCount:
                    type: integer
                    example: 5
                  pagination:
                    $ref: '#/components/schemas/Pagination'
        '400':
          description: Invalid query parameters
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

    post:
      tags:
        - Transactions
      summary: Create a new transaction manually
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - description
                - amount
                - type
                - date
                - financialAccountId
              properties:
                description:
                  type: string
                  minLength: 1
                  maxLength: 255
                  example: Payment to ZESCO for electricity
                amount:
                  type: number
                  minimum: 0.01
                  multipleOf: 0.01
                  example: 450.00
                type:
                  type: string
                  enum: [INCOME, EXPENSE]
                  example: EXPENSE
                date:
                  type: string
                  format: date
                  example: "2024-07-30"
                financialAccountId:
                  type: string
                  format: uuid
                  example: 123e4567-e89b-12d3-a456-************
                categoryId:
                  type: string
                  format: uuid
                  example: 123e4567-e89b-12d3-a456-************
      responses:
        '201':
          description: Transaction created successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  transaction:
                    $ref: '#/components/schemas/Transaction'
                  journalEntries:
                    type: array
                    items:
                      $ref: '#/components/schemas/JournalEntry'
                  suggestedCategory:
                    $ref: '#/components/schemas/CategorySuggestion'
        '400':
          description: Invalid transaction data
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /api/v1/transactions/{id}/category:
    put:
      tags:
        - Transactions
      summary: Update transaction category
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: string
            format: uuid
          description: Transaction ID
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - categoryId
              properties:
                categoryId:
                  type: string
                  format: uuid
                  example: 123e4567-e89b-12d3-a456-************
                accepted:
                  type: boolean
                  default: true
                  description: Whether user accepted AI suggestion
                justification:
                  type: string
                  maxLength: 500
                  description: User's reason for override
      responses:
        '200':
          description: Category updated successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  transaction:
                    $ref: '#/components/schemas/Transaction'
        '404':
          description: Transaction not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  # AI Service Endpoints
  /api/v1/ai/categorize:
    post:
      tags:
        - AI Services
      summary: Get AI categorization suggestion
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - description
                - amount
              properties:
                description:
                  type: string
                  example: Payment to ZESCO for electricity
                amount:
                  type: number
                  example: 450.00
                type:
                  type: string
                  enum: [INCOME, EXPENSE]
                  example: EXPENSE
                context:
                  type: object
                  description: Additional context for categorization
      responses:
        '200':
          description: Categorization suggestion generated
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CategorySuggestion'
        '503':
          description: AI service unavailable
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

components:
  securitySchemes:
    BearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT

  schemas:
    User:
      type: object
      properties:
        id:
          type: string
          format: uuid
          example: 123e4567-e89b-12d3-a456-************
        email:
          type: string
          format: email
          example: <EMAIL>
        firstName:
          type: string
          example: Grace
        lastName:
          type: string
          example: Phiri
        organizationName:
          type: string
          example: Phiri Catering Services
        tpin:
          type: string
          example: "**********"
        onboardingStatus:
          type: string
          enum: [STARTED, PROFILE_COMPLETE, ACCOUNTS_CONNECTED, ALL_SET]
          example: PROFILE_COMPLETE
        preferences:
          type: object
          example: {"currency": "ZMW", "notifications": true}
        createdAt:
          type: string
          format: date-time
          example: "2024-07-30T10:00:00Z"
        updatedAt:
          type: string
          format: date-time
          example: "2024-07-30T10:00:00Z"

    Transaction:
      type: object
      properties:
        id:
          type: string
          format: uuid
          example: 123e4567-e89b-12d3-a456-************
        userId:
          type: string
          format: uuid
          example: 123e4567-e89b-12d3-a456-************
        financialAccountId:
          type: string
          format: uuid
          example: 123e4567-e89b-12d3-a456-************
        date:
          type: string
          format: date
          example: "2024-07-30"
        description:
          type: string
          example: Payment to ZESCO for electricity
        amount:
          type: number
          example: 450.00
        type:
          type: string
          enum: [INCOME, EXPENSE]
          example: EXPENSE
        categoryId:
          type: string
          format: uuid
          example: 123e4567-e89b-12d3-a456-************
        status:
          type: string
          enum: [PENDING_CLASSIFICATION, CLASSIFIED, RECONCILED]
          example: CLASSIFIED
        source:
          type: string
          example: MTN Mobile Money
        aiConfidence:
          type: number
          minimum: 0
          maximum: 1
          example: 0.95
        aiExplanation:
          type: string
          example: ZESCO is the national electricity provider, categorized as Utilities expense
        createdAt:
          type: string
          format: date-time
          example: "2024-07-30T10:00:00Z"
        updatedAt:
          type: string
          format: date-time
          example: "2024-07-30T10:00:00Z"

    CategorySuggestion:
      type: object
      properties:
        suggestedCategory:
          $ref: '#/components/schemas/Category'
        confidence:
          type: number
          minimum: 0
          maximum: 1
          example: 0.95
        explanation:
          type: string
          example: ZESCO is the national electricity provider, typically categorized as Utilities expense

    Category:
      type: object
      properties:
        id:
          type: string
          format: uuid
          example: 123e4567-e89b-12d3-a456-************
        userId:
          type: string
          format: uuid
          nullable: true
          example: 123e4567-e89b-12d3-a456-************
        name:
          type: string
          example: Utilities
        type:
          type: string
          enum: [INCOME, EXPENSE]
          example: EXPENSE
        isSystemDefined:
          type: boolean
          example: true
        createdAt:
          type: string
          format: date-time
          example: "2024-07-30T10:00:00Z"
        updatedAt:
          type: string
          format: date-time
          example: "2024-07-30T10:00:00Z"

    JournalEntry:
      type: object
      properties:
        id:
          type: string
          format: uuid
          example: 123e4567-e89b-12d3-a456-************
        userId:
          type: string
          format: uuid
          example: 123e4567-e89b-12d3-a456-************
        transactionId:
          type: string
          format: uuid
          nullable: true
          example: 123e4567-e89b-12d3-a456-************
        date:
          type: string
          format: date
          example: "2024-07-30"
        description:
          type: string
          example: Payment to ZESCO for electricity
        debitAccountId:
          type: string
          format: uuid
          example: 123e4567-e89b-12d3-a456-************
        creditAccountId:
          type: string
          format: uuid
          example: 123e4567-e89b-12d3-a456-************
        amount:
          type: number
          example: 450.00
        entryType:
          type: string
          example: EXPENSE

    Pagination:
      type: object
      properties:
        limit:
          type: integer
          example: 20
        offset:
          type: integer
          example: 0
        total:
          type: integer
          example: 150
        hasNext:
          type: boolean
          example: true
        hasPrevious:
          type: boolean
          example: false

    ErrorResponse:
      type: object
      properties:
        error:
          type: string
          example: VALIDATION_ERROR
        message:
          type: string
          example: Invalid input data provided
        details:
          type: array
          items:
            type: object
            properties:
              field:
                type: string
                example: email
              message:
                type: string
                example: Email format is invalid
        timestamp:
          type: string
          format: date-time
          example: "2024-07-30T10:00:00Z"
        path:
          type: string
          example: /api/v1/auth/register
