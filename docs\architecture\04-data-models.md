# IntelliFin Architecture - Data Models

## 4. Data Models

This section defines the core data models/entities that will be shared between frontend and backend.

### 4.1 Transaction

**Purpose:** Represents a single financial transaction, either incoming (income) or outgoing (expense). This is the fundamental unit for financial tracking.

**Key Attributes:**
- `id`: UUID - Unique identifier for the transaction.
- `userId`: UUID - Foreign key to the User who owns this transaction.
- `financialAccountId`: UUID - Foreign key to the FinancialAccount (e.g., bank, mobile money wallet) the transaction belongs to.
- `date`: Date - The date the transaction occurred.
- `description`: String - A brief description of the transaction (e.g., "Shoprite purchase", "Client Payment").
- `amount`: Decimal - The amount of the transaction.
- `type`: Enum (INCOME, EXPENSE) - Whether the transaction is income or an expense.
- `categoryId`: UUID - Foreign key to the Category this transaction is classified under.
- `status`: Enum (PENDING_CLASSIFICATION, CLASSIFIED, RECONCILED) - Current status of the transaction.
- `source`: String - Where the transaction data originated from (e.g., "MTN Mobile Money", "Bank Statement", "Manual Entry").
- `createdAt`: DateTime - Timestamp of when the record was created.
- `updatedAt`: DateTime - Timestamp of when the record was last updated.

### 4.1.1 TypeScript Interface

```typescript
interface Transaction {
  id: string;
  userId: string;
  financialAccountId: string;
  date: string; // ISO 8601 date string
  description: string;
  amount: number; // Use number for decimal, handle precision carefully
  type: 'INCOME' | 'EXPENSE';
  categoryId: string;
  status: 'PENDING_CLASSIFICATION' | 'CLASSIFIED' | 'RECONCILED';
  source: string;
  createdAt: string; // ISO 8601 datetime string
  updatedAt: string; // ISO 8601 datetime string
}
```

### 4.1.2 Relationships

- One-to-Many: User has many Transactions.
- One-to-Many: FinancialAccount has many Transactions.
- Many-to-One: Transaction belongs to one Category.

### 4.2 Category

**Purpose:** Defines categories for classifying transactions (e.g., "Groceries", "Utilities", "Sales Revenue"). This helps users organize and analyze their financial data.

**Key Attributes:**
- `id`: UUID - Unique identifier for the category.
- `userId`: UUID - Foreign key to the User who owns this category (can be global or user-defined).
- `name`: String - The name of the category (e.g., "Transport", "Rent").
- `type`: Enum (INCOME, EXPENSE) - Whether this category is for income or expense transactions.
- `isSystemDefined`: Boolean - True if it's a default system category, false if user-created.
- `createdAt`: DateTime - Timestamp of when the record was created.
- `updatedAt`: DateTime - Timestamp of when the record was last updated.

### 4.2.1 TypeScript Interface

```typescript
interface Category {
  id: string;
  userId?: string; // Optional for system-defined categories
  name: string;
  type: 'INCOME' | 'EXPENSE';
  isSystemDefined: boolean;
  createdAt: string;
  updatedAt: string;
}
```

### 4.2.2 Relationships

- Many-to-One: Many Transactions can belong to one Category.
- One-to-Many: User has many user-defined Categories.

### 4.3 User

**Purpose:** Represents an individual user account within IntelliFin.

**Key Attributes:**
- `id`: UUID - Unique identifier for the user.
- `email`: String - User's email address (unique, used for login).
- `passwordHash`: String - Hashed password for security.
- `firstName`: String - User's first name.
- `lastName`: String - User's last name.
- `organizationName`: String - The name of the user's business/organization.
- `tpin`: String - User's Taxpayer Identification Number (ZRA specific).
- `onboardingStatus`: Enum (STARTED, PROFILE_COMPLETE, ACCOUNTS_CONNECTED, ALL_SET) - Tracks user onboarding progress.
- `preferences`: JSONB - User-specific settings (e.g., currency, notification preferences).
- `createdAt`: DateTime - Timestamp of when the record was created.
- `updatedAt`: DateTime - Timestamp of when the record was last updated.

### 4.3.1 TypeScript Interface

```typescript
interface User {
  id: string;
  email: string;
  passwordHash: string;
  firstName: string;
  lastName: string;
  organizationName: string;
  tpin?: string; // Optional if not yet provided
  onboardingStatus: 'STARTED' | 'PROFILE_COMPLETE' | 'ACCOUNTS_CONNECTED' | 'ALL_SET';
  preferences: Record<string, any>; // JSON object for flexible preferences
  createdAt: string;
  updatedAt: string;
}
```

### 4.3.2 Relationships

- One-to-Many: User has many FinancialAccounts.
- One-to-Many: User has many Transactions.
- One-to-Many: User has many Clients.
- One-to-Many: User has many Invoices.
- One-to-Many: User has many Categories.

### 4.4 FinancialAccount

**Purpose:** Represents a financial account connected to IntelliFin (e.g., bank account, mobile money wallet).

**Key Attributes:**
- `id`: UUID - Unique identifier for the financial account.
- `userId`: UUID - Foreign key to the User who owns this account.
- `provider`: String - The financial institution or service (e.g., "Standard Chartered", "MTN Mobile Money").
- `accountName`: String - User-defined name for the account (e.g., "My Business Savings", "MTN Wallet").
- `accountNumberLast4`: String - Last 4 digits of account number (for identification, not sensitive).
- `balance`: Decimal - Current balance of the account (can be synced).
- `currency`: String (ISO 4217) - Currency code (e.g., "ZMW", "USD").
- `connectionStatus`: Enum (CONNECTED, DISCONNECTED, ERROR) - Status of the connection to the external provider.
- `connectionDetails`: JSONB - Encrypted details needed for API integration (e.g., API keys, access tokens).
- `createdAt`: DateTime - Timestamp of when the record was created.
- `updatedAt`: DateTime - Timestamp of when the record was last updated.

### 4.4.1 TypeScript Interface

```typescript
interface FinancialAccount {
  id: string;
  userId: string;
  provider: string;
  accountName: string;
  accountNumberLast4: string;
  balance: number;
  currency: string;
  connectionStatus: 'CONNECTED' | 'DISCONNECTED' | 'ERROR';
  // connectionDetails should be handled securely on the backend only
  createdAt: string;
  updatedAt: string;
}
```

### 4.4.2 Relationships

- Many-to-One: Many FinancialAccounts belong to one User.
- One-to-Many: FinancialAccount has many Transactions.

### 4.5 Client

**Purpose:** Represents a client or customer of the IntelliFin user's business. Essential for invoicing and tracking.

**Key Attributes:**
- `id`: UUID - Unique identifier for the client.
- `userId`: UUID - Foreign key to the User who owns this client.
- `name`: String - Client's name or business name.
- `email`: String - Client's email address.
- `phone`: String - Client's phone number.
- `address`: String - Client's address (optional).
- `tpin`: String - Client's Taxpayer Identification Number (for ZRA compliance).
- `isActive`: Boolean - Whether the client is currently active.
- `createdAt`: DateTime - Timestamp of when the record was created.
- `updatedAt`: DateTime - Timestamp of when the record was last updated.

### 4.5.1 TypeScript Interface

```typescript
interface Client {
  id: string;
  userId: string;
  name: string;
  email: string;
  phone: string;
  address?: string;
  tpin?: string;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}
```

### 4.5.2 Relationships

- Many-to-One: Many Clients belong to one User.
- One-to-Many: Client has many Invoices.

### 4.6 Invoice

**Purpose:** Represents a ZRA-compliant Smart Invoice generated by the user.

**Key Attributes:**
- `id`: UUID - Unique identifier for the invoice.
- `userId`: UUID - Foreign key to the User who created this invoice.
- `clientId`: UUID - Foreign key to the Client this invoice is for.
- `invoiceNumber`: String - Unique invoice number (can be auto-generated).
- `issueDate`: Date - Date the invoice was issued.
- `dueDate`: Date - Date the invoice is due for payment.
- `subtotal`: Decimal - Total amount before tax.
- `taxAmount`: Decimal - Tax amount (VAT).
- `totalAmount`: Decimal - Total amount including tax.
- `status`: Enum (DRAFT, SENT, PAID, OVERDUE, CANCELLED) - Current status of the invoice.
- `zraReference`: String - ZRA reference number (after submission to ZRA).
- `notes`: String - Additional notes or terms.
- `createdAt`: DateTime - Timestamp of when the record was created.
- `updatedAt`: DateTime - Timestamp of when the record was last updated.

### 4.6.1 TypeScript Interface

```typescript
interface Invoice {
  id: string;
  userId: string;
  clientId: string;
  invoiceNumber: string;
  issueDate: string; // ISO 8601 date string
  dueDate: string; // ISO 8601 date string
  subtotal: number;
  taxAmount: number;
  totalAmount: number;
  status: 'DRAFT' | 'SENT' | 'PAID' | 'OVERDUE' | 'CANCELLED';
  zraReference?: string;
  notes?: string;
  createdAt: string;
  updatedAt: string;
}
```

### 4.6.2 Relationships

- Many-to-One: Many Invoices belong to one User.
- Many-to-One: Many Invoices belong to one Client.
- One-to-Many: Invoice has many InvoiceLineItems.

### 4.7 InvoiceLineItem

**Purpose:** Represents individual line items within an invoice.

**Key Attributes:**
- `id`: UUID - Unique identifier for the line item.
- `invoiceId`: UUID - Foreign key to the Invoice this line item belongs to.
- `description`: String - Description of the service or product.
- `quantity`: Decimal - Quantity of the item.
- `unitPrice`: Decimal - Price per unit.
- `totalPrice`: Decimal - Total price for this line item (quantity * unitPrice).
- `createdAt`: DateTime - Timestamp of when the record was created.
- `updatedAt`: DateTime - Timestamp of when the record was last updated.

### 4.7.1 TypeScript Interface

```typescript
interface InvoiceLineItem {
  id: string;
  invoiceId: string;
  description: string;
  quantity: number;
  unitPrice: number;
  totalPrice: number;
  createdAt: string;
  updatedAt: string;
}
```

### 4.7.2 Relationships

- Many-to-One: Many InvoiceLineItems belong to one Invoice.

### 4.8 JournalEntry

**Purpose:** Represents a double-entry bookkeeping journal entry for financial transactions.

**Key Attributes:**
- `id`: UUID - Unique identifier for the journal entry.
- `userId`: UUID - Foreign key to the User who owns this journal entry.
- `date`: Date - Date of the journal entry.
- `reference`: String - Reference number or description.
- `description`: String - Description of the journal entry.
- `totalDebit`: Decimal - Total debit amount.
- `totalCredit`: Decimal - Total credit amount.
- `status`: Enum (DRAFT, POSTED, VOID) - Status of the journal entry.
- `createdAt`: DateTime - Timestamp of when the record was created.
- `updatedAt`: DateTime - Timestamp of when the record was last updated.

### 4.8.1 TypeScript Interface

```typescript
interface JournalEntry {
  id: string;
  userId: string;
  date: string; // ISO 8601 date string
  reference: string;
  description: string;
  totalDebit: number;
  totalCredit: number;
  status: 'DRAFT' | 'POSTED' | 'VOID';
  createdAt: string;
  updatedAt: string;
}
```

### 4.8.2 Relationships

- Many-to-One: Many JournalEntries belong to one User.
- One-to-Many: JournalEntry has many Account entries (debits and credits).

### 4.9 Account

**Purpose:** Represents a chart of accounts for double-entry bookkeeping.

**Key Attributes:**
- `id`: UUID - Unique identifier for the account.
- `userId`: UUID - Foreign key to the User who owns this account.
- `accountNumber`: String - Account number in the chart of accounts.
- `name`: String - Name of the account.
- `type`: Enum (ASSET, LIABILITY, EQUITY, REVENUE, EXPENSE) - Type of account.
- `parentAccountId`: UUID - Foreign key to parent account (for hierarchical structure).
- `isActive`: Boolean - Whether the account is currently active.
- `balance`: Decimal - Current balance of the account.
- `createdAt`: DateTime - Timestamp of when the record was created.
- `updatedAt`: DateTime - Timestamp of when the record was last updated.

### 4.9.1 TypeScript Interface

```typescript
interface Account {
  id: string;
  userId: string;
  accountNumber: string;
  name: string;
  type: 'ASSET' | 'LIABILITY' | 'EQUITY' | 'REVENUE' | 'EXPENSE';
  parentAccountId?: string;
  isActive: boolean;
  balance: number;
  createdAt: string;
  updatedAt: string;
}
```

### 4.9.2 Relationships

- Many-to-One: Many Accounts belong to one User.
- Self-referencing: Account can have many child Accounts (hierarchical structure).

---

**Previous Section:** [Tech Stack](03-tech-stack.md)  
**Next Section:** [API Specification](05-api-specification.md) 