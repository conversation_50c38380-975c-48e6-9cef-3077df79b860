name: ConversationRepository QA Tests

on:
  push:
    branches: [ main, develop ]
    paths:
      - 'apps/backend-java-core/src/main/java/com/intellifin/repository/ConversationRepository.java'
      - 'apps/backend-java-core/src/test/java/com/intellifin/repository/ConversationRepositoryTest.java'
      - 'apps/backend-java-core/src/main/java/com/intellifin/model/Conversation.java'
      - 'apps/backend-java-core/src/main/java/com/intellifin/model/User.java'
      - 'apps/backend-java-core/pom.xml'
  pull_request:
    branches: [ main, develop ]
    paths:
      - 'apps/backend-java-core/src/main/java/com/intellifin/repository/ConversationRepository.java'
      - 'apps/backend-java-core/src/test/java/com/intellifin/repository/ConversationRepositoryTest.java'
      - 'apps/backend-java-core/src/main/java/com/intellifin/model/Conversation.java'
      - 'apps/backend-java-core/src/main/java/com/intellifin/model/User.java'
      - 'apps/backend-java-core/pom.xml'
  workflow_dispatch:
    inputs:
      test_method:
        description: 'Specific test method to run (optional)'
        required: false
        type: string

jobs:
  conversation-repository-tests:
    name: ConversationRepository QA Test Suite
    runs-on: ubuntu-latest
    
    defaults:
      run:
        working-directory: ./apps/backend-java-core
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
      
      - name: Setup JDK 17
        uses: actions/setup-java@v4
        with:
          java-version: '17'
          distribution: 'temurin'
      
      - name: Cache Maven dependencies
        uses: actions/cache@v3
        with:
          path: ~/.m2
          key: ${{ runner.os }}-m2-conversation-tests-${{ hashFiles('**/pom.xml') }}
          restore-keys: |
            ${{ runner.os }}-m2-conversation-tests-
            ${{ runner.os }}-m2-
      
      - name: Verify Maven Wrapper
        run: |
          if [ ! -f "./mvnw" ]; then
            echo "Maven wrapper not found, downloading..."
            curl -o mvnw https://raw.githubusercontent.com/apache/maven/master/maven-wrapper/src/main/resources/mvnw
            chmod +x mvnw
          fi
          if [ ! -f "./mvnw.cmd" ]; then
            echo "Maven wrapper CMD not found, downloading..."
            curl -o mvnw.cmd https://raw.githubusercontent.com/apache/maven/master/maven-wrapper/src/main/resources/mvnw.cmd
          fi
      
      - name: Make Maven wrapper executable
        run: chmod +x ./mvnw
      
      - name: Validate test file exists
        run: |
          if [ ! -f "src/test/java/com/intellifin/repository/ConversationRepositoryTest.java" ]; then
            echo "❌ ConversationRepositoryTest.java not found!"
            exit 1
          fi
          echo "✅ ConversationRepositoryTest.java found"
      
      - name: Validate test configuration exists
        run: |
          if [ ! -f "src/test/resources/application-test.yml" ]; then
            echo "❌ application-test.yml not found!"
            exit 1
          fi
          echo "✅ application-test.yml found"
      
      - name: Compile test classes
        run: ./mvnw test-compile
        env:
          MAVEN_OPTS: "-Xmx1024m"
      
      - name: Run ConversationRepository tests (all)
        if: ${{ github.event.inputs.test_method == '' }}
        run: ./mvnw test -Dtest=ConversationRepositoryTest
        env:
          MAVEN_OPTS: "-Xmx1024m"
          SPRING_PROFILES_ACTIVE: test
      
      - name: Run ConversationRepository tests (specific method)
        if: ${{ github.event.inputs.test_method != '' }}
        run: ./mvnw test -Dtest=ConversationRepositoryTest#${{ github.event.inputs.test_method }}
        env:
          MAVEN_OPTS: "-Xmx1024m"
          SPRING_PROFILES_ACTIVE: test
      
      - name: Generate test report
        if: always()
        run: |
          echo "## ConversationRepository Test Results" >> $GITHUB_STEP_SUMMARY
          echo "" >> $GITHUB_STEP_SUMMARY
          
          if [ -f "target/surefire-reports/TEST-com.intellifin.repository.ConversationRepositoryTest.xml" ]; then
            # Extract test results from XML
            TESTS=$(grep -o 'tests="[^"]*"' target/surefire-reports/TEST-com.intellifin.repository.ConversationRepositoryTest.xml | cut -d'"' -f2)
            FAILURES=$(grep -o 'failures="[^"]*"' target/surefire-reports/TEST-com.intellifin.repository.ConversationRepositoryTest.xml | cut -d'"' -f2)
            ERRORS=$(grep -o 'errors="[^"]*"' target/surefire-reports/TEST-com.intellifin.repository.ConversationRepositoryTest.xml | cut -d'"' -f2)
            SKIPPED=$(grep -o 'skipped="[^"]*"' target/surefire-reports/TEST-com.intellifin.repository.ConversationRepositoryTest.xml | cut -d'"' -f2)
            TIME=$(grep -o 'time="[^"]*"' target/surefire-reports/TEST-com.intellifin.repository.ConversationRepositoryTest.xml | cut -d'"' -f2)
            
            echo "| Metric | Value |" >> $GITHUB_STEP_SUMMARY
            echo "|--------|-------|" >> $GITHUB_STEP_SUMMARY
            echo "| Total Tests | $TESTS |" >> $GITHUB_STEP_SUMMARY
            echo "| Failures | $FAILURES |" >> $GITHUB_STEP_SUMMARY
            echo "| Errors | $ERRORS |" >> $GITHUB_STEP_SUMMARY
            echo "| Skipped | $SKIPPED |" >> $GITHUB_STEP_SUMMARY
            echo "| Execution Time | ${TIME}s |" >> $GITHUB_STEP_SUMMARY
            echo "" >> $GITHUB_STEP_SUMMARY
            
            if [ "$FAILURES" = "0" ] && [ "$ERRORS" = "0" ]; then
              echo "✅ **All tests passed successfully!**" >> $GITHUB_STEP_SUMMARY
            else
              echo "❌ **Some tests failed. Check the logs above for details.**" >> $GITHUB_STEP_SUMMARY
            fi
          else
            echo "⚠️ Test report not found. Tests may not have run properly." >> $GITHUB_STEP_SUMMARY
          fi
          
          echo "" >> $GITHUB_STEP_SUMMARY
          echo "### Test Coverage Areas" >> $GITHUB_STEP_SUMMARY
          echo "- ✅ Basic CRUD Operations" >> $GITHUB_STEP_SUMMARY
          echo "- ✅ Session ID Queries" >> $GITHUB_STEP_SUMMARY
          echo "- ✅ User-based Queries" >> $GITHUB_STEP_SUMMARY
          echo "- ✅ Time-based Queries" >> $GITHUB_STEP_SUMMARY
          echo "- ✅ Counting Operations" >> $GITHUB_STEP_SUMMARY
          echo "- ✅ Modifying Operations" >> $GITHUB_STEP_SUMMARY
          echo "- ✅ Search Operations" >> $GITHUB_STEP_SUMMARY
          echo "- ✅ Statistics Operations" >> $GITHUB_STEP_SUMMARY
          echo "- ✅ Edge Cases and Error Scenarios" >> $GITHUB_STEP_SUMMARY
      
      - name: Upload test results
        if: always()
        uses: actions/upload-artifact@v3
        with:
          name: conversation-repository-test-results
          path: |
            apps/backend-java-core/target/surefire-reports/
            apps/backend-java-core/target/site/jacoco/
          retention-days: 30
      
      - name: Comment on PR with test results
        if: github.event_name == 'pull_request' && always()
        uses: actions/github-script@v6
        with:
          script: |
            const fs = require('fs');
            const path = './apps/backend-java-core/target/surefire-reports/TEST-com.intellifin.repository.ConversationRepositoryTest.xml';
            
            let comment = '## 🧪 ConversationRepository Test Results\n\n';
            
            if (fs.existsSync(path)) {
              const content = fs.readFileSync(path, 'utf8');
              const tests = content.match(/tests="(\d+)"/)?.[1] || '0';
              const failures = content.match(/failures="(\d+)"/)?.[1] || '0';
              const errors = content.match(/errors="(\d+)"/)?.[1] || '0';
              const time = content.match(/time="([^"]+)"/)?.[1] || '0';
              
              comment += `| Metric | Value |\n`;
              comment += `|--------|-------|\n`;
              comment += `| Total Tests | ${tests} |\n`;
              comment += `| Failures | ${failures} |\n`;
              comment += `| Errors | ${errors} |\n`;
              comment += `| Execution Time | ${time}s |\n\n`;
              
              if (failures === '0' && errors === '0') {
                comment += '✅ **All ConversationRepository tests passed!**\n\n';
              } else {
                comment += '❌ **Some ConversationRepository tests failed.**\n\n';
              }
            } else {
              comment += '⚠️ Test results not found.\n\n';
            }
            
            comment += '### Test Coverage\n';
            comment += 'This test suite covers all 26 repository methods including:\n';
            comment += '- Query methods (read operations)\n';
            comment += '- Count methods\n';
            comment += '- Statistical methods\n';
            comment += '- Modifying methods (@Modifying operations)\n';
            comment += '- Inherited JpaRepository methods\n';
            comment += '- Edge cases and error scenarios\n\n';
            comment += '📚 For detailed information, see: `docs/testing/conversation-repository-qa-summary.md`';
            
            github.rest.issues.createComment({
              issue_number: context.issue.number,
              owner: context.repo.owner,
              repo: context.repo.repo,
              body: comment
            });

  validate-test-quality:
    name: Validate Test Quality
    runs-on: ubuntu-latest
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
      
      - name: Validate test file structure
        run: |
          TEST_FILE="apps/backend-java-core/src/test/java/com/intellifin/repository/ConversationRepositoryTest.java"
          
          if [ ! -f "$TEST_FILE" ]; then
            echo "❌ Test file not found: $TEST_FILE"
            exit 1
          fi
          
          echo "🔍 Validating test file structure..."
          
          # Check for required annotations
          if ! grep -q "@DataJpaTest" "$TEST_FILE"; then
            echo "❌ Missing @DataJpaTest annotation"
            exit 1
          fi
          
          if ! grep -q "@ActiveProfiles" "$TEST_FILE"; then
            echo "❌ Missing @ActiveProfiles annotation"
            exit 1
          fi
          
          # Check for test organization
          NESTED_COUNT=$(grep -c "@Nested" "$TEST_FILE")
          if [ "$NESTED_COUNT" -lt 8 ]; then
            echo "❌ Expected at least 8 nested test classes, found $NESTED_COUNT"
            exit 1
          fi
          
          # Check for test methods
          TEST_COUNT=$(grep -c "@Test" "$TEST_FILE")
          if [ "$TEST_COUNT" -lt 30 ]; then
            echo "❌ Expected at least 30 test methods, found $TEST_COUNT"
            exit 1
          fi
          
          echo "✅ Test file structure validation passed"
          echo "   - Nested test classes: $NESTED_COUNT"
          echo "   - Test methods: $TEST_COUNT"
