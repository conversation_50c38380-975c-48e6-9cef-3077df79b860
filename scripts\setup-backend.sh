#!/bin/bash

# IntelliFin Backend Setup Script
# This script helps configure and start the backend services

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to check if command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Function to check if port is available
port_available() {
    ! nc -z localhost $1 2>/dev/null
}

# Function to wait for service to be ready
wait_for_service() {
    local service_name=$1
    local port=$2
    local max_attempts=30
    local attempt=1

    print_status "Waiting for $service_name to be ready on port $port..."
    
    while [ $attempt -le $max_attempts ]; do
        if nc -z localhost $port 2>/dev/null; then
            print_success "$service_name is ready!"
            return 0
        fi
        
        echo -n "."
        sleep 2
        attempt=$((attempt + 1))
    done
    
    print_error "$service_name failed to start within expected time"
    return 1
}

# Function to generate JWT secret
generate_jwt_secret() {
    openssl rand -base64 32 2>/dev/null || python3 -c "import secrets; print(secrets.token_urlsafe(32))" 2>/dev/null || echo "change-this-jwt-secret-in-production"
}

# Main setup function
main() {
    echo "=================================================="
    echo "       IntelliFin Backend Setup Script"
    echo "=================================================="
    echo

    # Check prerequisites
    print_status "Checking prerequisites..."
    
    if ! command_exists docker; then
        print_error "Docker is not installed. Please install Docker first."
        exit 1
    fi
    
    if ! command_exists docker-compose; then
        print_error "Docker Compose is not installed. Please install Docker Compose first."
        exit 1
    fi
    
    print_success "Prerequisites check passed"

    # Check if .env file exists
    if [ ! -f .env ]; then
        print_warning ".env file not found. Creating from .env.example..."
        
        if [ -f .env.example ]; then
            cp .env.example .env
            
            # Generate JWT secret
            JWT_SECRET=$(generate_jwt_secret)
            sed -i.bak "s/your-local-jwt-secret-key-min-256-bits-change-this/$JWT_SECRET/" .env
            rm .env.bak 2>/dev/null || true
            
            print_success ".env file created with generated JWT secret"
            print_warning "Please review and update .env file with your specific configuration"
        else
            print_error ".env.example file not found. Please create .env file manually."
            exit 1
        fi
    else
        print_success ".env file found"
    fi

    # Check if ports are available
    print_status "Checking port availability..."
    
    PORTS=(5432 6379 8080)
    PORT_NAMES=("PostgreSQL" "Redis" "Backend")
    
    for i in "${!PORTS[@]}"; do
        port=${PORTS[$i]}
        name=${PORT_NAMES[$i]}
        
        if ! port_available $port; then
            print_warning "Port $port ($name) is already in use"
            read -p "Do you want to continue anyway? (y/N): " -n 1 -r
            echo
            if [[ ! $REPLY =~ ^[Yy]$ ]]; then
                print_error "Setup cancelled"
                exit 1
            fi
        fi
    done

    # Start infrastructure services
    print_status "Starting infrastructure services (PostgreSQL, Redis)..."
    docker-compose up -d postgres redis

    # Wait for services to be ready
    wait_for_service "PostgreSQL" 5432
    wait_for_service "Redis" 6379

    # Run database migrations
    print_status "Running database migrations..."
    cd apps/backend-java-core
    
    if command_exists ./mvnw; then
        ./mvnw flyway:migrate -Dflyway.configFiles=src/main/resources/flyway.conf
    elif command_exists mvn; then
        mvn flyway:migrate -Dflyway.configFiles=src/main/resources/flyway.conf
    else
        print_warning "Maven not found. Please run database migrations manually."
    fi
    
    cd ../..

    # Start the backend application
    print_status "Starting backend application..."
    
    # Option 1: Docker
    read -p "Start backend with Docker (d) or locally with Maven (m)? [d/m]: " -n 1 -r
    echo
    
    if [[ $REPLY =~ ^[Mm]$ ]]; then
        # Start locally with Maven
        print_status "Starting backend locally with Maven..."
        cd apps/backend-java-core
        
        if command_exists ./mvnw; then
            ./mvnw spring-boot:run &
        elif command_exists mvn; then
            mvn spring-boot:run &
        else
            print_error "Maven not found. Please install Maven or use Docker option."
            exit 1
        fi
        
        BACKEND_PID=$!
        cd ../..
        
        # Wait for backend to be ready
        wait_for_service "Backend" 8080
        
        print_success "Backend started locally (PID: $BACKEND_PID)"
    else
        # Start with Docker
        print_status "Starting backend with Docker..."
        docker-compose up -d backend-core
        
        # Wait for backend to be ready
        wait_for_service "Backend" 8080
        
        print_success "Backend started with Docker"
    fi

    # Test the backend
    print_status "Testing backend endpoints..."
    
    # Test health endpoint
    if curl -s http://localhost:8080/actuator/health > /dev/null; then
        print_success "Health endpoint is responding"
    else
        print_error "Health endpoint is not responding"
    fi
    
    # Test API documentation
    if curl -s http://localhost:8080/swagger-ui.html > /dev/null; then
        print_success "API documentation is available"
    else
        print_warning "API documentation may not be available"
    fi

    # Display summary
    echo
    echo "=================================================="
    echo "           Setup Complete!"
    echo "=================================================="
    echo
    print_success "Backend services are running:"
    echo "  • PostgreSQL: http://localhost:5432"
    echo "  • Redis: http://localhost:6379"
    echo "  • Backend API: http://localhost:8080"
    echo "  • API Documentation: http://localhost:8080/swagger-ui.html"
    echo "  • Health Check: http://localhost:8080/actuator/health"
    echo
    print_status "Next steps:"
    echo "  1. Start the frontend: cd apps/frontend-nextjs && npm run dev"
    echo "  2. Test authentication: http://localhost:3000/login"
    echo "  3. Configure OAuth providers (see BACKEND_CONFIGURATION.md)"
    echo "  4. Set up email service for production"
    echo
    print_warning "Remember to:"
    echo "  • Update .env with production values"
    echo "  • Configure OAuth client IDs"
    echo "  • Set up email service for production"
    echo
}

# Cleanup function
cleanup() {
    if [ ! -z "$BACKEND_PID" ]; then
        print_status "Stopping backend process..."
        kill $BACKEND_PID 2>/dev/null || true
    fi
}

# Set up cleanup on script exit
trap cleanup EXIT

# Run main function
main "$@"
