# IntelliFin PRD - Non-Functional Requirements

## Non-Functional Requirements (NFR)

These describe how well the system performs, its quality attributes, and its operational aspects.

### Performance
*   **NFR1:** The initial load time for the web application on a standard 3G or better internet connection must be under 4 seconds.
*   **NFR2:** Responses to user commands in the conversational interface must feel near-instant. The system must provide visual feedback within 500ms of a command being submitted, and AI-generated text or data displays (not dependent on external APIs) should fully render within 2 seconds.
*   **NFR3:** The system must efficiently process and categorize up to 100 new mobile money transactions within 60 seconds of fetching them from the source API.

### Security
*   **NFR4:** All user data, especially Personally Identifiable Information (PII) and financial transaction data, must be encrypted both in transit (using TLS 1.2 or higher) and at rest.
*   **NFR5:** User passwords must never be stored in plaintext. They must be securely hashed and salted using a modern, strong algorithm (e.g., bcrypt).
*   **NFR6:** The system must implement the "Confirm & Commit" protocol, ensuring that no sensitive external action (e.g., submitting a ZRA invoice) is performed without explicit user review and approval on a clear confirmation screen.
*   **NFR7:** User sessions must automatically time out after a reasonable period of inactivity (e.g., 30 minutes), requiring the user to log in again to continue.

### Reliability & Availability
*   **NFR8:** The core IntelliFin application services shall maintain an uptime of 99.5%.
*   **NFR9:** The system must handle failures or timeouts from external third-party APIs (MTN, ZRA) gracefully. It must provide clear, user-friendly error messages (e.g., "We're having trouble connecting to MTN right now, please try again in a few minutes") without crashing or losing user data.
*   **NFR10:** The system must ensure full data integrity. Once a transaction is fetched from the source and stored in our database, it must not be lost or silently corrupted.

### Usability
*   **NFR11:** A first-time user must be able to complete the entire onboarding process (signup, login, connect MTN account) in under 5 minutes without needing a separate tutorial.
*   **NFR12:** The AI's Natural Language Understanding (NLU) must correctly interpret the user's intent for the core MVP commands (e.g., "create invoice," "show profit") with at least a 90% success rate during testing.

### Compatibility
*   **NFR13:** The web application must be fully functional and render correctly on the last two major versions of Google Chrome, Mozilla Firefox, Apple Safari, and Microsoft Edge.
*   **NFR14:** The user interface must be fully responsive, providing a high-quality, usable experience on screen sizes ranging from a standard smartphone (360px width) to a standard desktop monitor (1920px width).

---

**Previous Section:** [Functional Requirements](../prd/02-functional-requirements.md)  
**Next Section:** [User Interface Design Goals](../prd/04-ui-design-goals.md) 