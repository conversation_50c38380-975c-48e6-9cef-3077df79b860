# IntelliFin Product Requirements Document (PRD)

## Overview

This directory contains the sharded sections of the IntelliFin Product Requirements Document (PRD), broken down for easier navigation and development team access.

## Document Sections

1. **[Goals and Background Context](01-goals-and-background.md)** - Project goals and context
2. **[Functional Requirements](02-functional-requirements.md)** - System capabilities and features
3. **[Non-Functional Requirements](03-non-functional-requirements.md)** - Performance, security, and quality attributes
4. **[User Interface Design Goals](04-ui-design-goals.md)** - UX vision and interaction paradigms
5. **[Technical Assumptions](05-technical-assumptions.md)** - Architecture decisions and technology stack
6. **[Epic List](06-epic-list.md)** - Development epics and deliverables
7. **[Next Steps](07-next-steps.md)** - Immediate actions and handoffs

## Quick Reference

### Key Requirements
- **FR1-FR2:** User authentication and account management
- **FR3-FR4:** Financial account integration (MTN Mobile Money)
- **FR5-FR8:** AI-powered transaction categorization
- **FR9:** Conversational interface
- **FR10-FR12:** ZRA invoice management
- **FR13-FR14:** Financial reporting

### Development Epics
1. **Epic 1:** Local Development Environment & CI/CD Foundation
2. **Epic 2:** Cloud Provisioning & User Onboarding
3. **Epic 3:** Foundational AI Engine & Transaction Categorization
4. **Epic 4:** ZRA Smart Invoicing & Compliance Engine
5. **Epic 5:** Basic Financial Insights & Reporting

### Technology Stack
- **Frontend:** Next.js 14 with TypeScript
- **Backend Core:** Java with Spring Boot 3
- **AI Service:** Python with FastAPI
- **ZRA Service:** Containerized Java application
- **Database:** PostgreSQL
- **Cloud:** Microsoft Azure

## Related Documents

- **[Full PRD](../prd.md)** - Complete unsharded PRD document
- **[Architecture Document](../architecture/)** - Technical architecture details
- **[UI/UX Specification](../ui-ux/)** - Design and user experience details
- **[Epics and Stories](../stories/)** - Detailed vertically sliced stories

---

**Last Updated:** 2024-07-30  
**Version:** 1.0  
**Author:** John, PM 