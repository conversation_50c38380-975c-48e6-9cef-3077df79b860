# IntelliFin Environment Configuration Template
# Copy this file to .env and update with your actual values

# =============================================================================
# DATABASE CONFIGURATION
# =============================================================================
DATABASE_URL=**********************************************
DATABASE_USERNAME=intellifin_user
DATABASE_PASSWORD=intellifin_dev_password

# =============================================================================
# REDIS CONFIGURATION
# =============================================================================
REDIS_URL=redis://redis:6379

# =============================================================================
# JWT CONFIGURATION
# =============================================================================
# Generate a secure 256-bit key for production
JWT_SECRET=your-local-jwt-secret-key-min-256-bits-change-this
JWT_EXPIRATION=86400

# =============================================================================
# FRONTEND CONFIGURATION
# =============================================================================
FRONTEND_URL=http://localhost:3000

# =============================================================================
# EMAIL CONFIGURATION
# =============================================================================
EMAIL_ENABLED=false
EMAIL_FROM=<EMAIL>
SMTP_HOST=localhost
SMTP_PORT=587
SMTP_USERNAME=
SMTP_PASSWORD=
SMTP_AUTH=true
SMTP_STARTTLS=true

# =============================================================================
# OAUTH CONFIGURATION
# =============================================================================
# Google OAuth (get from Google Cloud Console)
GOOGLE_CLIENT_ID=your-google-client-id
GOOGLE_CLIENT_SECRET=your-google-client-secret

# Apple OAuth (get from Apple Developer Console)
APPLE_CLIENT_ID=your-apple-client-id
APPLE_CLIENT_SECRET=your-apple-client-secret

# =============================================================================
# AI SERVICE CONFIGURATION
# =============================================================================
# Local development with Ollama
OLLAMA_BASE_URL=http://ollama:11434

# Google Gemini for production (get from Google AI Studio)
GOOGLE_API_KEY=your-google-api-key-for-gemini

# OpenAI for embeddings (optional)
OPENAI_API_KEY=your-openai-api-key-for-embeddings

# =============================================================================
# EXTERNAL API CONFIGURATION
# =============================================================================
# MTN Mobile Money API (Development/Sandbox)
MTN_API_BASE_URL=https://sandbox.mtn.com/api
MTN_API_KEY=sandbox-key-get-from-mtn-developer-portal
MTN_CLIENT_ID=your-mtn-client-id
MTN_CLIENT_SECRET=your-mtn-client-secret

# ZRA VSDC API (Development/Sandbox)
ZRA_API_BASE_URL=https://sandbox.zra.org.zm/api
ZRA_API_KEY=sandbox-key-get-from-zra
ZRA_CLIENT_ID=your-zra-client-id
ZRA_CLIENT_SECRET=your-zra-client-secret

# Stitch API for bank integration (optional)
STITCH_API_BASE_URL=https://api.stitch.money
STITCH_CLIENT_ID=your-stitch-client-id
STITCH_CLIENT_SECRET=your-stitch-client-secret

# =============================================================================
# FRONTEND CONFIGURATION
# =============================================================================
NEXT_PUBLIC_API_BASE_URL=http://localhost:8080
NEXT_PUBLIC_WS_URL=ws://localhost:8080
NEXT_PUBLIC_AI_SERVICE_URL=http://localhost:8000

# =============================================================================
# LOGGING CONFIGURATION
# =============================================================================
LOG_LEVEL=DEBUG

# =============================================================================
# GOOGLE CLOUD CONFIGURATION (Production)
# =============================================================================
# GCP_PROJECT_ID=intellifin-prod
# GCP_ZONE=us-central1-a
# GOOGLE_APPLICATION_CREDENTIALS=/path/to/service-account.json

# =============================================================================
# MONITORING CONFIGURATION (Production)
# =============================================================================
# SENTRY_DSN=your-sentry-dsn
# PROMETHEUS_ENABLED=true
# GRAFANA_URL=https://grafana.intellifin.com

# =============================================================================
# EMAIL CONFIGURATION (Production)
# =============================================================================
# SMTP_HOST=smtp.gmail.com
# SMTP_PORT=587
# SMTP_USERNAME=<EMAIL>
# SMTP_PASSWORD=your-smtp-password
# FROM_EMAIL=<EMAIL>

# =============================================================================
# SECURITY CONFIGURATION
# =============================================================================
# CORS_ALLOWED_ORIGINS=https://app.intellifin.com,https://admin.intellifin.com
# RATE_LIMIT_REQUESTS_PER_MINUTE=60
# RATE_LIMIT_REQUESTS_PER_HOUR=1000

# =============================================================================
# FEATURE FLAGS
# =============================================================================
FEATURE_VOICE_COMMANDS=false
FEATURE_MULTI_LANGUAGE=false
FEATURE_INVENTORY_MANAGEMENT=false
FEATURE_BANK_INTEGRATION=false
