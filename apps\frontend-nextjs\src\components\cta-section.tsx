"use client"

import React from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { AnimatedSection } from "@/components/ui/animated-section"
import { FloatingShapes } from "@/components/ui/floating-shapes"

export function CTASection() {
  return (
    <section className="relative py-24 bg-gradient-hero-alt overflow-hidden">
      <FloatingShapes />

      <div className="container mx-auto px-6 text-center relative z-10">
        <AnimatedSection>
          <h2 className="text-4xl md:text-5xl font-bold text-white mb-8">
            Ready to transform your business?
          </h2>
        </AnimatedSection>

        <AnimatedSection delay={0.2}>
          <p className="text-xl text-white/80 mb-12 max-w-2xl mx-auto">
            Join hundreds of Zambian businesses already growing with AI-powered accounting
          </p>
        </AnimatedSection>

        <AnimatedSection delay={0.4}>
          <Button
            size="lg"
            className="bg-white/20 hover:bg-white/30 text-white font-medium px-8 py-4 text-lg rounded-full border border-white/20 backdrop-blur-sm transition-all"
          >
            Request a Demo
          </Button>
        </AnimatedSection>
      </div>
    </section>
  )
}
