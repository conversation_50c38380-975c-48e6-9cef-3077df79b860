# IntelliFin Fullstack Architecture

## Overview

This directory contains the sharded sections of the IntelliFin Fullstack Architecture Document, providing comprehensive technical guidance for the entire system implementation.

## Document Sections

1. **[Introduction](01-introduction.md)** - Architecture overview and project context
2. **[High Level Architecture](02-high-level-architecture.md)** - Foundational architectural decisions and patterns
3. **[Tech Stack](03-tech-stack.md)** - Definitive technology selection and rationale
4. **[Data Models](04-data-models.md)** - Core data models and TypeScript interfaces
5. **[API Specification](05-api-specification.md)** - Hybrid API strategy (WebSocket + REST)
6. **[External Integrations](06-external-integrations.md)** - MTN, Stitch, ZRA integration strategies
7. **[Security & Compliance](07-security-compliance.md)** - Security measures and regulatory compliance
8. **[Core Financial Engine](08-core-financial-engine.md)** - Double-entry bookkeeping and accounting logic
9. **[Frontend Architecture](09-frontend-architecture.md)** - Next.js implementation and component patterns

## Quick Reference

### Architecture Patterns
- **Polyglot Microservices:** Java Spring Boot (core) + Python FastAPI (AI) + Next.js (frontend)
- **Hybrid API Strategy:** WebSocket conversational gateway + RESTful resource APIs
- **Event-Driven Architecture:** Webhooks and internal event queues for external integrations
- **Double-Entry Bookkeeping:** Core accounting logic with JournalEntry and Chart of Accounts

### Technology Stack
- **Frontend:** Next.js 14, TypeScript, Shadcn/ui, Zustand, Tailwind CSS
- **Backend Core:** Java 17/21, Spring Boot 3, PostgreSQL, Redis
- **AI Service:** Python 3.10+, FastAPI, LangChain4j, Google Gemini
- **Infrastructure:** Docker, GitHub Actions, Google Cloud Platform
- **Security:** JWT, Spring Security, TLS 1.2+, encryption at rest/transit

### Key Integrations
- **MTN Mobile Money:** Webhook-prioritized integration for real-time transactions
- **Stitch Aggregator:** OAuth-based bank integration for broader financial visibility
- **ZRA VSDC:** Internal API contract with dedicated ZRA Compliance Service

### Data Models
- **Core Entities:** User, Transaction, Category, FinancialAccount, Client, Invoice
- **Accounting:** JournalEntry, Account (Chart of Accounts)
- **Relationships:** One-to-many, many-to-one with proper foreign key constraints

## Development Guidelines

### Service Independence
- Each service can be developed, tested, and deployed independently
- API contracts define service boundaries
- Circuit breakers prevent cascading failures
- Graceful degradation when services are unavailable

### Security Principles
- **Accountant's Constitution:** Confirm & Commit protocol for sensitive actions
- **Zero Trust:** JWT authentication, RBAC, least privilege principle
- **Data Protection:** Encryption at rest/transit, secure credential management
- **Compliance:** ZRA requirements, data privacy regulations, regular audits

### Performance & Scalability
- **Real-time Communication:** WebSocket for conversational interface
- **Caching Strategy:** Redis for session management and real-time data
- **Database Optimization:** Proper indexing, query optimization, connection pooling
- **Monitoring:** Azure Monitor/Application Insights for comprehensive observability

## Related Documents

- **[Full Architecture Document](../architecture.md)** - Complete unsharded architecture document
- **[PRD](../prd/)** - Product requirements and functional specifications
- **[UI/UX Specification](../ui-ux/)** - Design and user experience details
- **[Epics and Stories](../stories/)** - Vertically sliced development stories

---

**Last Updated:** 2024-07-30  
**Version:** 1.0  
**Author:** Architect 