# IntelliFin - Intelligent Financial Platform for Zambian SMEs

[![Build Status](https://github.com/your-org/intellifin/workflows/CI/badge.svg)](https://github.com/your-org/intellifin/actions)
[![License: MIT](https://img.shields.io/badge/License-MIT-yellow.svg)](https://opensource.org/licenses/MIT)
[![Coverage](https://codecov.io/gh/your-org/intellifin/branch/main/graph/badge.svg)](https://codecov.io/gh/your-org/intellifin)

## Overview

IntelliFin is a revolutionary conversational AI-powered financial platform designed specifically for Zambian small and medium enterprises (SMEs). It transforms complex accounting and financial management into intuitive dialogue-driven actions, addressing the critical mobile money accounting gap in Zambia.

### Key Features

- 🤖 **Conversational AI Interface** - Natural language commands for all financial operations
- 📱 **Mobile Money Integration** - Automatic MTN Mobile Money transaction tracking
- 🧾 **ZRA Smart Invoice Compliance** - Automated invoice creation and submission
- 📊 **AI-Powered Categorization** - Intelligent transaction categorization with Zambian context
- 💰 **Real-time Financial Insights** - Instant profit/loss calculations and reporting
- 🔒 **Bank-Grade Security** - End-to-end encryption and comprehensive audit trails

## Quick Start

### Prerequisites

- Docker 24.0+ and Docker Compose 2.0+
- Node.js 18+ (for frontend development)
- Java 21+ (for backend development)
- Python 3.10+ (for AI service development)
- Git 2.30+

### Installation

```bash
# Clone the repository
git clone https://github.com/your-org/intellifin.git
cd intellifin

# Copy environment configuration
cp .env.example .env
# Edit .env with your configuration values

# Install dependencies
npm install

# Start all services
npm run dev

# Initialize database (in a new terminal)
docker-compose exec postgres psql -U intellifin_user -d intellifin_dev -f /docker-entrypoint-initdb.d/001_initial_schema.sql
docker-compose exec postgres psql -U intellifin_user -d intellifin_dev -f /docker-entrypoint-initdb.d/002_seed_data.sql

# Setup AI models
docker-compose exec ollama ollama pull llama3.1:8b

# Verify installation
npm run health-check
```

## 🔧 Backend Configuration

### Automated Setup (Recommended)

For a complete backend setup with authentication integration:

**Windows (PowerShell):**
```powershell
.\scripts\setup-backend.ps1
```

**Linux/macOS:**
```bash
chmod +x scripts/setup-backend.sh
./scripts/setup-backend.sh
```

### Manual Backend Setup

1. **Environment Configuration**
   ```bash
   # Copy and edit environment variables
   cp .env.example .env
   # Update JWT_SECRET, database credentials, OAuth settings
   ```

2. **Start Infrastructure**
   ```bash
   docker-compose up -d postgres redis
   ```

3. **Run Database Migrations**
   ```bash
   cd apps/backend-java-core
   ./mvnw flyway:migrate
   ```

4. **Start Backend**
   ```bash
   ./mvnw spring-boot:run
   ```

5. **Test Backend**
   ```bash
   # Run automated tests
   ./scripts/test-backend.sh

   # Manual health check
   curl http://localhost:8080/actuator/health
   ```

### Authentication Configuration

The backend includes complete authentication integration:

- ✅ **JWT Authentication** - Token-based auth with refresh
- ✅ **User Registration** - Email verification support
- ✅ **Password Reset** - Secure token-based reset flow
- ✅ **Account Security** - Lockout protection, password policies
- 🔄 **OAuth Integration** - Google/Apple login (setup required)
- 🔄 **Email Service** - SMTP/SendGrid integration (setup required)

For detailed configuration, see: [`apps/backend-java-core/BACKEND_CONFIGURATION.md`](apps/backend-java-core/BACKEND_CONFIGURATION.md)

### Access the Application

- **Frontend**: http://localhost:3000
- **Backend API**: http://localhost:8080
- **API Documentation**: http://localhost:8080/swagger-ui.html
- **Health Check**: http://localhost:8080/actuator/health
- **Database Admin**: http://localhost:5050 (pgAdmin)

## Architecture

IntelliFin follows a microservices architecture with the following components:

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Frontend      │    │   API Gateway   │    │   Load Balancer │
│   (Next.js)     │◄──►│   (Spring Boot) │◄──►│   (Nginx)       │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                                │
                ┌───────────────┼───────────────┐
                │               │               │
        ┌───────▼──────┐ ┌──────▼──────┐ ┌─────▼──────┐
        │ Core Backend │ │ AI Service  │ │ ZRA Service│
        │ (Java)       │ │ (Python)    │ │ (Java)     │
        └──────────────┘ └─────────────┘ └────────────┘
                │               │               │
        ┌───────▼──────┐ ┌──────▼──────┐ ┌─────▼──────┐
        │ PostgreSQL   │ │ Redis Cache │ │ Vector DB  │
        │ Database     │ │             │ │ (AI Search)│
        └──────────────┘ └─────────────┘ └────────────┘
```

### Technology Stack

| Component | Technology | Purpose |
|-----------|------------|---------|
| Frontend | Next.js 14, TypeScript, Tailwind CSS | User interface and experience |
| Backend Core | Java 21, Spring Boot 3 | Business logic and API |
| AI Service | Python 3.10, FastAPI, LangChain | Natural language processing |
| ZRA Service | Java 21, Spring Boot 3 | Tax compliance integration |
| Database | PostgreSQL 15 | Primary data storage |
| Cache | Redis 7 | Session and response caching |
| AI Models | Ollama (dev), Google Gemini (prod) | Language understanding |
| Message Queue | Redis Pub/Sub | Inter-service communication |

## Project Structure

```
intellifin/
├── apps/
│   ├── frontend-nextjs/          # Next.js frontend application
│   ├── backend-java-core/        # Core business logic service
│   ├── backend-python-ai/        # AI and ML service
│   └── backend-java-zra/         # ZRA compliance service
├── docs/
│   ├── api/                      # API specifications
│   ├── architecture/             # Architecture documentation
│   ├── database/                 # Database schemas and migrations
│   ├── deployment/               # Deployment guides
│   ├── development/              # Development setup
│   ├── security/                 # Security implementation
│   ├── testing/                  # Test specifications
│   └── ai/                       # AI implementation guides
├── scripts/                      # Utility scripts
├── k8s/                         # Kubernetes manifests
├── docker-compose.yml           # Development environment
├── .env.example                 # Environment template
└── README.md                    # This file
```

## Development

### Getting Started

1. **Setup Development Environment**
   ```bash
   # Follow the detailed setup guide
   cat docs/development/setup-guide.md
   ```

2. **Run Tests**
   ```bash
   # Run all tests
   npm run test:all
   
   # Run specific service tests
   cd apps/backend-java-core && ./mvnw test
   cd apps/backend-python-ai && pytest
   cd apps/frontend-nextjs && npm test
   ```

3. **Development Workflow**
   ```bash
   # Create feature branch
   git checkout -b feature/your-feature
   
   # Make changes and test
   npm run test:all
   
   # Commit and push
   git commit -m "feat: your feature description"
   git push origin feature/your-feature
   ```

### Key Documentation

- 📖 [Development Setup Guide](docs/development/setup-guide.md)
- 🏗️ [Architecture Overview](docs/architecture.md)
- 🔌 [API Documentation](docs/api/openapi-spec.yaml)
- 🤖 [AI Implementation Guide](docs/ai/implementation-guide.md)
- 🔒 [Security Guide](docs/security/implementation-guide.md)
- 🧪 [Testing Specifications](docs/testing/test-specifications.md)
- 🚀 [Deployment Guide](docs/deployment/deployment-guide.md)

## API Usage

### REST API Example

```javascript
// Create a transaction
const response = await fetch('/api/v1/transactions', {
  method: 'POST',
  headers: {
    'Authorization': `Bearer ${token}`,
    'Content-Type': 'application/json'
  },
  body: JSON.stringify({
    description: 'Payment to ZESCO for electricity',
    amount: 450.00,
    type: 'EXPENSE',
    date: '2024-07-30'
  })
});

const transaction = await response.json();
console.log('AI Suggestion:', transaction.suggestedCategory);
```

### WebSocket API Example

```javascript
// Connect to conversational interface
const ws = new WebSocket(`ws://localhost:8080/ws/conversation?token=${token}`);

// Send natural language command
ws.send(JSON.stringify({
  type: 'COMMAND',
  sessionId: 'session-id',
  messageId: 'msg-id',
  payload: {
    command: 'Show me my profit this month'
  }
}));

// Receive AI response
ws.onmessage = (event) => {
  const response = JSON.parse(event.data);
  console.log('AI Response:', response.payload.message);
};
```

## Deployment

### Production Deployment

```bash
# Deploy to Google Cloud Platform
gcloud container clusters get-credentials intellifin-cluster
kubectl apply -f k8s/production/

# Monitor deployment
kubectl rollout status deployment/intellifin-backend-core
```

### Environment Configuration

| Environment | URL | Purpose |
|-------------|-----|---------|
| Development | http://localhost:3000 | Local development |
| Staging | https://staging.intellifin.com | Testing and QA |
| Production | https://app.intellifin.com | Live application |

## Contributing

We welcome contributions! Please see our [Contributing Guide](CONTRIBUTING.md) for details.

### Development Process

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Ensure all tests pass
6. Submit a pull request

### Code Standards

- **Java**: Follow Google Java Style Guide
- **Python**: Follow PEP 8 with Black formatting
- **TypeScript**: Follow Airbnb TypeScript Style Guide
- **Commit Messages**: Follow Conventional Commits

## Security

IntelliFin takes security seriously. Please see our [Security Policy](SECURITY.md) for:

- Vulnerability reporting
- Security best practices
- Compliance information

## License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## Support

- 📧 **Email**: <EMAIL>
- 💬 **Slack**: [IntelliFin Workspace](https://intellifin.slack.com)
- 📖 **Documentation**: [docs.intellifin.com](https://docs.intellifin.com)
- 🐛 **Issues**: [GitHub Issues](https://github.com/your-org/intellifin/issues)

## Roadmap

### Current Version (v1.0)
- ✅ Conversational AI interface
- ✅ MTN Mobile Money integration
- ✅ ZRA Smart Invoice compliance
- ✅ Basic financial reporting

### Upcoming Features (v1.1)
- 🔄 Airtel and Zamtel mobile money support
- 🔄 Bank account integration via Stitch
- 🔄 Inventory management
- 🔄 Multi-user collaboration

### Future Vision (v2.0+)
- 🔮 Voice commands and local language support
- 🔮 Proactive AI financial advisor
- 🔮 Direct loan application integration
- 🔮 Geographic expansion across Africa

---

**Built with ❤️ for Zambian entrepreneurs**
