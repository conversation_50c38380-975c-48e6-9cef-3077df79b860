# Quick Task Assignment Guide

## 🚨 CRITICAL BLOCKER - Immediate Assignment Needed

### Task: Fix Lombok Annotation Processing in Maven Build
**Assignee**: Java Developer with Maven/Spring Boot experience  
**Priority**: P0 (Blocking all backend work)  
**Estimated Time**: 4-6 hours  

#### Problem Statement
Maven compilation fails because Lombok annotations (@Data, @Builder, @Slf4j) are not generating getter/setter methods and logger instances during the build process.

#### Current Error
```
[ERROR] cannot find symbol
[ERROR]   symbol:   method getPassword()
[ERROR]   location: variable request of type com.intellifin.dto.auth.LoginRequest
```

#### Investigation Points
1. **Version Mismatch**: Spring Boot manages Lombok 1.18.30, but annotation processor uses 1.18.34
2. **Java 21 Compatibility**: Ensure Lombok version supports Java 21 features
3. **Maven Configuration**: Annotation processor path configuration in pom.xml

#### Files to Focus On
- `apps/backend-java-core/pom.xml` (lines 180-200, <PERSON>ven compiler plugin)
- `apps/backend-java-core/src/main/java/com/intellifin/dto/auth/LoginRequest.java`
- `apps/backend-java-core/src/main/java/com/intellifin/model/User.java`

#### Success Criteria
- [ ] `./mvnw clean compile` succeeds without errors
- [ ] No manual getter/setter methods needed
- [ ] Backend starts with `./mvnw spring-boot:run`
- [ ] Test script `./scripts/test-backend.sh` passes

#### Resources
- Current working directory: `d:\Projects\Intellifin\apps\backend-java-core`
- Lombok documentation: https://projectlombok.org/setup/maven
- Spring Boot Lombok integration guide

---

## 🔧 READY FOR ASSIGNMENT - Well-Defined Tasks

### Task A: OAuth Backend Implementation
**Assignee**: Java Developer with OAuth/Spring Security experience  
**Priority**: P1 (High business value)  
**Estimated Time**: 8-12 hours  
**Dependencies**: Lombok fix must be completed first  

#### Scope
Implement Google and Apple OAuth authentication flows in the backend.

#### Current State
- Placeholder endpoints exist in `AuthController.java`
- Frontend OAuth service ready and waiting
- Environment variables configured

#### Implementation Required
1. **Google OAuth Flow**:
   - Implement `GET /api/v1/auth/oauth/google` (redirect to Google)
   - Implement `POST /api/v1/auth/oauth/google/callback` (handle response)
   - User creation/authentication logic

2. **Apple OAuth Flow**:
   - Implement `GET /api/v1/auth/oauth/apple` (redirect to Apple)
   - Implement `POST /api/v1/auth/oauth/apple/callback` (handle response)
   - User creation/authentication logic

#### Files to Modify
- `apps/backend-java-core/src/main/java/com/intellifin/controller/AuthController.java`
- `apps/backend-java-core/src/main/java/com/intellifin/service/AuthService.java`
- Create new OAuth service classes

### Task B: Email Service Integration
**Assignee**: Java Developer (Junior-Mid level acceptable)  
**Priority**: P2 (Production readiness)  
**Estimated Time**: 4-6 hours  
**Dependencies**: Lombok fix must be completed first  

#### Scope
Implement email sending for verification and password reset.

#### Current State
- Email configuration exists in `application.yml`
- Email templates needed
- SMTP configuration ready

#### Implementation Required
1. Create email service with SMTP support
2. Design email templates (HTML/text)
3. Integrate with registration and password reset flows
4. Add SendGrid support as alternative

#### Files to Create/Modify
- Create `EmailService.java`
- Create email templates in `src/main/resources/templates/`
- Update `AuthService.java` to use email service

### Task C: Frontend-Backend Integration Testing
**Assignee**: Full-stack Developer or QA Engineer  
**Priority**: P2 (Quality assurance)  
**Estimated Time**: 2-4 hours  
**Dependencies**: Lombok fix must be completed first  

#### Scope
Comprehensive end-to-end testing of authentication flows.

#### Test Scenarios
1. User registration → email verification → login
2. Login → token refresh → protected endpoint access
3. Forgot password → email → reset password → login
4. Error handling and edge cases

#### Tools
- Frontend: http://localhost:3000
- Backend: http://localhost:8080
- API docs: http://localhost:8080/swagger-ui.html
- Test script: `./scripts/test-backend.sh`

---

## 📋 TASK ASSIGNMENT CHECKLIST

### Before Starting Any Task
- [ ] Pull latest code from repository
- [ ] Verify development environment setup
- [ ] Review relevant documentation
- [ ] Understand acceptance criteria

### For Lombok Task Specifically
- [ ] Confirm Java 21 is installed and active
- [ ] Verify VS Code Java extensions are working
- [ ] Review current pom.xml configuration
- [ ] Test compilation before making changes

### For OAuth Tasks
- [ ] Set up Google Cloud Console project
- [ ] Set up Apple Developer account
- [ ] Obtain OAuth client credentials
- [ ] Update environment variables

### For Email Tasks
- [ ] Choose email provider (SMTP or SendGrid)
- [ ] Obtain email service credentials
- [ ] Design email templates
- [ ] Test email delivery

---

## 🚀 Getting Started Commands

### Environment Setup
```bash
# Clone and navigate
git clone <repository-url>
cd intellifin

# Copy environment file
cp .env.example .env
# Edit .env with your configuration

# Start infrastructure
docker-compose up -d postgres redis
```

### Development Workflow
```bash
# Backend development
cd apps/backend-java-core
./mvnw clean compile  # Should work after Lombok fix
./mvnw spring-boot:run

# Frontend development (separate terminal)
cd apps/frontend-nextjs
npm install
npm run dev

# Testing
./scripts/test-backend.sh  # Backend API tests
```

### Verification Steps
```bash
# Health checks
curl http://localhost:8080/actuator/health
curl http://localhost:3000/api/health

# API documentation
open http://localhost:8080/swagger-ui.html

# Frontend application
open http://localhost:3000
```

---

## 📞 Need Help?

### Documentation References
- **Project Overview**: `README.md`
- **Backend Setup**: `apps/backend-java-core/BACKEND_CONFIGURATION.md`
- **Frontend Integration**: `apps/frontend-nextjs/AUTHENTICATION_INTEGRATION.md`
- **Full Project Status**: `PROJECT_STATUS_HANDOFF.md`

### Common Issues & Solutions
1. **Port conflicts**: Check if ports 3000, 8080, 5432, 6379 are available
2. **Java version**: Ensure Java 21 is active (`java -version`)
3. **Docker issues**: Restart Docker Desktop, check container logs
4. **Maven issues**: Clear `.m2/repository` cache if needed

### Quick Debugging
```bash
# Check service status
docker-compose ps

# View logs
docker-compose logs backend-core
docker-compose logs postgres

# Maven debugging
./mvnw clean compile -X  # Verbose output
```

---

**Last Updated**: December 2024  
**Next Review**: After Lombok issue resolution
