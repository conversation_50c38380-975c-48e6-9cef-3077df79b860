package com.intellifin.dto.auth;

import com.intellifin.dto.user.UserDto;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AuthResponse {

    private String token;
    
    private String tokenType;
    
    private Long expiresIn; // seconds
    
    private LocalDateTime expiresAt;
    
    private UserDto user;
    
    private String refreshToken;
    
    @Builder.Default
    private String message = "Authentication successful";

    // Helper method to create successful auth response
    public static AuthResponse success(String token, Long expiresIn, LocalDateTime expiresAt, UserDto user) {
        return AuthResponse.builder()
                .token(token)
                .tokenType("Bearer")
                .expiresIn(expiresIn)
                .expiresAt(expiresAt)
                .user(user)
                .message("Authentication successful")
                .build();
    }

    // Helper method to create registration response
    public static AuthResponse registration(String token, Long expiresIn, LocalDateTime expiresAt, UserDto user) {
        return AuthResponse.builder()
                .token(token)
                .tokenType("Bearer")
                .expiresIn(expiresIn)
                .expiresAt(expiresAt)
                .user(user)
                .message("Registration successful. Please check your email to verify your account.")
                .build();
    }
}
