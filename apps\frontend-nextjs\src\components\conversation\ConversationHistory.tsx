"use client"

import React from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  MessageSquare, 
  User, 
  <PERSON><PERSON><PERSON>, 
  <PERSON>, 
  Trash2,
  MoreVertical
} from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { useConversationStore } from '@/stores';
import { formatRelativeTime } from '@/utils';

export function ConversationHistory() {
  const { 
    messages, 
    currentSessionId,
    clearMessages,
    isConnected 
  } = useConversationStore();

  const handleClearHistory = () => {
    if (confirm('Are you sure you want to clear the conversation history?')) {
      clearMessages();
    }
  };

  const groupedMessages = messages.reduce((groups, message) => {
    const date = message.timestamp.toDateString();
    if (!groups[date]) {
      groups[date] = [];
    }
    groups[date].push(message);
    return groups;
  }, {} as Record<string, typeof messages>);

  const sortedDates = Object.keys(groupedMessages).sort((a, b) => 
    new Date(b).getTime() - new Date(a).getTime()
  );

  return (
    <Card className="h-full flex flex-col">
      <CardHeader className="pb-3">
        <CardTitle className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <MessageSquare className="w-5 h-5 text-primary" />
            <span>Conversation History</span>
          </div>
          <div className="flex items-center space-x-2">
            {messages.length > 0 && (
              <Button
                variant="ghost"
                size="sm"
                onClick={handleClearHistory}
                className="text-gray-500 hover:text-red-600"
              >
                <Trash2 className="w-4 h-4" />
              </Button>
            )}
            <Button variant="ghost" size="sm">
              <MoreVertical className="w-4 h-4" />
            </Button>
          </div>
        </CardTitle>
        
        {/* Session Info */}
        <div className="flex items-center space-x-2 text-xs text-gray-500">
          <div className={`w-2 h-2 rounded-full ${isConnected ? 'bg-green-500' : 'bg-red-500'}`} />
          <span>Session: {currentSessionId?.slice(-8) || 'Not connected'}</span>
        </div>
      </CardHeader>

      <CardContent className="flex-1 overflow-y-auto space-y-4 pb-4">
        {messages.length === 0 ? (
          <div className="flex flex-col items-center justify-center h-full text-center py-8">
            <div className="w-12 h-12 bg-gray-100 rounded-full flex items-center justify-center mb-3">
              <MessageSquare className="w-6 h-6 text-gray-400" />
            </div>
            <p className="text-sm text-gray-500">
              Your conversation history will appear here
            </p>
          </div>
        ) : (
          <div className="space-y-6">
            {sortedDates.map((date) => (
              <div key={date}>
                {/* Date Header */}
                <div className="flex items-center space-x-2 mb-3">
                  <Clock className="w-3 h-3 text-gray-400" />
                  <span className="text-xs font-medium text-gray-500 uppercase tracking-wide">
                    {new Date(date).toLocaleDateString('en-US', { 
                      weekday: 'short', 
                      month: 'short', 
                      day: 'numeric' 
                    })}
                  </span>
                  <div className="flex-1 h-px bg-gray-200" />
                </div>

                {/* Messages for this date */}
                <div className="space-y-3">
                  <AnimatePresence>
                    {groupedMessages[date].map((message, index) => (
                      <motion.div
                        key={message.id}
                        initial={{ opacity: 0, y: 10 }}
                        animate={{ opacity: 1, y: 0 }}
                        exit={{ opacity: 0, y: -10 }}
                        transition={{ duration: 0.2 }}
                        className={`flex items-start space-x-2 ${
                          message.role === 'USER' ? 'flex-row-reverse space-x-reverse' : ''
                        }`}
                      >
                        {/* Avatar */}
                        <div className={`w-6 h-6 rounded-full flex items-center justify-center flex-shrink-0 ${
                          message.role === 'USER' 
                            ? 'bg-primary text-white' 
                            : 'bg-gradient-primary text-white'
                        }`}>
                          {message.role === 'USER' ? (
                            <User className="w-3 h-3" />
                          ) : (
                            <Sparkles className="w-3 h-3" />
                          )}
                        </div>
                        
                        {/* Message Content */}
                        <div className={`flex-1 min-w-0 ${
                          message.role === 'USER' ? 'text-right' : 'text-left'
                        }`}>
                          <div className={`inline-block max-w-full rounded-lg px-3 py-2 text-xs ${
                            message.role === 'USER'
                              ? 'bg-primary text-white'
                              : 'bg-gray-100 text-gray-900'
                          }`}>
                            <p className="break-words">
                              {message.content.length > 100 
                                ? `${message.content.substring(0, 100)}...` 
                                : message.content
                              }
                            </p>
                          </div>
                          
                          {/* Intent Recognition */}
                          {message.intentRecognized && (
                            <div className={`text-xs mt-1 opacity-75 ${
                              message.role === 'USER' ? 'text-gray-500' : 'text-gray-400'
                            }`}>
                              Intent: {message.intentRecognized}
                              {message.confidenceScore && ` (${Math.round(message.confidenceScore * 100)}%)`}
                            </div>
                          )}
                          
                          {/* Timestamp */}
                          <div className={`text-xs mt-1 opacity-75 ${
                            message.role === 'USER' ? 'text-gray-500' : 'text-gray-400'
                          }`}>
                            {formatRelativeTime(message.timestamp)}
                          </div>
                        </div>
                      </motion.div>
                    ))}
                  </AnimatePresence>
                </div>
              </div>
            ))}
          </div>
        )}

        {/* Scroll to bottom indicator */}
        {messages.length > 10 && (
          <div className="text-center py-2">
            <span className="text-xs text-gray-400">
              {messages.length} messages total
            </span>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
