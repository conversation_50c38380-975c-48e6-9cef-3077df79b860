import { apiClient } from './client';
import {
  FinancialAccount,
  ConnectAccountRequest
} from '@/types/api';

/**
 * Financial Accounts API Service
 * Handles all financial account-related API calls
 */
export class AccountsService {
  /**
   * Get all connected financial accounts
   */
  static async getAccounts(): Promise<FinancialAccount[]> {
    const response = await apiClient.get<FinancialAccount[]>('/api/v1/financial-accounts');
    return response.data;
  }

  /**
   * Get single financial account by ID
   */
  static async getAccount(id: string): Promise<FinancialAccount> {
    const response = await apiClient.get<FinancialAccount>(`/api/v1/financial-accounts/${id}`);
    return response.data;
  }

  /**
   * Connect MTN Mobile Money account
   */
  static async connectMTNAccount(credentials: {
    phoneNumber: string;
    pin: string;
    accountName?: string;
  }): Promise<FinancialAccount> {
    const response = await apiClient.post<FinancialAccount>('/api/v1/financial-accounts/connect/mtn', {
      provider: 'MTN Mobile Money',
      accountName: credentials.accountName || 'MTN Mobile Money',
      credentials: {
        phoneNumber: credentials.phoneNumber,
        pin: credentials.pin
      }
    });
    return response.data;
  }

  /**
   * Connect Airtel Money account
   */
  static async connectAirtelAccount(credentials: {
    phoneNumber: string;
    pin: string;
    accountName?: string;
  }): Promise<FinancialAccount> {
    const response = await apiClient.post<FinancialAccount>('/api/v1/financial-accounts/connect/airtel', {
      provider: 'Airtel Money',
      accountName: credentials.accountName || 'Airtel Money',
      credentials: {
        phoneNumber: credentials.phoneNumber,
        pin: credentials.pin
      }
    });
    return response.data;
  }

  /**
   * Connect bank account via Stitch
   */
  static async connectBankAccount(bankData: {
    bankName: string;
    accountName: string;
    stitchToken: string;
  }): Promise<FinancialAccount> {
    const response = await apiClient.post<FinancialAccount>('/api/v1/financial-accounts/connect/bank', {
      provider: bankData.bankName,
      accountName: bankData.accountName,
      credentials: {
        stitchToken: bankData.stitchToken
      }
    });
    return response.data;
  }

  /**
   * Update account details
   */
  static async updateAccount(id: string, updates: {
    accountName?: string;
  }): Promise<FinancialAccount> {
    const response = await apiClient.put<FinancialAccount>(`/api/v1/financial-accounts/${id}`, updates);
    return response.data;
  }

  /**
   * Disconnect account
   */
  static async disconnectAccount(id: string): Promise<void> {
    await apiClient.delete(`/api/v1/financial-accounts/${id}`);
  }

  /**
   * Sync account transactions
   */
  static async syncAccount(id: string): Promise<{
    status: 'SUCCESS' | 'PARTIAL' | 'FAILED';
    newTransactions: number;
    errors?: string[];
  }> {
    const response = await apiClient.post(`/api/v1/financial-accounts/${id}/sync`);
    return response.data;
  }

  /**
   * Get account balance
   */
  static async getAccountBalance(id: string): Promise<{
    balance: number;
    currency: string;
    lastUpdated: string;
  }> {
    const response = await apiClient.get(`/api/v1/financial-accounts/${id}/balance`);
    return response.data;
  }

  /**
   * Get account transaction history
   */
  static async getAccountTransactions(id: string, params?: {
    startDate?: string;
    endDate?: string;
    limit?: number;
    offset?: number;
  }): Promise<{
    transactions: any[];
    total: number;
  }> {
    const queryString = params ? new URLSearchParams(params as any).toString() : '';
    const url = `/api/v1/financial-accounts/${id}/transactions${queryString ? `?${queryString}` : ''}`;
    
    const response = await apiClient.get(url);
    return response.data;
  }

  /**
   * Test account connection
   */
  static async testConnection(id: string): Promise<{
    status: 'CONNECTED' | 'DISCONNECTED' | 'ERROR';
    message: string;
    lastTested: string;
  }> {
    const response = await apiClient.post(`/api/v1/financial-accounts/${id}/test`);
    return response.data;
  }

  /**
   * Get supported providers
   */
  static async getSupportedProviders(): Promise<Array<{
    id: string;
    name: string;
    type: 'MOBILE_MONEY' | 'BANK' | 'DIGITAL_WALLET';
    logo?: string;
    supported: boolean;
    comingSoon?: boolean;
  }>> {
    const response = await apiClient.get('/api/v1/financial-accounts/providers');
    return response.data;
  }

  /**
   * Get account connection status
   */
  static async getConnectionStatus(): Promise<{
    totalAccounts: number;
    connectedAccounts: number;
    disconnectedAccounts: number;
    errorAccounts: number;
    lastSync: string;
  }> {
    const response = await apiClient.get('/api/v1/financial-accounts/status');
    return response.data;
  }

  /**
   * Enable/disable automatic sync for account
   */
  static async toggleAutoSync(id: string, enabled: boolean): Promise<FinancialAccount> {
    const response = await apiClient.patch<FinancialAccount>(`/api/v1/financial-accounts/${id}/auto-sync`, {
      enabled
    });
    return response.data;
  }

  /**
   * Set sync frequency for account
   */
  static async setSyncFrequency(id: string, frequency: 'HOURLY' | 'DAILY' | 'WEEKLY'): Promise<FinancialAccount> {
    const response = await apiClient.patch<FinancialAccount>(`/api/v1/financial-accounts/${id}/sync-frequency`, {
      frequency
    });
    return response.data;
  }

  /**
   * Get account sync history
   */
  static async getSyncHistory(id: string): Promise<Array<{
    id: string;
    timestamp: string;
    status: 'SUCCESS' | 'PARTIAL' | 'FAILED';
    newTransactions: number;
    duration: number;
    errors?: string[];
  }>> {
    const response = await apiClient.get(`/api/v1/financial-accounts/${id}/sync-history`);
    return response.data;
  }

  /**
   * Refresh account credentials
   */
  static async refreshCredentials(id: string, newCredentials: Record<string, any>): Promise<FinancialAccount> {
    const response = await apiClient.post<FinancialAccount>(`/api/v1/financial-accounts/${id}/refresh-credentials`, {
      credentials: newCredentials
    });
    return response.data;
  }
}

// Export default instance
export const accountsService = AccountsService;
