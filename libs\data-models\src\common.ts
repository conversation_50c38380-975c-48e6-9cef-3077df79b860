import { z } from 'zod';

// Common types and schemas
export const UUIDSchema = z.string().uuid();
export const DateSchema = z.string().datetime();
export const CurrencySchema = z.enum(['ZMW', 'USD']);
export const AmountSchema = z.number().positive().multipleOf(0.01);

export type UUID = z.infer<typeof UUIDSchema>;
export type Currency = z.infer<typeof CurrencySchema>;

// Base entity interface
export interface BaseEntity {
  id: UUID;
  createdAt: string;
  updatedAt: string;
}

// API response wrapper
export interface ApiResponse<T> {
  data: T;
  message?: string;
  timestamp: string;
}

// Error response
export interface ErrorResponse {
  error: string;
  message: string;
  details?: Array<{
    field: string;
    message: string;
  }>;
  timestamp: string;
  path: string;
}

// Pagination
export interface Pagination {
  limit: number;
  offset: number;
  total: number;
  hasNext: boolean;
  hasPrevious: boolean;
}

export interface PaginatedResponse<T> {
  items: T[];
  pagination: Pagination;
}

// Service health check
export interface ServiceHealthCheck {
  service: string;
  status: 'healthy' | 'unhealthy' | 'degraded';
  timestamp: string;
  version: string;
  uptime?: number;
  environment?: string;
}

// Service discovery
export interface ServiceDiscovery {
  services: {
    [serviceName: string]: {
      url: string;
      health: string;
      version: string;
    };
  };
}
