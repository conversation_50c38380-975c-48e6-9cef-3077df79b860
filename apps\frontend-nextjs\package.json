{"name": "@intellifin/frontend", "version": "1.0.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "type-check": "tsc --noEmit"}, "dependencies": {"@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-dialog": "^1.0.5", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-label": "^2.0.2", "@radix-ui/react-slot": "^1.0.2", "@radix-ui/react-tabs": "^1.0.4", "@stomp/stompjs": "^7.0.0", "@tanstack/react-query": "^5.0.0", "axios": "^1.11.0", "class-variance-authority": "^0.7.1", "clsx": "^2.0.0", "framer-motion": "^11.0.0", "lucide-react": "^0.290.0", "next": "^15.4.4", "react": "^19.1.0", "react-dom": "^19.1.0", "react-intersection-observer": "^9.5.3", "sockjs-client": "^1.6.1", "tailwind-merge": "^2.0.0", "tailwindcss-animate": "^1.0.7", "zustand": "^4.5.7"}, "devDependencies": {"@testing-library/jest-dom": "^6.1.0", "@testing-library/react": "^13.4.0", "@types/node": "^20.8.0", "@types/react": "^18.3.23", "@types/react-dom": "^18.3.7", "@types/sockjs-client": "^1.5.1", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "autoprefixer": "^10.4.16", "eslint": "^8.50.0", "eslint-config-next": "^15.4.4", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "postcss": "^8.4.31", "tailwindcss": "^3.3.5", "typescript": "^5.2.0"}, "engines": {"node": ">=18.0.0", "npm": ">=9.0.0"}}