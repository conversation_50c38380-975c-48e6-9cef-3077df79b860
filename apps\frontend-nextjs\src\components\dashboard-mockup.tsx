"use client"

import React from "react"
import { motion } from "framer-motion"
import { Card, CardContent } from "@/components/ui/card"
import {
  MessageSquare,
  Search,
  Bell,
  Settings,
  BarChart3,
  Receipt,
  CreditCard,
  FileText,
  TrendingUp,
  AlertCircle,
  CheckCircle,
  Clock,
  Send,
  Mic,
  User,
  Home,
  Wallet,
  PieChart
} from "lucide-react"

export function DashboardMockup() {
  return (
    <motion.div
      initial={{ opacity: 0, y: 50, scale: 0.95 }}
      animate={{ opacity: 1, y: 0, scale: 1 }}
      transition={{ duration: 1, delay: 0.8, ease: "easeOut" }}
      className="relative max-w-7xl mx-auto"
    >
      <div className="bg-white rounded-2xl shadow-2xl overflow-hidden border border-gray-200">
        {/* Browser Header */}
        <div className="flex items-center justify-between bg-gray-50 px-6 py-3 border-b">
          <div className="flex items-center space-x-3">
            <div className="flex space-x-2">
              <div className="w-3 h-3 bg-red-400 rounded-full"></div>
              <div className="w-3 h-3 bg-yellow-400 rounded-full"></div>
              <div className="w-3 h-3 bg-green-400 rounded-full"></div>
            </div>
            <div className="text-sm text-gray-500 ml-4">app.intellifin.com</div>
          </div>
          <div className="flex items-center space-x-2">
            <div className="w-4 h-4 text-gray-400">⚡</div>
            <div className="w-4 h-4 text-gray-400">🔒</div>
          </div>
        </div>

        {/* Top Notification Bar */}
        <div className="bg-gradient-to-r from-primary to-accent px-6 py-2 text-white text-sm flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <Bell className="w-4 h-4" />
            <span>New transaction: Airtel Money +K2,450 received</span>
          </div>
          <button className="text-white/80 hover:text-white">×</button>
        </div>

        {/* Main Dashboard Layout */}
        <div className="flex h-[600px]">
          {/* Left Sidebar - Navigation */}
          <div className="w-64 bg-gray-50 border-r border-gray-200 flex flex-col">
            {/* Logo */}
            <div className="p-6 border-b border-gray-200">
              <h2 className="text-xl font-bold text-gray-900">intellifin</h2>
            </div>

            {/* Navigation Menu */}
            <nav className="flex-1 p-4 space-y-2">
              <div className="flex items-center space-x-3 px-3 py-2 bg-primary/10 text-primary rounded-lg">
                <Home className="w-4 h-4" />
                <span className="text-sm font-medium">Dashboard</span>
              </div>
              <div className="flex items-center space-x-3 px-3 py-2 text-gray-600 hover:bg-gray-100 rounded-lg cursor-pointer">
                <CreditCard className="w-4 h-4" />
                <span className="text-sm">Transactions</span>
              </div>
              <div className="flex items-center space-x-3 px-3 py-2 text-gray-600 hover:bg-gray-100 rounded-lg cursor-pointer">
                <Receipt className="w-4 h-4" />
                <span className="text-sm">Invoices</span>
              </div>
              <div className="flex items-center space-x-3 px-3 py-2 text-gray-600 hover:bg-gray-100 rounded-lg cursor-pointer">
                <FileText className="w-4 h-4" />
                <span className="text-sm">Bills</span>
              </div>
              <div className="flex items-center space-x-3 px-3 py-2 text-gray-600 hover:bg-gray-100 rounded-lg cursor-pointer">
                <BarChart3 className="w-4 h-4" />
                <span className="text-sm">Reports</span>
              </div>
            </nav>

            {/* Key Business Vitals */}
            <div className="p-4 border-t border-gray-200">
              <h3 className="text-xs font-semibold text-gray-500 uppercase tracking-wide mb-3">Business Vitals</h3>
              <div className="space-y-3">
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-600">Cash Balance</span>
                  <span className="text-sm font-semibold text-secondary">K34,743</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-600">Due Bills</span>
                  <span className="text-sm font-semibold text-red-500">K2,450</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-600">Pending Invoices</span>
                  <span className="text-sm font-semibold text-highlight">K8,920</span>
                </div>
              </div>
            </div>

            {/* Bottom Menu */}
            <div className="p-4 border-t border-gray-200 space-y-2">
              <div className="flex items-center space-x-3 px-3 py-2 text-gray-600 hover:bg-gray-100 rounded-lg cursor-pointer">
                <Settings className="w-4 h-4" />
                <span className="text-sm">Settings</span>
              </div>
              <div className="flex items-center space-x-3 px-3 py-2 text-gray-600 hover:bg-gray-100 rounded-lg cursor-pointer">
                <User className="w-4 h-4" />
                <span className="text-sm">Profile</span>
              </div>
            </div>
          </div>

          {/* Main Workspace - Conversational AI Interface */}
          <div className="flex-1 flex flex-col bg-white">
            {/* Chat Header */}
            <div className="p-4 border-b border-gray-200 bg-gradient-to-r from-primary/5 to-accent/5">
              <div className="flex items-center space-x-3">
                <div className="w-10 h-10 bg-gradient-primary rounded-full flex items-center justify-center">
                  <MessageSquare className="w-5 h-5 text-white" />
                </div>
                <div>
                  <h3 className="font-semibold text-gray-900">Start talking to IntelliFin</h3>
                  <p className="text-sm text-gray-600">Your AI accounting assistant. Ask me anything about your business!</p>
                </div>
              </div>
            </div>

            {/* Chat Messages Area */}
            <div className="flex-1 p-6 space-y-4 overflow-y-auto">
              {/* AI Welcome Message */}
              <div className="flex items-start space-x-3">
                <div className="w-8 h-8 bg-gradient-primary rounded-full flex items-center justify-center flex-shrink-0">
                  <MessageSquare className="w-4 h-4 text-white" />
                </div>
                <div className="bg-gray-50 rounded-lg p-4 max-w-md">
                  <p className="text-gray-900">Hi Grace! 👋 I'm your AI accounting assistant. I can help you with transactions, invoices, reports, and more. What would you like to know about your business today?</p>
                </div>
              </div>

              {/* Sample User Query */}
              <div className="flex items-start space-x-3 justify-end">
                <div className="bg-gradient-primary text-white rounded-lg p-4 max-w-md">
                  <p>Show me my transactions from this week</p>
                </div>
                <div className="w-8 h-8 bg-gray-300 rounded-full flex items-center justify-center flex-shrink-0">
                  <User className="w-4 h-4 text-gray-600" />
                </div>
              </div>

              {/* AI Response with Data */}
              <div className="flex items-start space-x-3">
                <div className="w-8 h-8 bg-gradient-primary rounded-full flex items-center justify-center flex-shrink-0">
                  <MessageSquare className="w-4 h-4 text-white" />
                </div>
                <div className="bg-gray-50 rounded-lg p-4 max-w-2xl">
                  <p className="text-gray-900 mb-3">Here are your transactions from this week:</p>

                  {/* Transaction Table */}
                  <div className="bg-white rounded-lg border border-gray-200 overflow-hidden">
                    <div className="grid grid-cols-4 gap-4 p-3 bg-gray-50 text-sm font-medium text-gray-700">
                      <span>Date</span>
                      <span>Description</span>
                      <span>Type</span>
                      <span className="text-right">Amount</span>
                    </div>
                    <div className="divide-y divide-gray-100">
                      <div className="grid grid-cols-4 gap-4 p-3 text-sm">
                        <span className="text-gray-600">Oct 16</span>
                        <span>Airtel Money Payment</span>
                        <span className="text-secondary">Income</span>
                        <span className="text-right font-semibold text-secondary">+K2,450</span>
                      </div>
                      <div className="grid grid-cols-4 gap-4 p-3 text-sm">
                        <span className="text-gray-600">Oct 15</span>
                        <span>Office Supplies</span>
                        <span className="text-red-500">Expense</span>
                        <span className="text-right font-semibold text-red-500">-K450</span>
                      </div>
                      <div className="grid grid-cols-4 gap-4 p-3 text-sm">
                        <span className="text-gray-600">Oct 14</span>
                        <span>MTN Mobile Money</span>
                        <span className="text-secondary">Income</span>
                        <span className="text-right font-semibold text-secondary">+K1,890</span>
                      </div>
                    </div>
                  </div>

                  <div className="mt-3 flex space-x-2">
                    <button className="px-3 py-1 bg-primary text-white text-sm rounded-md hover:bg-primary/90">Export to Excel</button>
                    <button className="px-3 py-1 bg-gray-200 text-gray-700 text-sm rounded-md hover:bg-gray-300">Create Invoice</button>
                    <button className="px-3 py-1 bg-gray-200 text-gray-700 text-sm rounded-md hover:bg-gray-300">View Details</button>
                  </div>
                </div>
              </div>
            </div>

            {/* Chat Input */}
            <div className="p-4 border-t border-gray-200 bg-gray-50">
              <div className="flex items-center space-x-3">
                <div className="flex-1 relative">
                  <input
                    type="text"
                    placeholder="Ask me anything about your business..."
                    className="w-full px-4 py-3 pr-12 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary/20 focus:border-primary"
                  />
                  <button className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-primary">
                    <Mic className="w-5 h-5" />
                  </button>
                </div>
                <button className="px-4 py-3 bg-gradient-primary text-white rounded-lg hover:opacity-90 transition-opacity">
                  <Send className="w-5 h-5" />
                </button>
              </div>
            </div>
          </div>

          {/* Right Sidebar - Real-time Business Updates */}
          <div className="w-80 bg-gray-50 border-l border-gray-200 flex flex-col">
            {/* Header */}
            <div className="p-4 border-b border-gray-200">
              <div className="flex items-center justify-between">
                <h3 className="font-semibold text-gray-900">Business Updates</h3>
                <div className="flex items-center space-x-1">
                  <div className="w-2 h-2 bg-secondary rounded-full animate-pulse"></div>
                  <span className="text-xs text-gray-500">Live</span>
                </div>
              </div>
            </div>

            {/* Real-time Updates */}
            <div className="flex-1 p-4 space-y-4 overflow-y-auto">
              {/* Recent Transaction */}
              <div className="bg-white rounded-lg p-3 border border-gray-200 shadow-sm">
                <div className="flex items-start space-x-3">
                  <div className="w-8 h-8 bg-secondary/10 rounded-full flex items-center justify-center">
                    <TrendingUp className="w-4 h-4 text-secondary" />
                  </div>
                  <div className="flex-1">
                    <p className="text-sm font-medium text-gray-900">New Payment Received</p>
                    <p className="text-xs text-gray-600">Airtel Money +K2,450</p>
                    <p className="text-xs text-gray-500">2 minutes ago</p>
                  </div>
                </div>
              </div>

              {/* Upcoming Bill */}
              <div className="bg-white rounded-lg p-3 border border-gray-200 shadow-sm">
                <div className="flex items-start space-x-3">
                  <div className="w-8 h-8 bg-red-100 rounded-full flex items-center justify-center">
                    <AlertCircle className="w-4 h-4 text-red-500" />
                  </div>
                  <div className="flex-1">
                    <p className="text-sm font-medium text-gray-900">Bill Due Tomorrow</p>
                    <p className="text-xs text-gray-600">Office Rent - K3,500</p>
                    <p className="text-xs text-gray-500">Due: Oct 18, 2024</p>
                  </div>
                </div>
              </div>

              {/* Invoice Sent */}
              <div className="bg-white rounded-lg p-3 border border-gray-200 shadow-sm">
                <div className="flex items-start space-x-3">
                  <div className="w-8 h-8 bg-primary/10 rounded-full flex items-center justify-center">
                    <Receipt className="w-4 h-4 text-primary" />
                  </div>
                  <div className="flex-1">
                    <p className="text-sm font-medium text-gray-900">Invoice Sent</p>
                    <p className="text-xs text-gray-600">INV-001 to ABC Corp</p>
                    <p className="text-xs text-gray-500">15 minutes ago</p>
                  </div>
                </div>
              </div>

              {/* ZRA Compliance */}
              <div className="bg-white rounded-lg p-3 border border-gray-200 shadow-sm">
                <div className="flex items-start space-x-3">
                  <div className="w-8 h-8 bg-accent/10 rounded-full flex items-center justify-center">
                    <CheckCircle className="w-4 h-4 text-accent" />
                  </div>
                  <div className="flex-1">
                    <p className="text-sm font-medium text-gray-900">ZRA Report Ready</p>
                    <p className="text-xs text-gray-600">Monthly VAT return generated</p>
                    <p className="text-xs text-gray-500">1 hour ago</p>
                  </div>
                </div>
              </div>

              {/* Low Balance Warning */}
              <div className="bg-white rounded-lg p-3 border border-gray-200 shadow-sm">
                <div className="flex items-start space-x-3">
                  <div className="w-8 h-8 bg-highlight/10 rounded-full flex items-center justify-center">
                    <Wallet className="w-4 h-4 text-highlight" />
                  </div>
                  <div className="flex-1">
                    <p className="text-sm font-medium text-gray-900">Low Balance Alert</p>
                    <p className="text-xs text-gray-600">MTN Mobile Money: K150</p>
                    <p className="text-xs text-gray-500">3 hours ago</p>
                  </div>
                </div>
              </div>
            </div>

            {/* Quick Actions */}
            <div className="p-4 border-t border-gray-200">
              <h4 className="text-sm font-semibold text-gray-900 mb-3">Quick Actions</h4>
              <div className="grid grid-cols-2 gap-2">
                <button className="p-2 bg-primary text-white rounded-lg text-xs hover:bg-primary/90">
                  Create Invoice
                </button>
                <button className="p-2 bg-gray-200 text-gray-700 rounded-lg text-xs hover:bg-gray-300">
                  Add Expense
                </button>
                <button className="p-2 bg-secondary text-white rounded-lg text-xs hover:bg-secondary/90">
                  View Reports
                </button>
                <button className="p-2 bg-gray-200 text-gray-700 rounded-lg text-xs hover:bg-gray-300">
                  Pay Bills
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </motion.div>
  )
}
