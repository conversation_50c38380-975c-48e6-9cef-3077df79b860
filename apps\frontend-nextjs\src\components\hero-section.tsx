"use client"

import React from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { AnimatedSection } from "@/components/ui/animated-section"
import { FloatingShapes } from "@/components/ui/floating-shapes"
import { Sparkles } from "lucide-react"

export function HeroSection() {
  return (
    <section className="relative min-h-screen flex items-center justify-center overflow-hidden">
      <FloatingShapes />

      <div className="container mx-auto px-6 text-center relative z-10">
        <AnimatedSection>
          <div className="flex items-center justify-center mb-6">
            <div className="flex items-center space-x-2 bg-white/10 backdrop-blur-sm rounded-full px-4 py-2 border border-white/20">
              <Sparkles className="w-4 h-4 text-white" />
              <span className="text-sm font-medium text-white">Now that drives change</span>
            </div>
          </div>
        </AnimatedSection>

        <AnimatedSection delay={0.2}>
          <h1 className="text-5xl md:text-7xl font-bold text-white mb-8 leading-tight">
            Accounting, as easy as{" "}
            <br />
            <span className="bg-gradient-to-r from-accent to-highlight bg-clip-text text-transparent">a conversation</span>
          </h1>
        </AnimatedSection>

        <AnimatedSection delay={0.4}>
          <p className="text-xl md:text-2xl text-white/80 mb-12 max-w-3xl mx-auto">
            Unlock Your Business Potential with AI-Powered Mobile Money Accounting
          </p>
        </AnimatedSection>

        <AnimatedSection delay={0.6}>
          <Button
            size="lg"
            className="bg-white/20 hover:bg-white/30 text-white font-medium px-8 py-4 text-lg rounded-full border border-white/20 backdrop-blur-sm transition-all"
          >
            Request a Demo
          </Button>
        </AnimatedSection>
      </div>
    </section>
  )
}
