"use client"

import React from "react"
import Link from "next/link"
import { Button } from "@/components/ui/button"

export function Header() {
  return (
    <header className="relative z-50 w-full">
      <div className="container mx-auto flex h-20 items-center justify-between px-6">
        {/* Logo */}
        <Link href="/" className="flex items-center space-x-2">
          <span className="text-2xl font-bold text-white">intellifin</span>
        </Link>

        {/* Navigation */}
        <nav className="hidden md:flex items-center">
          <div className="flex items-center space-x-1 bg-white/10 backdrop-blur-sm rounded-full px-2 py-2">
            <Link
              href="#products"
              className="px-4 py-2 text-sm font-medium text-white/90 hover:text-white hover:bg-white/10 rounded-full transition-all"
            >
              Products
            </Link>
            <Link
              href="#solutions"
              className="px-4 py-2 text-sm font-medium text-white/90 hover:text-white hover:bg-white/10 rounded-full transition-all"
            >
              Solutions
            </Link>
            <Link
              href="#why-intellifin"
              className="px-4 py-2 text-sm font-medium text-white/90 hover:text-white hover:bg-white/10 rounded-full transition-all"
            >
              Why IntelliFin
            </Link>
            <Link
              href="#resources"
              className="px-4 py-2 text-sm font-medium text-white/90 hover:text-white hover:bg-white/10 rounded-full transition-all"
            >
              Resources
            </Link>
          </div>
        </nav>

        {/* CTA Button */}
        <Button
          className="bg-white/20 hover:bg-white/30 text-white font-medium px-6 py-2 rounded-full border border-white/20 backdrop-blur-sm transition-all"
          size="default"
        >
          Get Started
        </Button>
      </div>
    </header>
  )
}
