"use client"

import React, { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import { useAuthStore } from '@/stores';
import { ROUTES } from '@/utils/constants';
import { Loader2 } from 'lucide-react';

interface ProtectedRouteProps {
  children: React.ReactNode;
  requireAuth?: boolean;
  requireOnboarding?: boolean;
  redirectTo?: string;
}

/**
 * ProtectedRoute component that handles authentication and onboarding checks
 */
export function ProtectedRoute({
  children,
  requireAuth = true,
  requireOnboarding = true,
  redirectTo,
}: ProtectedRouteProps) {
  const router = useRouter();
  const { isAuthenticated, user, isLoading, initialize } = useAuthStore();
  const [isInitialized, setIsInitialized] = useState(false);

  useEffect(() => {
    const initAuth = async () => {
      if (!isInitialized) {
        await initialize();
        setIsInitialized(true);
      }
    };

    initAuth();
  }, [initialize, isInitialized]);

  useEffect(() => {
    if (!isInitialized || isLoading) {
      return; // Still loading, don't redirect yet
    }

    if (requireAuth && !isAuthenticated) {
      // User needs to be authenticated but isn't
      const redirectUrl = redirectTo || ROUTES.LOGIN;
      router.push(redirectUrl);
      return;
    }

    if (!requireAuth && isAuthenticated) {
      // User is authenticated but shouldn't be (e.g., on login page)
      const redirectUrl = redirectTo || ROUTES.DASHBOARD;
      router.push(redirectUrl);
      return;
    }

    if (
      requireAuth &&
      isAuthenticated &&
      requireOnboarding &&
      user?.onboardingStatus !== 'ALL_SET'
    ) {
      // User is authenticated but hasn't completed onboarding
      router.push(ROUTES.ONBOARDING);
      return;
    }
  }, [
    isInitialized,
    isLoading,
    isAuthenticated,
    user?.onboardingStatus,
    requireAuth,
    requireOnboarding,
    redirectTo,
    router,
  ]);

  // Show loading spinner while initializing or redirecting
  if (!isInitialized || isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="text-center">
          <Loader2 className="h-8 w-8 animate-spin text-primary mx-auto mb-4" />
          <p className="text-gray-600">Loading...</p>
        </div>
      </div>
    );
  }

  // Check if user should be redirected
  if (requireAuth && !isAuthenticated) {
    return null; // Will redirect via useEffect
  }

  if (!requireAuth && isAuthenticated) {
    return null; // Will redirect via useEffect
  }

  if (
    requireAuth &&
    isAuthenticated &&
    requireOnboarding &&
    user?.onboardingStatus !== 'ALL_SET'
  ) {
    return null; // Will redirect to onboarding
  }

  // All checks passed, render children
  return <>{children}</>;
}

/**
 * Hook for checking authentication status in components
 */
export function useAuthGuard() {
  const { isAuthenticated, user, isLoading } = useAuthStore();

  return {
    isAuthenticated,
    user,
    isLoading,
    isOnboarded: user?.onboardingStatus === 'ALL_SET',
    needsEmailVerification: user && !user.emailVerified,
  };
}

/**
 * Higher-order component for protecting routes
 */
export function withAuth<P extends object>(
  Component: React.ComponentType<P>,
  options: {
    requireAuth?: boolean;
    requireOnboarding?: boolean;
    redirectTo?: string;
  } = {}
) {
  const {
    requireAuth = true,
    requireOnboarding = true,
    redirectTo,
  } = options;

  return function AuthenticatedComponent(props: P) {
    return (
      <ProtectedRoute
        requireAuth={requireAuth}
        requireOnboarding={requireOnboarding}
        redirectTo={redirectTo}
      >
        <Component {...props} />
      </ProtectedRoute>
    );
  };
}

/**
 * Component for routes that should only be accessible to unauthenticated users
 */
export function PublicOnlyRoute({ children }: { children: React.ReactNode }) {
  return (
    <ProtectedRoute requireAuth={false} requireOnboarding={false}>
      {children}
    </ProtectedRoute>
  );
}

/**
 * Component for routes that require completed onboarding
 */
export function OnboardedRoute({ children }: { children: React.ReactNode }) {
  return (
    <ProtectedRoute requireAuth={true} requireOnboarding={true}>
      {children}
    </ProtectedRoute>
  );
}

/**
 * Component for routes that require authentication but not onboarding
 */
export function AuthenticatedRoute({ children }: { children: React.ReactNode }) {
  return (
    <ProtectedRoute requireAuth={true} requireOnboarding={false}>
      {children}
    </ProtectedRoute>
  );
}
