"use client"

import React, { useState } from 'react';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import { motion } from 'framer-motion';
import { Mail, ArrowRight, AlertCircle, CheckCircle, ArrowLeft } from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { useAuthStore } from '@/stores';
import { validateField, ValidationRule } from '@/utils/validation';
import { ROUTES } from '@/utils/constants';

interface ForgotPasswordForm {
  email: string;
}

interface FormErrors {
  email: string[];
  general: string[];
}

const validationRules: Record<keyof ForgotPasswordForm, ValidationRule> = {
  email: {
    required: true,
    email: true,
  },
};

export default function ForgotPasswordPage() {
  const router = useRouter();
  const { forgotPassword, isLoading, error, clearError } = useAuthStore();

  const [form, setForm] = useState<ForgotPasswordForm>({
    email: '',
  });

  const [errors, setErrors] = useState<FormErrors>({
    email: [],
    general: [],
  });

  const [isSubmitted, setIsSubmitted] = useState(false);

  const handleInputChange = (field: keyof ForgotPasswordForm, value: string) => {
    setForm(prev => ({ ...prev, [field]: value }));
    
    // Clear field errors when user starts typing
    if (errors[field].length > 0) {
      setErrors(prev => ({ ...prev, [field]: [] }));
    }
  };

  const handleBlur = (field: keyof ForgotPasswordForm) => {
    const fieldErrors = validateField(form[field], validationRules[field]);
    setErrors(prev => ({ ...prev, [field]: fieldErrors }));
  };

  const validateForm = (): boolean => {
    const newErrors: FormErrors = {
      email: [],
      general: [],
    };

    // Validate each field
    Object.keys(validationRules).forEach(field => {
      const fieldKey = field as keyof ForgotPasswordForm;
      const fieldErrors = validateField(form[fieldKey], validationRules[fieldKey]);
      newErrors[fieldKey] = fieldErrors;
    });

    setErrors(newErrors);

    // Check if there are any errors
    return Object.values(newErrors).every(fieldErrors => fieldErrors.length === 0);
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    clearError();

    try {
      await forgotPassword(form.email);
      setIsSubmitted(true);
    } catch (error: any) {
      // Error is handled by the store and displayed via the error state
      console.error('Forgot password failed:', error);
    }
  };

  const getFieldError = (field: keyof FormErrors): string | null => {
    if (errors[field].length > 0) {
      return errors[field][0];
    }
    return null;
  };

  const hasFieldError = (field: keyof FormErrors): boolean => {
    return errors[field].length > 0;
  };

  if (isSubmitted) {
    return (
      <div className="min-h-screen bg-white flex items-center justify-center p-4">
        <motion.div
          initial={{ opacity: 0, scale: 0.95 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ duration: 0.5 }}
          className="w-full max-w-md text-center"
        >
          <div className="mx-auto w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mb-6">
            <CheckCircle className="h-8 w-8 text-green-600" />
          </div>
          <h1 className="text-2xl font-bold text-gray-900 mb-2">Check your email</h1>
          <p className="text-gray-600 mb-6">
            We've sent a password reset link to <strong>{form.email}</strong>
          </p>
          <div className="space-y-3">
            <Button
              onClick={() => setIsSubmitted(false)}
              variant="outline"
              className="w-full"
            >
              Send another email
            </Button>
            <Link href={ROUTES.LOGIN}>
              <Button variant="ghost" className="w-full">
                <ArrowLeft className="h-4 w-4 mr-2" />
                Back to login
              </Button>
            </Link>
          </div>
        </motion.div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-white">
      {/* Desktop Split Screen Layout */}
      <div className="hidden lg:flex h-screen">
        {/* Left Side - Form */}
        <div className="flex-1 flex items-center justify-center p-8">
          <motion.div
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.6 }}
            className="w-full max-w-md"
          >
            {/* Logo */}
            <div className="flex items-center mb-8">
              <div className="w-8 h-8 bg-gradient-primary rounded-lg flex items-center justify-center mr-3">
                <span className="text-white font-bold text-lg">I</span>
              </div>
              <span className="text-xl font-bold text-gray-900">IntelliFin</span>
            </div>

            {/* Form Header */}
            <div className="mb-8">
              <h1 className="text-3xl font-bold text-gray-900 mb-2">Reset Your Password</h1>
              <p className="text-gray-600">We'll send you a reset link to your email.</p>
            </div>

            {/* Error Alert */}
            {error && (
              <Alert variant="destructive" className="mb-6">
                <AlertCircle className="h-4 w-4" />
                <AlertDescription>{error}</AlertDescription>
              </Alert>
            )}

            {/* Form */}
            <form onSubmit={handleSubmit} className="space-y-5">
              {/* Email */}
              <div className="space-y-2">
                <label htmlFor="email" className="text-sm font-medium text-gray-700">
                  Email or Username
                </label>
                <div className="relative">
                  <Mail className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                  <Input
                    id="email"
                    type="email"
                    placeholder="<EMAIL>"
                    value={form.email}
                    onChange={(e) => handleInputChange('email', e.target.value)}
                    onBlur={() => handleBlur('email')}
                    className={`pl-10 ${hasFieldError('email') ? 'border-red-500 focus:border-red-500' : ''}`}
                    disabled={isLoading}
                  />
                </div>
                {getFieldError('email') && (
                  <p className="text-sm text-red-600">{getFieldError('email')}</p>
                )}
              </div>

              <Button
                type="submit"
                className="w-full h-12 bg-gradient-primary hover:opacity-90 transition-opacity text-white font-medium"
                disabled={isLoading}
              >
                {isLoading ? (
                  <div className="flex items-center space-x-2">
                    <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
                    <span>Sending...</span>
                  </div>
                ) : (
                  <span>Continue</span>
                )}
              </Button>
            </form>

            {/* Back to Login */}
            <div className="text-center mt-6">
              <Link
                href={ROUTES.LOGIN}
                className="text-sm text-gray-600 hover:text-gray-900 font-medium inline-flex items-center"
              >
                <ArrowLeft className="h-4 w-4 mr-1" />
                Already have an account? Log In
              </Link>
            </div>
          </motion.div>
        </div>

        {/* Right Side - Value Proposition */}
        <div className="flex-1 bg-gradient-to-br from-primary to-primary-dark flex items-center justify-center p-8">
          <motion.div
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.6, delay: 0.2 }}
            className="text-white max-w-lg"
          >
            <h2 className="text-4xl font-bold mb-4">
              Secure password recovery made simple.
            </h2>
            <p className="text-xl text-blue-100 mb-8">
              Get back to managing your finances quickly and securely with our streamlined password reset process.
            </p>

            {/* Security Features */}
            <div className="space-y-4">
              <div className="flex items-center space-x-3">
                <div className="w-8 h-8 bg-white/20 rounded-lg flex items-center justify-center">
                  <CheckCircle className="h-5 w-5" />
                </div>
                <span className="text-blue-100">Secure email verification</span>
              </div>
              <div className="flex items-center space-x-3">
                <div className="w-8 h-8 bg-white/20 rounded-lg flex items-center justify-center">
                  <CheckCircle className="h-5 w-5" />
                </div>
                <span className="text-blue-100">Encrypted password reset links</span>
              </div>
              <div className="flex items-center space-x-3">
                <div className="w-8 h-8 bg-white/20 rounded-lg flex items-center justify-center">
                  <CheckCircle className="h-5 w-5" />
                </div>
                <span className="text-blue-100">Quick account recovery</span>
              </div>
            </div>
          </motion.div>
        </div>
      </div>

      {/* Mobile Layout */}
      <div className="lg:hidden min-h-screen bg-gray-50 flex items-center justify-center p-4">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
          className="w-full max-w-sm"
        >
          <Card className="shadow-lg border-0 bg-white">
            <CardHeader className="space-y-1 text-center pb-6">
              <div className="mx-auto w-12 h-12 bg-gradient-primary rounded-full flex items-center justify-center mb-4">
                <span className="text-white font-bold text-xl">I</span>
              </div>
              <CardTitle className="text-2xl font-bold">Reset Your Password</CardTitle>
              <CardDescription>
                We'll send you a reset link to your email
              </CardDescription>
            </CardHeader>
            
            <CardContent className="space-y-4">
              {error && (
                <Alert variant="destructive">
                  <AlertCircle className="h-4 w-4" />
                  <AlertDescription>{error}</AlertDescription>
                </Alert>
              )}
              
              <form onSubmit={handleSubmit} className="space-y-4">
                {/* Email */}
                <div className="space-y-2">
                  <label htmlFor="email-mobile" className="text-sm font-medium text-gray-700">
                    Email or Username
                  </label>
                  <div className="relative">
                    <Mail className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                    <Input
                      id="email-mobile"
                      type="email"
                      placeholder="<EMAIL>"
                      value={form.email}
                      onChange={(e) => handleInputChange('email', e.target.value)}
                      onBlur={() => handleBlur('email')}
                      className={`pl-10 ${hasFieldError('email') ? 'border-red-500 focus:border-red-500' : ''}`}
                      disabled={isLoading}
                    />
                  </div>
                  {getFieldError('email') && (
                    <p className="text-sm text-red-600">{getFieldError('email')}</p>
                  )}
                </div>

                <Button
                  type="submit"
                  className="w-full h-12 bg-gradient-primary hover:opacity-90 transition-opacity text-white font-medium"
                  disabled={isLoading}
                >
                  {isLoading ? (
                    <div className="flex items-center space-x-2">
                      <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
                      <span>Sending...</span>
                    </div>
                  ) : (
                    <span>Continue</span>
                  )}
                </Button>
              </form>

              {/* Back to Login */}
              <div className="text-center mt-6">
                <Link
                  href={ROUTES.LOGIN}
                  className="text-sm text-gray-600 hover:text-gray-900 font-medium inline-flex items-center"
                >
                  <ArrowLeft className="h-4 w-4 mr-1" />
                  Already have an account? Log In
                </Link>
              </div>
            </CardContent>
          </Card>
        </motion.div>
      </div>
    </div>
  );
}
