import { useEffect, useRef, useState, useCallback } from 'react';
import { Client, IMessage } from '@stomp/stompjs';
import SockJS from 'sockjs-client';
import { useAuth } from '@/contexts/AuthContext';

interface WebSocketMessage {
  messageId?: string;
  sessionId?: string;
  conversationId?: string;
  content: string;
  type: 'COMMAND' | 'RESPONSE' | 'ERROR' | 'PROGRESS' | 'SYSTEM';
  role: 'USER' | 'ASSISTANT' | 'SYSTEM';
  status: 'PENDING' | 'PROCESSING' | 'COMPLETED' | 'FAILED';
  intentRecognized?: string;
  confidenceScore?: number;
  processingTimeMs?: number;
  errorMessage?: string;
  timestamp: string;
  isComplete: boolean;
  requiresFollowUp?: boolean;
  followUpPrompt?: string;
}

interface UseWebSocketReturn {
  isConnected: boolean;
  sendCommand: (command: string, sessionId?: string) => void;
  sendTyping: (isTyping: boolean, sessionId?: string) => void;
  messages: WebSocketMessage[];
  clearMessages: () => void;
  connectionError: string | null;
  isConnecting: boolean;
}

const API_BASE_URL = process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:8080';

export function useWebSocket(): UseWebSocketReturn {
  const { token, isAuthenticated } = useAuth();
  const [isConnected, setIsConnected] = useState(false);
  const [isConnecting, setIsConnecting] = useState(false);
  const [messages, setMessages] = useState<WebSocketMessage[]>([]);
  const [connectionError, setConnectionError] = useState<string | null>(null);
  
  const clientRef = useRef<Client | null>(null);
  const reconnectTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const reconnectAttempts = useRef(0);
  const maxReconnectAttempts = 5;

  const connect = useCallback(() => {
    if (!isAuthenticated || !token || clientRef.current?.connected) {
      return;
    }

    setIsConnecting(true);
    setConnectionError(null);

    try {
      const client = new Client({
        webSocketFactory: () => new SockJS(`${API_BASE_URL}/ws/conversation`),
        connectHeaders: {
          Authorization: `Bearer ${token}`,
        },
        debug: (str) => {
          console.log('STOMP Debug:', str);
        },
        reconnectDelay: 5000,
        heartbeatIncoming: 4000,
        heartbeatOutgoing: 4000,
        onConnect: (frame) => {
          console.log('WebSocket connected:', frame);
          setIsConnected(true);
          setIsConnecting(false);
          setConnectionError(null);
          reconnectAttempts.current = 0;

          // Subscribe to conversation messages
          client.subscribe('/user/queue/conversation', (message: IMessage) => {
            try {
              const messageData: WebSocketMessage = JSON.parse(message.body);
              console.log('Received WebSocket message:', messageData);
              
              setMessages(prev => [...prev, messageData]);
            } catch (error) {
              console.error('Error parsing WebSocket message:', error);
            }
          });

          // Subscribe to errors
          client.subscribe('/user/queue/errors', (message: IMessage) => {
            try {
              const errorData = JSON.parse(message.body);
              console.error('WebSocket error:', errorData);
              setConnectionError(errorData.message || 'WebSocket error occurred');
            } catch (error) {
              console.error('Error parsing WebSocket error message:', error);
            }
          });
        },
        onStompError: (frame) => {
          console.error('STOMP error:', frame);
          setConnectionError(`Connection error: ${frame.headers['message'] || 'Unknown error'}`);
          setIsConnected(false);
          setIsConnecting(false);
        },
        onWebSocketError: (error) => {
          console.error('WebSocket error:', error);
          setConnectionError('WebSocket connection failed');
          setIsConnected(false);
          setIsConnecting(false);
        },
        onDisconnect: (frame) => {
          console.log('WebSocket disconnected:', frame);
          setIsConnected(false);
          setIsConnecting(false);
          
          // Attempt to reconnect if not manually disconnected
          if (reconnectAttempts.current < maxReconnectAttempts) {
            reconnectAttempts.current++;
            const delay = Math.min(1000 * Math.pow(2, reconnectAttempts.current), 30000);
            
            reconnectTimeoutRef.current = setTimeout(() => {
              console.log(`Attempting to reconnect (${reconnectAttempts.current}/${maxReconnectAttempts})...`);
              connect();
            }, delay);
          } else {
            setConnectionError('Maximum reconnection attempts reached');
          }
        },
      });

      clientRef.current = client;
      client.activate();
    } catch (error) {
      console.error('Error creating WebSocket connection:', error);
      setConnectionError('Failed to create WebSocket connection');
      setIsConnecting(false);
    }
  }, [isAuthenticated, token]);

  const disconnect = useCallback(() => {
    if (reconnectTimeoutRef.current) {
      clearTimeout(reconnectTimeoutRef.current);
      reconnectTimeoutRef.current = null;
    }

    if (clientRef.current) {
      clientRef.current.deactivate();
      clientRef.current = null;
    }

    setIsConnected(false);
    setIsConnecting(false);
    reconnectAttempts.current = 0;
  }, []);

  const sendCommand = useCallback((command: string, sessionId?: string) => {
    if (!clientRef.current?.connected) {
      console.error('WebSocket not connected');
      setConnectionError('Not connected to server');
      return;
    }

    try {
      const message = {
        command,
        sessionId,
        expectResponse: true,
        saveToHistory: true,
      };

      clientRef.current.publish({
        destination: '/app/conversation/command',
        body: JSON.stringify(message),
      });

      console.log('Sent command:', command);
    } catch (error) {
      console.error('Error sending command:', error);
      setConnectionError('Failed to send command');
    }
  }, []);

  const sendTyping = useCallback((isTyping: boolean, sessionId?: string) => {
    if (!clientRef.current?.connected) {
      return;
    }

    try {
      const message = {
        typing: isTyping,
        sessionId,
      };

      clientRef.current.publish({
        destination: '/app/conversation/typing',
        body: JSON.stringify(message),
      });
    } catch (error) {
      console.error('Error sending typing indicator:', error);
    }
  }, []);

  const clearMessages = useCallback(() => {
    setMessages([]);
  }, []);

  // Connect when authenticated
  useEffect(() => {
    if (isAuthenticated && token) {
      connect();
    } else {
      disconnect();
    }

    return () => {
      disconnect();
    };
  }, [isAuthenticated, token, connect, disconnect]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      disconnect();
    };
  }, [disconnect]);

  return {
    isConnected,
    sendCommand,
    sendTyping,
    messages,
    clearMessages,
    connectionError,
    isConnecting,
  };
}
