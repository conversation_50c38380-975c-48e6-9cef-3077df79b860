# IntelliFin Development Setup Guide

## Overview

This guide provides step-by-step instructions for setting up the IntelliFin development environment on your local machine.

## 1. Prerequisites

### 1.1 Required Software

Install the following software on your development machine:

```bash
# Docker and Docker Compose
# Download from: https://docs.docker.com/get-docker/
docker --version  # Should be 24.0+
docker-compose --version  # Should be 2.0+

# Node.js (for frontend development)
# Download from: https://nodejs.org/
node --version  # Should be 18+
npm --version   # Should be 9+

# Java (for backend development)
# Download from: https://adoptium.net/
java --version  # Should be 17+

# Python (for AI service development)
# Download from: https://python.org/
python --version  # Should be 3.10+
pip --version

# Git
git --version  # Should be 2.30+
```

### 1.2 IDE Recommendations

**For Java Development:**
- IntelliJ IDEA Community Edition (recommended)
- VS Code with Java Extension Pack

**For Python Development:**
- PyCharm Community Edition (recommended)
- VS Code with Python Extension

**For Frontend Development:**
- VS Code (recommended)
- WebStorm

## 2. Project Setup

### 2.1 Clone Repository

```bash
# Clone the repository
git clone https://github.com/your-org/intellifin.git
cd intellifin

# Verify project structure
ls -la
# Should see: apps/, docs/, docker-compose.yml, .env.example, etc.
```

### 2.2 Environment Configuration

```bash
# Copy environment template
cp .env.example .env

# Edit environment variables
nano .env  # or use your preferred editor
```

**Environment Variables (.env):**

```bash
# Database Configuration
DATABASE_URL=**********************************************
DATABASE_USERNAME=intellifin_user
DATABASE_PASSWORD=intellifin_dev_password

# Redis Configuration
REDIS_URL=redis://redis:6379

# JWT Configuration (Generate a secure key for development)
JWT_SECRET=your-local-jwt-secret-key-min-256-bits-change-this
JWT_EXPIRATION=86400

# AI Service Configuration
OLLAMA_BASE_URL=http://ollama:11434
OPENAI_API_KEY=your-openai-api-key-for-embeddings
GOOGLE_API_KEY=your-google-api-key-for-gemini

# External API Configuration (Development/Sandbox)
MTN_API_BASE_URL=https://sandbox.mtn.com/api
MTN_API_KEY=sandbox-key-get-from-mtn-developer-portal
ZRA_API_BASE_URL=https://sandbox.zra.org.zm/api
ZRA_API_KEY=sandbox-key-get-from-zra

# Frontend Configuration
NEXT_PUBLIC_API_BASE_URL=http://localhost:8080
NEXT_PUBLIC_WS_URL=ws://localhost:8080
NEXT_PUBLIC_AI_SERVICE_URL=http://localhost:8000

# Logging
LOG_LEVEL=DEBUG
```

### 2.3 Start Development Environment

```bash
# Start all services with Docker Compose
docker-compose up -d

# Verify all services are running
docker-compose ps

# Expected output:
# NAME                     STATUS
# intellifin-postgres      Up (healthy)
# intellifin-redis         Up (healthy)
# intellifin-ollama        Up (healthy)
# intellifin-backend-core  Up (healthy)
# intellifin-ai-service    Up (healthy)
# intellifin-zra-service   Up (healthy)
# intellifin-frontend      Up (healthy)
```

### 2.4 Initialize Database

```bash
# Run database migrations
docker-compose exec postgres psql -U intellifin_user -d intellifin_dev -f /docker-entrypoint-initdb.d/001_initial_schema.sql
docker-compose exec postgres psql -U intellifin_user -d intellifin_dev -f /docker-entrypoint-initdb.d/002_seed_data.sql

# Verify database setup
docker-compose exec postgres psql -U intellifin_user -d intellifin_dev -c "\dt"
# Should show all tables: users, transactions, categories, etc.
```

### 2.5 Setup AI Models

```bash
# Pull Ollama model for local AI development
docker-compose exec ollama ollama pull llama3.1:8b

# Verify model is available
docker-compose exec ollama ollama list
# Should show: llama3.1:8b
```

## 3. Service-Specific Setup

### 3.1 Backend Core (Java Spring Boot)

```bash
# Navigate to backend directory
cd apps/backend-java-core

# Install dependencies and run tests
./mvnw clean install

# Run application locally (outside Docker for debugging)
./mvnw spring-boot:run -Dspring-boot.run.profiles=development

# Or run with debug mode
./mvnw spring-boot:run -Dspring-boot.run.jvmArguments="-agentlib:jdwp=transport=dt_socket,server=y,suspend=n,address=*:5005"
```

**IDE Setup for Java:**

1. Import project as Maven project
2. Set JDK to version 17
3. Configure run configuration:
   - Main class: `com.intellifin.IntelliFin Application`
   - VM options: `-Dspring.profiles.active=development`
   - Environment variables: Load from `.env` file

### 3.2 AI Service (Python FastAPI)

```bash
# Navigate to AI service directory
cd apps/backend-python-ai

# Create virtual environment
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate

# Install dependencies
pip install -r requirements.txt

# Run application locally
uvicorn src.main:app --reload --host 0.0.0.0 --port 8000

# Run tests
pytest

# Run with coverage
pytest --cov=src --cov-report=html
```

**IDE Setup for Python:**

1. Set Python interpreter to virtual environment
2. Configure run configuration:
   - Script path: `uvicorn`
   - Parameters: `src.main:app --reload --host 0.0.0.0 --port 8000`
   - Working directory: `apps/backend-python-ai`

### 3.3 Frontend (Next.js)

```bash
# Navigate to frontend directory
cd apps/frontend-nextjs

# Install dependencies
npm install

# Run development server
npm run dev

# Run tests
npm test

# Run tests with coverage
npm run test:coverage

# Build for production (to test)
npm run build
```

**IDE Setup for Frontend:**

1. Install recommended VS Code extensions:
   - ES7+ React/Redux/React-Native snippets
   - Tailwind CSS IntelliSense
   - TypeScript Importer
   - Prettier - Code formatter

2. Configure VS Code settings (`.vscode/settings.json`):
```json
{
  "typescript.preferences.importModuleSpecifier": "relative",
  "editor.formatOnSave": true,
  "editor.defaultFormatter": "esbenp.prettier-vscode",
  "tailwindCSS.experimental.classRegex": [
    ["cva\\(([^)]*)\\)", "[\"'`]([^\"'`]*).*?[\"'`]"]
  ]
}
```

## 4. Development Workflow

### 4.1 Daily Development Routine

```bash
# 1. Start development environment
docker-compose up -d

# 2. Check service health
./scripts/health-check.sh

# 3. Pull latest changes
git pull origin develop

# 4. Update dependencies if needed
cd apps/backend-java-core && ./mvnw clean install
cd ../backend-python-ai && pip install -r requirements.txt
cd ../frontend-nextjs && npm install

# 5. Run tests before starting work
npm run test:all  # Runs tests for all services
```

### 4.2 Making Changes

```bash
# 1. Create feature branch
git checkout -b feature/your-feature-name

# 2. Make changes and test locally
# ... develop your feature ...

# 3. Run comprehensive tests
npm run test:all
npm run test:integration
npm run test:e2e

# 4. Commit changes
git add .
git commit -m "feat: add your feature description"

# 5. Push and create pull request
git push origin feature/your-feature-name
```

### 4.3 Debugging

**Backend Debugging:**
```bash
# Attach debugger to Java service (port 5005)
# In IntelliJ: Run > Attach to Process > localhost:5005

# View logs
docker-compose logs -f backend-core
docker-compose logs -f ai-service
```

**Frontend Debugging:**
```bash
# Use browser dev tools
# React DevTools extension recommended

# View Next.js logs
npm run dev  # Shows detailed logs in terminal
```

**Database Debugging:**
```bash
# Connect to PostgreSQL
docker-compose exec postgres psql -U intellifin_user -d intellifin_dev

# Or use pgAdmin (available at http://localhost:5050)
# Email: <EMAIL>
# Password: admin123
```

## 5. Testing

### 5.1 Running Tests

```bash
# Run all tests
npm run test:all

# Run specific test suites
cd apps/backend-java-core && ./mvnw test
cd apps/backend-python-ai && pytest
cd apps/frontend-nextjs && npm test

# Run integration tests
docker-compose -f docker-compose.test.yml up --abort-on-container-exit

# Run E2E tests
cd apps/frontend-nextjs && npm run test:e2e
```

### 5.2 Test Data

```bash
# Reset test database
docker-compose exec postgres psql -U intellifin_user -d intellifin_dev -c "TRUNCATE TABLE transactions, categories, users CASCADE;"

# Load test data
./scripts/load-test-data.sh
```

## 6. Troubleshooting

### 6.1 Common Issues

**Docker Issues:**
```bash
# Services not starting
docker-compose down
docker-compose up -d --force-recreate

# Port conflicts
docker-compose down
# Change ports in docker-compose.yml if needed
docker-compose up -d

# Volume issues
docker-compose down -v
docker-compose up -d
```

**Database Issues:**
```bash
# Connection refused
docker-compose restart postgres
# Wait for health check to pass

# Migration errors
docker-compose exec postgres psql -U intellifin_user -d intellifin_dev -c "DROP SCHEMA public CASCADE; CREATE SCHEMA public;"
# Re-run migrations
```

**AI Service Issues:**
```bash
# Ollama model not found
docker-compose exec ollama ollama pull llama3.1:8b

# Python dependencies
cd apps/backend-python-ai
pip install -r requirements.txt --force-reinstall
```

### 6.2 Getting Help

1. Check service logs: `docker-compose logs [service-name]`
2. Verify environment variables: `docker-compose config`
3. Check service health: `./scripts/health-check.sh`
4. Consult documentation in `/docs` directory
5. Ask team members or create GitHub issue

## 7. Performance Optimization

### 7.1 Development Performance

```bash
# Use Docker BuildKit for faster builds
export DOCKER_BUILDKIT=1

# Use npm cache
npm config set cache ~/.npm-cache

# Use Maven cache
# Already configured in docker-compose.yml volumes
```

This setup guide provides everything needed to get the IntelliFin development environment running smoothly on any developer's machine.
