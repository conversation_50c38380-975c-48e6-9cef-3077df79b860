spring:
  application:
    name: intellifin-backend-core
  
  profiles:
    active: ${SPRING_PROFILES_ACTIVE:development}
  
  datasource:
    url: ${DATABASE_URL:***********************************************}
    username: ${DATABASE_USERNAME:postgres}
    password: ${DATABASE_PASSWORD:postgres}
    driver-class-name: org.postgresql.Driver
    hikari:
      maximum-pool-size: 10
      minimum-idle: 2
      connection-timeout: 30000
      idle-timeout: 600000
      max-lifetime: 1800000
  
  jpa:
    hibernate:
      ddl-auto: update
    show-sql: false
    properties:
      hibernate:
        dialect: org.hibernate.dialect.PostgreSQLDialect
        format_sql: true
        use_sql_comments: true
  
  data:
    redis:
      url: ${REDIS_URL:redis://localhost:6379}
      timeout: 2000ms
      lettuce:
        pool:
          max-active: 8
          max-idle: 8
          min-idle: 0
  
  flyway:
    enabled: true
    locations: classpath:db/migration
    baseline-on-migrate: true

server:
  port: 8080
  servlet:
    context-path: /
  error:
    include-message: always
    include-binding-errors: always

management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,prometheus
  endpoint:
    health:
      show-details: always
  health:
    redis:
      enabled: true
    db:
      enabled: true

logging:
  level:
    com.intellifin: ${LOG_LEVEL:INFO}
    org.springframework.web: ${LOG_LEVEL:INFO}
    org.hibernate.SQL: ${LOG_LEVEL:INFO}
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"

# JWT Configuration
jwt:
  secret: ${JWT_SECRET:intellifin-super-secure-jwt-secret-key-for-development-environment-2024}
  expiration: ${JWT_EXPIRATION:86400}

# Application Configuration
app:
  frontend:
    url: ${FRONTEND_URL:http://localhost:3000}
  email:
    enabled: ${EMAIL_ENABLED:false}
    from: ${EMAIL_FROM:<EMAIL>}
    smtp:
      host: ${SMTP_HOST:localhost}
      port: ${SMTP_PORT:587}
      username: ${SMTP_USERNAME:}
      password: ${SMTP_PASSWORD:}
      auth: ${SMTP_AUTH:true}
      starttls: ${SMTP_STARTTLS:true}

# OAuth Configuration
oauth:
  google:
    client-id: ${GOOGLE_CLIENT_ID:}
    client-secret: ${GOOGLE_CLIENT_SECRET:}
    redirect-uri: ${GOOGLE_REDIRECT_URI:http://localhost:8080/api/v1/auth/oauth/google/callback}
  apple:
    client-id: ${APPLE_CLIENT_ID:}
    client-secret: ${APPLE_CLIENT_SECRET:}
    redirect-uri: ${APPLE_REDIRECT_URI:http://localhost:8080/api/v1/auth/oauth/apple/callback}

# External Services
external:
  ai-service:
    url: ${AI_SERVICE_URL:http://localhost:8000}
    timeout: 30
  zra-service:
    url: ${ZRA_SERVICE_URL:http://localhost:8081}
    timeout: 30

---
# Development Profile
spring:
  config:
    activate:
      on-profile: development
  
  devtools:
    restart:
      enabled: true
    livereload:
      enabled: true
  
  jpa:
    show-sql: true
    properties:
      hibernate:
        format_sql: true

logging:
  level:
    com.intellifin: DEBUG
    org.springframework.web: DEBUG
    org.hibernate.SQL: DEBUG

---
# Production Profile
spring:
  config:
    activate:
      on-profile: production
  
  jpa:
    show-sql: false
  
  datasource:
    hikari:
      maximum-pool-size: 20
      minimum-idle: 5

logging:
  level:
    com.intellifin: INFO
    org.springframework.web: WARN
    org.hibernate.SQL: WARN
