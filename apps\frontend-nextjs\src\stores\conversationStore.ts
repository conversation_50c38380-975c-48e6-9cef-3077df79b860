import { create } from 'zustand';
import { devtools, subscribeWithSelector } from 'zustand/middleware';
import { 
  DisplayMessage, 
  ConversationState, 
  WebSocketMessage, 
  CommandSuggestion,
  ConnectionState 
} from '@/types/conversation';

interface ConversationStore extends ConversationState {
  // WebSocket connection
  connectionState: ConnectionState;
  reconnectAttempts: number;
  
  // Actions
  addMessage: (message: DisplayMessage) => void;
  updateMessage: (messageId: string, updates: Partial<DisplayMessage>) => void;
  removeMessage: (messageId: string) => void;
  clearMessages: () => void;
  
  // WebSocket actions
  setConnectionState: (state: ConnectionState) => void;
  setConnectionError: (error: string | null) => void;
  incrementReconnectAttempts: () => void;
  resetReconnectAttempts: () => void;
  
  // Typing and activity
  setTyping: (isTyping: boolean) => void;
  setAwaitingResponse: (awaiting: boolean) => void;
  updateLastActivity: () => void;
  
  // Session management
  setSessionId: (sessionId: string | null) => void;
  generateNewSession: () => string;
  
  // Message processing
  processWebSocketMessage: (wsMessage: WebSocketMessage) => void;
  addUserMessage: (content: string) => DisplayMessage;
  
  // Suggestions and commands
  getCommandSuggestions: () => CommandSuggestion[];
  
  // Utilities
  getLastMessage: () => DisplayMessage | null;
  getMessageById: (id: string) => DisplayMessage | null;
  getUnreadCount: () => number;
  markAllAsRead: () => void;
}

const defaultSuggestions: CommandSuggestion[] = [
  {
    text: "What's my account balance?",
    description: "Check current balance across all accounts",
    category: 'ACCOUNT',
    icon: '💰'
  },
  {
    text: "Show me recent transactions",
    description: "View latest transaction activity",
    category: 'TRANSACTION',
    icon: '📊'
  },
  {
    text: "Create a new invoice",
    description: "Generate an invoice for a client",
    category: 'INVOICE',
    icon: '📄'
  },
  {
    text: "Help me categorize expenses",
    description: "Review and categorize pending transactions",
    category: 'TRANSACTION',
    icon: '🏷️'
  },
  {
    text: "Show my profit this month",
    description: "View monthly financial summary",
    category: 'REPORT',
    icon: '📈'
  },
  {
    text: "What can you help me with?",
    description: "Learn about available features",
    category: 'GENERAL',
    icon: '❓'
  }
];

export const useConversationStore = create<ConversationStore>()(
  devtools(
    subscribeWithSelector((set, get) => ({
      // Initial state
      messages: [],
      isConnected: false,
      isConnecting: false,
      connectionError: null,
      currentSessionId: null,
      isTyping: false,
      awaitingResponse: false,
      lastActivity: null,
      connectionState: 'DISCONNECTED',
      reconnectAttempts: 0,

      // Actions
      addMessage: (message: DisplayMessage) => {
        set((state) => ({
          messages: [...state.messages, message],
          lastActivity: new Date(),
        }));
      },

      updateMessage: (messageId: string, updates: Partial<DisplayMessage>) => {
        set((state) => ({
          messages: state.messages.map((msg) =>
            msg.id === messageId ? { ...msg, ...updates } : msg
          ),
        }));
      },

      removeMessage: (messageId: string) => {
        set((state) => ({
          messages: state.messages.filter((msg) => msg.id !== messageId),
        }));
      },

      clearMessages: () => {
        set({
          messages: [],
          lastActivity: new Date(),
        });
      },

      // WebSocket actions
      setConnectionState: (connectionState: ConnectionState) => {
        set({
          connectionState,
          isConnected: connectionState === 'CONNECTED',
          isConnecting: connectionState === 'CONNECTING' || connectionState === 'RECONNECTING',
        });
      },

      setConnectionError: (error: string | null) => {
        set({ connectionError: error });
      },

      incrementReconnectAttempts: () => {
        set((state) => ({
          reconnectAttempts: state.reconnectAttempts + 1,
        }));
      },

      resetReconnectAttempts: () => {
        set({ reconnectAttempts: 0 });
      },

      // Typing and activity
      setTyping: (isTyping: boolean) => {
        set({ isTyping });
      },

      setAwaitingResponse: (awaiting: boolean) => {
        set({ awaitingResponse: awaiting });
      },

      updateLastActivity: () => {
        set({ lastActivity: new Date() });
      },

      // Session management
      setSessionId: (sessionId: string | null) => {
        set({ currentSessionId: sessionId });
      },

      generateNewSession: (): string => {
        const sessionId = `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
        set({ currentSessionId: sessionId });
        return sessionId;
      },

      // Message processing
      processWebSocketMessage: (wsMessage: WebSocketMessage) => {
        const displayMessage: DisplayMessage = {
          id: wsMessage.messageId || `ws_${Date.now()}_${Math.random()}`,
          content: wsMessage.content,
          role: wsMessage.role,
          type: wsMessage.type,
          timestamp: new Date(wsMessage.timestamp),
          isComplete: wsMessage.isComplete,
          requiresFollowUp: wsMessage.requiresFollowUp,
          followUpPrompt: wsMessage.followUpPrompt,
          intentRecognized: wsMessage.intentRecognized,
          confidenceScore: wsMessage.confidenceScore,
          data: wsMessage.data,
          isLoading: wsMessage.status === 'PROCESSING',
          error: wsMessage.errorMessage,
        };

        // Check if message already exists (for updates)
        const existingMessage = get().getMessageById(displayMessage.id);
        
        if (existingMessage) {
          get().updateMessage(displayMessage.id, displayMessage);
        } else {
          get().addMessage(displayMessage);
        }

        // Update awaiting response state
        if (wsMessage.role === 'ASSISTANT' && wsMessage.isComplete) {
          set({ awaitingResponse: false });
        }
      },

      addUserMessage: (content: string): DisplayMessage => {
        const message: DisplayMessage = {
          id: `user_${Date.now()}_${Math.random()}`,
          content,
          role: 'USER',
          type: 'COMMAND',
          timestamp: new Date(),
          isComplete: true,
        };

        get().addMessage(message);
        set({ awaitingResponse: true });
        
        return message;
      },

      // Suggestions and commands
      getCommandSuggestions: (): CommandSuggestion[] => {
        const state = get();
        
        // Return contextual suggestions based on conversation state
        if (state.messages.length === 0) {
          return defaultSuggestions;
        }

        // Could add logic here to provide contextual suggestions
        // based on the last message or conversation history
        return defaultSuggestions;
      },

      // Utilities
      getLastMessage: (): DisplayMessage | null => {
        const messages = get().messages;
        return messages.length > 0 ? messages[messages.length - 1] : null;
      },

      getMessageById: (id: string): DisplayMessage | null => {
        return get().messages.find((msg) => msg.id === id) || null;
      },

      getUnreadCount: (): number => {
        // For now, return 0. Could implement read/unread tracking later
        return 0;
      },

      markAllAsRead: () => {
        // Placeholder for read/unread functionality
      },
    })),
    {
      name: 'conversation-store',
    }
  )
);

// Selectors for common use cases
export const conversationSelectors = {
  isConnected: (state: ConversationStore) => state.isConnected,
  isConnecting: (state: ConversationStore) => state.isConnecting,
  hasError: (state: ConversationStore) => !!state.connectionError,
  hasMessages: (state: ConversationStore) => state.messages.length > 0,
  isAwaitingResponse: (state: ConversationStore) => state.awaitingResponse,
  lastMessage: (state: ConversationStore) => state.getLastMessage(),
  messageCount: (state: ConversationStore) => state.messages.length,
  canSendMessage: (state: ConversationStore) => 
    state.isConnected && !state.awaitingResponse,
  connectionStatus: (state: ConversationStore) => ({
    state: state.connectionState,
    error: state.connectionError,
    attempts: state.reconnectAttempts,
  }),
};
