import { apiClient } from './client';
import {
  LoginRequest,
  RegisterRequest,
  AuthResponse,
  TokenValidationResponse,
  UserProfile,
  UpdateUserRequest,
  ApiResponse
} from '@/types/api';

/**
 * Authentication API Service
 * Handles all authentication-related API calls
 */
export class AuthService {
  /**
   * <PERSON>gin user with email and password
   */
  static async login(credentials: LoginRequest): Promise<AuthResponse> {
    const response = await apiClient.axios.post<AuthResponse>('/api/v1/auth/login', credentials);
    return response.data;
  }

  /**
   * Register new user account
   */
  static async register(userData: RegisterRequest): Promise<AuthResponse> {
    const response = await apiClient.axios.post<AuthResponse>('/api/v1/auth/register', userData);
    return response.data;
  }

  /**
   * Logout user (invalidate token on server)
   */
  static async logout(): Promise<void> {
    try {
      await apiClient.post('/api/v1/auth/logout');
    } catch (error) {
      // Even if logout fails on server, we should clear local storage
      console.warn('Logout request failed, but continuing with local cleanup:', error);
    }
  }

  /**
   * Validate current token
   */
  static async validateToken(): Promise<TokenValidationResponse> {
    const response = await apiClient.axios.get<TokenValidationResponse>('/api/v1/auth/validate-token');
    return response.data;
  }

  /**
   * Refresh authentication token
   */
  static async refreshToken(): Promise<AuthResponse> {
    const response = await apiClient.axios.post<AuthResponse>('/api/v1/auth/refresh');
    return response.data;
  }

  /**
   * Verify email address with token
   */
  static async verifyEmail(token: string): Promise<void> {
    await apiClient.post(`/api/v1/auth/verify-email?token=${encodeURIComponent(token)}`);
  }

  /**
   * Resend email verification
   */
  static async resendVerification(email: string): Promise<void> {
    await apiClient.post(`/api/v1/auth/resend-verification?email=${encodeURIComponent(email)}`);
  }

  /**
   * Request password reset
   */
  static async forgotPassword(email: string): Promise<{ message: string; status: string }> {
    const response = await apiClient.axios.post<{ message: string; status: string }>(
      `/api/v1/auth/forgot-password?email=${encodeURIComponent(email)}`
    );
    return response.data;
  }

  /**
   * Reset password with token
   */
  static async resetPassword(token: string, newPassword: string): Promise<{ message: string; status: string }> {
    const response = await apiClient.axios.post<{ message: string; status: string }>(
      `/api/v1/auth/reset-password?token=${encodeURIComponent(token)}&newPassword=${encodeURIComponent(newPassword)}`
    );
    return response.data;
  }

  /**
   * Change password (authenticated user)
   */
  static async changePassword(currentPassword: string, newPassword: string): Promise<void> {
    await apiClient.post('/api/v1/auth/change-password', {
      currentPassword,
      newPassword
    });
  }

  /**
   * Get current user profile
   */
  static async getCurrentUser(): Promise<UserProfile> {
    const response = await apiClient.get<UserProfile>('/api/v1/users/me');
    return response.data;
  }

  /**
   * Update user profile
   */
  static async updateProfile(updates: UpdateUserRequest): Promise<UserProfile> {
    const response = await apiClient.put<UserProfile>('/api/v1/users/me', updates);
    return response.data;
  }

  /**
   * Update user preferences
   */
  static async updatePreferences(preferences: Record<string, any>): Promise<UserProfile> {
    const response = await apiClient.patch<UserProfile>('/api/v1/users/me/preferences', {
      preferences
    });
    return response.data;
  }

  /**
   * Update onboarding status
   */
  static async updateOnboardingStatus(
    status: 'STARTED' | 'PROFILE_COMPLETE' | 'ACCOUNTS_CONNECTED' | 'ALL_SET'
  ): Promise<UserProfile> {
    const response = await apiClient.patch<UserProfile>('/api/v1/users/me/onboarding', {
      onboardingStatus: status
    });
    return response.data;
  }

  /**
   * Delete user account
   */
  static async deleteAccount(password: string): Promise<void> {
    await apiClient.delete('/api/v1/users/me', {
      data: { password }
    });
  }

  /**
   * Check if email is available for registration
   */
  static async checkEmailAvailability(email: string): Promise<{ available: boolean }> {
    const response = await apiClient.get<{ available: boolean }>(
      `/api/v1/auth/check-email?email=${encodeURIComponent(email)}`
    );
    return response.data;
  }

  /**
   * Get user sessions (active login sessions)
   */
  static async getUserSessions(): Promise<any[]> {
    const response = await apiClient.get<any[]>('/api/v1/auth/sessions');
    return response.data;
  }

  /**
   * Revoke specific session
   */
  static async revokeSession(sessionId: string): Promise<void> {
    await apiClient.delete(`/api/v1/auth/sessions/${sessionId}`);
  }

  /**
   * Revoke all other sessions (keep current)
   */
  static async revokeAllOtherSessions(): Promise<void> {
    await apiClient.post('/api/v1/auth/revoke-all-sessions');
  }

  /**
   * Enable two-factor authentication
   */
  static async enableTwoFactor(): Promise<{ qrCode: string; backupCodes: string[] }> {
    const response = await apiClient.post<{ qrCode: string; backupCodes: string[] }>(
      '/api/v1/auth/2fa/enable'
    );
    return response.data;
  }

  /**
   * Verify and confirm two-factor authentication setup
   */
  static async confirmTwoFactor(code: string): Promise<void> {
    await apiClient.post('/api/v1/auth/2fa/confirm', { code });
  }

  /**
   * Disable two-factor authentication
   */
  static async disableTwoFactor(password: string): Promise<void> {
    await apiClient.post('/api/v1/auth/2fa/disable', { password });
  }

  /**
   * Verify two-factor authentication code
   */
  static async verifyTwoFactor(code: string): Promise<void> {
    await apiClient.post('/api/v1/auth/2fa/verify', { code });
  }

  /**
   * Generate new backup codes for 2FA
   */
  static async generateBackupCodes(): Promise<{ backupCodes: string[] }> {
    const response = await apiClient.post<{ backupCodes: string[] }>(
      '/api/v1/auth/2fa/backup-codes'
    );
    return response.data;
  }
}

// Export default instance
export const authService = AuthService;
