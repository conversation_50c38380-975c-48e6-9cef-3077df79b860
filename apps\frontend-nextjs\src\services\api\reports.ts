import { apiClient } from './client';
import { FinancialSummary } from '@/types/api';
import { DashboardData, FinancialMetrics, TransactionAnalysis, InvoiceAnalytics } from '@/types/financial';

/**
 * Financial Reports API Service
 * Handles all financial reporting and analytics API calls
 */
export class ReportsService {
  /**
   * Get financial summary for a period
   */
  static async getFinancialSummary(params?: {
    startDate?: string;
    endDate?: string;
    accountIds?: string[];
  }): Promise<FinancialSummary> {
    const queryString = params ? new URLSearchParams({
      ...params,
      accountIds: params.accountIds?.join(',') || ''
    } as any).toString() : '';
    const url = `/api/v1/reports/financial-summary${queryString ? `?${queryString}` : ''}`;
    
    const response = await apiClient.get<FinancialSummary>(url);
    return response.data;
  }

  /**
   * Get dashboard data (summary for main dashboard)
   */
  static async getDashboardData(): Promise<DashboardData> {
    const response = await apiClient.get<DashboardData>('/api/v1/reports/dashboard');
    return response.data;
  }

  /**
   * Get financial metrics with comparison
   */
  static async getFinancialMetrics(params?: {
    period?: 'MONTH' | 'QUARTER' | 'YEAR';
    startDate?: string;
    endDate?: string;
  }): Promise<FinancialMetrics> {
    const queryString = params ? new URLSearchParams(params as any).toString() : '';
    const url = `/api/v1/reports/metrics${queryString ? `?${queryString}` : ''}`;
    
    const response = await apiClient.get<FinancialMetrics>(url);
    return response.data;
  }

  /**
   * Get profit and loss statement
   */
  static async getProfitLoss(params?: {
    startDate?: string;
    endDate?: string;
    format?: 'summary' | 'detailed';
  }): Promise<{
    period: { startDate: string; endDate: string };
    revenue: {
      categories: Array<{ name: string; amount: number }>;
      total: number;
    };
    expenses: {
      categories: Array<{ name: string; amount: number }>;
      total: number;
    };
    netIncome: number;
    grossProfit: number;
    operatingIncome: number;
  }> {
    const queryString = params ? new URLSearchParams(params as any).toString() : '';
    const url = `/api/v1/reports/profit-loss${queryString ? `?${queryString}` : ''}`;
    
    const response = await apiClient.get(url);
    return response.data;
  }

  /**
   * Get balance sheet
   */
  static async getBalanceSheet(asOfDate?: string): Promise<{
    asOfDate: string;
    assets: {
      current: Array<{ name: string; amount: number }>;
      nonCurrent: Array<{ name: string; amount: number }>;
      total: number;
    };
    liabilities: {
      current: Array<{ name: string; amount: number }>;
      nonCurrent: Array<{ name: string; amount: number }>;
      total: number;
    };
    equity: {
      items: Array<{ name: string; amount: number }>;
      total: number;
    };
  }> {
    const url = `/api/v1/reports/balance-sheet${asOfDate ? `?asOfDate=${asOfDate}` : ''}`;
    
    const response = await apiClient.get(url);
    return response.data;
  }

  /**
   * Get cash flow statement
   */
  static async getCashFlow(params?: {
    startDate?: string;
    endDate?: string;
  }): Promise<{
    period: { startDate: string; endDate: string };
    operatingActivities: {
      items: Array<{ name: string; amount: number }>;
      total: number;
    };
    investingActivities: {
      items: Array<{ name: string; amount: number }>;
      total: number;
    };
    financingActivities: {
      items: Array<{ name: string; amount: number }>;
      total: number;
    };
    netCashFlow: number;
    beginningCash: number;
    endingCash: number;
  }> {
    const queryString = params ? new URLSearchParams(params as any).toString() : '';
    const url = `/api/v1/reports/cash-flow${queryString ? `?${queryString}` : ''}`;
    
    const response = await apiClient.get(url);
    return response.data;
  }

  /**
   * Get transaction analysis
   */
  static async getTransactionAnalysis(params?: {
    startDate?: string;
    endDate?: string;
    accountId?: string;
  }): Promise<TransactionAnalysis> {
    const queryString = params ? new URLSearchParams(params as any).toString() : '';
    const url = `/api/v1/reports/transaction-analysis${queryString ? `?${queryString}` : ''}`;
    
    const response = await apiClient.get<TransactionAnalysis>(url);
    return response.data;
  }

  /**
   * Get invoice analytics
   */
  static async getInvoiceAnalytics(params?: {
    startDate?: string;
    endDate?: string;
  }): Promise<InvoiceAnalytics> {
    const queryString = params ? new URLSearchParams(params as any).toString() : '';
    const url = `/api/v1/reports/invoice-analytics${queryString ? `?${queryString}` : ''}`;
    
    const response = await apiClient.get<InvoiceAnalytics>(url);
    return response.data;
  }

  /**
   * Get tax summary for ZRA reporting
   */
  static async getTaxSummary(params?: {
    startDate?: string;
    endDate?: string;
  }): Promise<{
    period: { startDate: string; endDate: string };
    totalIncome: number;
    totalExpenses: number;
    taxableIncome: number;
    vatCollected: number;
    vatPaid: number;
    netVat: number;
    estimatedIncomeTax: number;
    payeDeducted: number;
    complianceStatus: 'COMPLIANT' | 'PENDING' | 'OVERDUE';
    nextDeadlines: Array<{
      type: 'VAT' | 'INCOME_TAX' | 'PAYE';
      dueDate: string;
      description: string;
    }>;
  }> {
    const queryString = params ? new URLSearchParams(params as any).toString() : '';
    const url = `/api/v1/reports/tax-summary${queryString ? `?${queryString}` : ''}`;
    
    const response = await apiClient.get(url);
    return response.data;
  }

  /**
   * Export report to PDF
   */
  static async exportReportPDF(reportType: string, params?: Record<string, any>): Promise<Blob> {
    const queryString = params ? new URLSearchParams(params as any).toString() : '';
    const url = `/api/v1/reports/${reportType}/pdf${queryString ? `?${queryString}` : ''}`;
    
    const response = await apiClient.axios.get(url, {
      responseType: 'blob'
    });
    return response.data;
  }

  /**
   * Export report to Excel
   */
  static async exportReportExcel(reportType: string, params?: Record<string, any>): Promise<Blob> {
    const queryString = params ? new URLSearchParams(params as any).toString() : '';
    const url = `/api/v1/reports/${reportType}/excel${queryString ? `?${queryString}` : ''}`;
    
    const response = await apiClient.axios.get(url, {
      responseType: 'blob'
    });
    return response.data;
  }

  /**
   * Get custom report
   */
  static async getCustomReport(reportId: string, params?: Record<string, any>): Promise<any> {
    const queryString = params ? new URLSearchParams(params as any).toString() : '';
    const url = `/api/v1/reports/custom/${reportId}${queryString ? `?${queryString}` : ''}`;
    
    const response = await apiClient.get(url);
    return response.data;
  }

  /**
   * Get available report templates
   */
  static async getReportTemplates(): Promise<Array<{
    id: string;
    name: string;
    description: string;
    category: string;
    parameters: Array<{
      name: string;
      type: string;
      required: boolean;
      defaultValue?: any;
    }>;
  }>> {
    const response = await apiClient.get('/api/v1/reports/templates');
    return response.data;
  }

  /**
   * Schedule recurring report
   */
  static async scheduleReport(config: {
    reportType: string;
    frequency: 'DAILY' | 'WEEKLY' | 'MONTHLY' | 'QUARTERLY';
    recipients: string[];
    parameters?: Record<string, any>;
    format: 'PDF' | 'EXCEL' | 'CSV';
  }): Promise<{
    scheduleId: string;
    nextRun: string;
    status: 'ACTIVE' | 'PAUSED';
  }> {
    const response = await apiClient.post('/api/v1/reports/schedule', config);
    return response.data;
  }

  /**
   * Get scheduled reports
   */
  static async getScheduledReports(): Promise<Array<{
    id: string;
    reportType: string;
    frequency: string;
    recipients: string[];
    nextRun: string;
    lastRun?: string;
    status: 'ACTIVE' | 'PAUSED' | 'ERROR';
  }>> {
    const response = await apiClient.get('/api/v1/reports/scheduled');
    return response.data;
  }

  /**
   * Update scheduled report
   */
  static async updateScheduledReport(scheduleId: string, updates: {
    frequency?: 'DAILY' | 'WEEKLY' | 'MONTHLY' | 'QUARTERLY';
    recipients?: string[];
    status?: 'ACTIVE' | 'PAUSED';
    parameters?: Record<string, any>;
  }): Promise<void> {
    await apiClient.put(`/api/v1/reports/scheduled/${scheduleId}`, updates);
  }

  /**
   * Delete scheduled report
   */
  static async deleteScheduledReport(scheduleId: string): Promise<void> {
    await apiClient.delete(`/api/v1/reports/scheduled/${scheduleId}`);
  }
}

// Export default instance
export const reportsService = ReportsService;
