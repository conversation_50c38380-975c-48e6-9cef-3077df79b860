# PowerShell script to run ConversationRepository tests
# This script handles the setup and execution of the ConversationRepository test suite

param(
    [switch]$UseDocker = $false,
    [switch]$Verbose = $false,
    [string]$TestMethod = ""
)

Write-Host "🧪 ConversationRepository Test Runner" -ForegroundColor Cyan
Write-Host "=====================================" -ForegroundColor Cyan

# Set working directory
$BackendDir = "apps/backend-java-core"
$OriginalLocation = Get-Location

try {
    # Change to backend directory
    Set-Location $BackendDir
    Write-Host "📁 Working directory: $(Get-Location)" -ForegroundColor Gray

    # Check if we should use Docker
    if ($UseDocker) {
        Write-Host "🐳 Running tests using Docker..." -ForegroundColor Yellow
        
        # Check if Docker is available
        try {
            docker --version | Out-Null
            Write-Host "✅ Docker is available" -ForegroundColor Green
        }
        catch {
            Write-Host "❌ Docker is not available. Please install Docker or run without -UseDocker flag." -ForegroundColor Red
            exit 1
        }

        # Run tests in Docker container
        $dockerCommand = "docker run --rm -v `"$(Get-Location):/app`" -w /app maven:3.9.5-eclipse-temurin-17 mvn test -Dtest=ConversationRepositoryTest"
        
        if ($TestMethod) {
            $dockerCommand += "#$TestMethod"
        }
        
        if ($Verbose) {
            $dockerCommand += " -X"
        }

        Write-Host "🚀 Executing: $dockerCommand" -ForegroundColor Gray
        Invoke-Expression $dockerCommand
    }
    else {
        Write-Host "🔧 Running tests using local Maven..." -ForegroundColor Yellow
        
        # Check for Java
        try {
            $javaVersion = java -version 2>&1 | Select-String "version"
            Write-Host "✅ Java found: $javaVersion" -ForegroundColor Green
        }
        catch {
            Write-Host "❌ Java is not available. Please install Java 17+ or use -UseDocker flag." -ForegroundColor Red
            exit 1
        }

        # Set JAVA_HOME if not set
        if (-not $env:JAVA_HOME) {
            try {
                $javaPath = (Get-Command java).Source
                $javaBinDir = Split-Path $javaPath -Parent
                $env:JAVA_HOME = Split-Path $javaBinDir -Parent
                Write-Host "🔧 Set JAVA_HOME to: $env:JAVA_HOME" -ForegroundColor Yellow
            }
            catch {
                Write-Host "⚠️  Could not determine JAVA_HOME automatically" -ForegroundColor Yellow
            }
        }

        # Check for Maven wrapper
        $mvnwCmd = if (Test-Path "mvnw.cmd") { ".\mvnw.cmd" } else { ".\mvnw" }
        
        if (-not (Test-Path $mvnwCmd.TrimStart('.\\'))) {
            Write-Host "❌ Maven wrapper not found. Please ensure mvnw.cmd exists." -ForegroundColor Red
            exit 1
        }

        Write-Host "✅ Maven wrapper found: $mvnwCmd" -ForegroundColor Green

        # Build test command
        $testCommand = "$mvnwCmd test -Dtest=ConversationRepositoryTest"
        
        if ($TestMethod) {
            $testCommand += "#$TestMethod"
        }
        
        if ($Verbose) {
            $testCommand += " -X"
        }

        Write-Host "🚀 Executing: $testCommand" -ForegroundColor Gray
        
        # Execute the test command
        Invoke-Expression $testCommand
    }

    # Check exit code
    if ($LASTEXITCODE -eq 0) {
        Write-Host ""
        Write-Host "✅ Tests completed successfully!" -ForegroundColor Green
        Write-Host "📊 Check the test results above for detailed information." -ForegroundColor Gray
    }
    else {
        Write-Host ""
        Write-Host "❌ Tests failed with exit code: $LASTEXITCODE" -ForegroundColor Red
        Write-Host "📋 Check the error messages above for details." -ForegroundColor Gray
    }
}
catch {
    Write-Host "💥 An error occurred: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}
finally {
    # Return to original location
    Set-Location $OriginalLocation
}

Write-Host ""
Write-Host "📚 For more information, see: docs/testing/conversation-repository-qa-summary.md" -ForegroundColor Cyan
