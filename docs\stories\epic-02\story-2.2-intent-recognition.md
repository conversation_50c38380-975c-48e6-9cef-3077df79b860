# Story 2.2: Intent Recognition and Entity Extraction

**Epic:** Conversational Interface Foundation  
**Status:** Ready for Development  
**Priority:** High  
**Story Points:** 10

## User Story

**As a** user  
**I want** the system to understand my natural language commands and extract relevant information  
**So that** I can interact naturally without learning specific commands or syntax

## Acceptance Criteria

- [ ] AI service can recognize user intent from natural language commands
- [ ] System extracts relevant entities (amounts, dates, categories, etc.)
- [ ] Intent recognition accuracy is at least 90% for core MVP commands
- [ ] System provides confidence scores for intent recognition
- [ ] Fallback handling for ambiguous or unclear commands
- [ ] Support for core intents: CREATE_INVOICE, SHOW_SUMMARY, CATEGORIZE_TRANSACTION
- [ ] Entity extraction handles Zambian context (currency, business terms)
- [ ] Error handling for malformed or unsupported commands
- [ ] Graceful degradation when AI service is unavailable
- [ ] API contract is defined and documented
- [ ] Intent recognition events are published via messaging abstraction layer
- [ ] Entity extraction results are communicated through messaging events
- [ ] Clarification workflows work consistently across RabbitMQ (local) and Azure Service Bus (production)
- [ ] Failed intent processing events are routed to dead-letter queues

## Technical Implementation

### Frontend Changes
- `src/components/conversation/IntentDisplay.tsx` - Shows recognized intent and confidence
- `src/components/conversation/EntityHighlight.tsx` - Highlights extracted entities
- `src/hooks/useIntentRecognition.ts` - Intent recognition hook
- `src/types/conversation.ts` - Intent and entity type definitions

### API Gateway Changes
- `src/main/java/com/intellifin/gateway/routes/AIProxyController.java` - AI service proxy
- `src/main/java/com/intellifin/gateway/middleware/AICircuitBreaker.java` - Circuit breaker for AI service

### Service Changes
- **AI Service**: `src/main.py` - LangChain4j integration with Google Gemini
- **AI Service**: `src/intent_recognition.py` - Intent recognition logic with MessagingService event handling
- **AI Service**: `src/entity_extraction.py` - Entity extraction logic with event publishing
- **AI Service**: `src/services/messaging_service.py` - Python messaging abstraction implementation
- **Core Backend**: `src/main/java/com/intellifin/ai/` - AI service integration via Spring Cloud Stream
- **Core Backend**: `src/main/java/com/intellifin/messaging/` - Intent processing event handling

### Database Changes
- Intent recognition training data storage
- Entity extraction patterns and rules
- User command history for intent learning

## API Contracts

```typescript
// Intent recognition contracts
interface IntentRecognitionRequest {
  command: string;
  userId: string;
  sessionId: string;
  context?: {
    previousCommands: string[];
    userPreferences: any;
  };
}

interface IntentRecognitionResponse {
  intent: {
    name: string;
    confidence: number;
    description: string;
  };
  entities: {
    [entityType: string]: {
      value: any;
      confidence: number;
      startIndex: number;
      endIndex: number;
    }[];
  };
  suggestions?: string[];
  fallback?: {
    type: 'CLARIFICATION' | 'ALTERNATIVE' | 'ERROR';
    message: string;
    options?: string[];
  };
}

interface AIAPI {
  POST /api/v1/ai/intent: {
    body: IntentRecognitionRequest;
    response: IntentRecognitionResponse;
    errors: { 400: "Invalid request", 503: "AI service unavailable" };
  };
}

// Internal messaging events for intent processing (abstracted across RabbitMQ/Azure Service Bus)
interface IntentEvents {
  IntentRecognitionRequested: {
    commandId: string;
    command: string;
    userId: string;
    sessionId: string;
    context?: any;
    timestamp: string;
  };
  IntentRecognized: {
    commandId: string;
    intent: string;
    confidence: number;
    entities: Entity[];
    needsClarification: boolean;
    processingTime: number;
  };
  ClarificationRequired: {
    commandId: string;
    questions: string[];
    context: any;
    suggestedActions: string[];
  };
  IntentProcessingFailed: {
    commandId: string;
    error: string;
    retryCount: number;
    timestamp: string;
  };
}
```

## Error Handling

- **Low Confidence Intent:** Request clarification from user via messaging events
- **Ambiguous Entities:** Provide multiple interpretation options through clarification events
- **AI Service Unavailable:** Fallback to rule-based intent recognition with messaging dead-letter queues
- **Unsupported Commands:** Suggest similar supported commands via event responses
- **Malformed Input:** Clear error messages with examples
- **Messaging Broker Connectivity:** Issues handled by abstraction layer with automatic failover
- **Failed Intent Processing:** Events routed to dead-letter queues (RabbitMQ DLX or Azure Service Bus DLQ)
- **Event Processing Failures:** Retry mechanisms with exponential backoff and manual intervention queues

## Definition of Done

- [ ] AI service correctly recognizes core MVP intents with 90%+ accuracy
- [ ] Entity extraction works for amounts, dates, categories, and business terms
- [ ] Confidence scores are provided and used appropriately
- [ ] Fallback mechanisms work when AI service is unavailable
- [ ] Zambian context is properly handled (currency, business terminology)
- [ ] Error scenarios are handled gracefully with helpful suggestions
- [ ] Performance meets real-time requirements (< 2 seconds response time)
- [ ] Tests cover intent recognition accuracy and error scenarios
- [ ] Service can be deployed independently
- [ ] No breaking changes to other services
- [ ] Intent recognition events work consistently on both RabbitMQ (local) and Azure Service Bus (production)
- [ ] Messaging abstraction layer handles intent processing failures gracefully
- [ ] Dead-letter queue processing is implemented for failed intent events
- [ ] Event-driven clarification workflows maintain business logic environment independence

## Dependencies

- [Story 2.1: Basic Conversational Command Processing](story-2.1-conversational-commands.md)

## Notes

This story establishes the AI-powered understanding layer that makes the conversational interface truly intelligent. The intent recognition must be accurate and fast to provide a seamless user experience.

---

**Related Stories:**
- [Story 2.1: Basic Conversational Command Processing](story-2.1-conversational-commands.md)

**Epic:** [Conversational Interface Foundation](../../epics-and-stories.md#epic-2-conversational-interface-foundation) 