"use client"

import React from 'react';
import { motion } from 'framer-motion';
import { 
  Wallet, 
  TrendingUp, 
  TrendingDown, 
  AlertCircle, 
  Receipt, 
  Clock,
  DollarSign
} from 'lucide-react';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { useFinancialStore } from '@/stores';
import { formatCurrency } from '@/utils';
import type { DashboardData } from '@/types/financial';

interface BusinessVitalsBarProps {
  data?: DashboardData | null;
  isLoading?: boolean;
}

export function BusinessVitalsBar({ data, isLoading }: BusinessVitalsBarProps) {
  const { accounts } = useFinancialStore();

  // Calculate key metrics
  const totalBalance = accounts.reduce((sum, account) => sum + account.balance, 0);
  const totalIncome = data?.summary?.totalIncome || 0;
  const totalExpenses = data?.summary?.totalExpenses || 0;
  const netProfit = totalIncome - totalExpenses;
  const pendingInvoicesCount = data?.pendingInvoices?.length || 0;
  const overdueInvoicesCount = data?.alerts?.filter(
    alert => alert.type === 'OVERDUE_INVOICE'
  ).length || 0;
  const criticalAlertsCount = data?.alerts?.filter(
    alert => alert.severity === 'HIGH' || alert.severity === 'CRITICAL'
  ).length || 0;

  const vitals = [
    {
      id: 'balance',
      label: 'Total Balance',
      value: formatCurrency(totalBalance),
      icon: Wallet,
      color: 'blue',
      trend: totalBalance > 0 ? 'positive' : 'neutral'
    },
    {
      id: 'income',
      label: 'This Month Income',
      value: formatCurrency(totalIncome),
      icon: TrendingUp,
      color: 'green',
      trend: 'positive'
    },
    {
      id: 'expenses',
      label: 'This Month Expenses',
      value: formatCurrency(totalExpenses),
      icon: TrendingDown,
      color: 'red',
      trend: 'negative'
    },
    {
      id: 'profit',
      label: 'Net Profit',
      value: formatCurrency(netProfit),
      icon: DollarSign,
      color: netProfit >= 0 ? 'green' : 'red',
      trend: netProfit >= 0 ? 'positive' : 'negative'
    },
    {
      id: 'invoices',
      label: 'Pending Invoices',
      value: pendingInvoicesCount.toString(),
      icon: Receipt,
      color: pendingInvoicesCount > 0 ? 'yellow' : 'gray',
      trend: 'neutral',
      badge: overdueInvoicesCount > 0 ? { count: overdueInvoicesCount, color: 'red' } : undefined
    },
    {
      id: 'alerts',
      label: 'Critical Alerts',
      value: criticalAlertsCount.toString(),
      icon: AlertCircle,
      color: criticalAlertsCount > 0 ? 'red' : 'gray',
      trend: 'neutral'
    }
  ];

  const getColorClasses = (color: string, trend: string) => {
    const colors = {
      blue: {
        bg: 'bg-blue-100',
        text: 'text-blue-600',
        icon: 'text-blue-500'
      },
      green: {
        bg: 'bg-green-100',
        text: 'text-green-600',
        icon: 'text-green-500'
      },
      red: {
        bg: 'bg-red-100',
        text: 'text-red-600',
        icon: 'text-red-500'
      },
      yellow: {
        bg: 'bg-yellow-100',
        text: 'text-yellow-600',
        icon: 'text-yellow-500'
      },
      gray: {
        bg: 'bg-gray-100',
        text: 'text-gray-600',
        icon: 'text-gray-500'
      }
    };
    return colors[color as keyof typeof colors] || colors.gray;
  };

  if (isLoading) {
    return (
      <Card>
        <CardContent className="p-4">
          <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-4">
            {Array.from({ length: 6 }).map((_, index) => (
              <div key={index} className="animate-pulse">
                <div className="flex items-center space-x-3">
                  <div className="w-10 h-10 bg-gray-200 rounded-full" />
                  <div className="flex-1">
                    <div className="h-3 bg-gray-200 rounded mb-2" />
                    <div className="h-4 bg-gray-200 rounded w-16" />
                  </div>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardContent className="p-4">
        <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-4">
          {vitals.map((vital, index) => {
            const colorClasses = getColorClasses(vital.color, vital.trend);
            const Icon = vital.icon;
            
            return (
              <motion.div
                key={vital.id}
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: index * 0.05 }}
                className="flex items-center space-x-3 p-2 rounded-lg hover:bg-gray-50 transition-colors cursor-pointer"
              >
                <div className={`w-10 h-10 rounded-full flex items-center justify-center ${colorClasses.bg} relative`}>
                  <Icon className={`w-5 h-5 ${colorClasses.icon}`} />
                  {vital.badge && (
                    <div className="absolute -top-1 -right-1">
                      <Badge 
                        variant="destructive" 
                        className="w-5 h-5 p-0 flex items-center justify-center text-xs"
                      >
                        {vital.badge.count}
                      </Badge>
                    </div>
                  )}
                </div>
                <div className="flex-1 min-w-0">
                  <p className="text-xs text-gray-500 truncate">
                    {vital.label}
                  </p>
                  <p className={`text-sm font-semibold truncate ${colorClasses.text}`}>
                    {vital.value}
                  </p>
                </div>
              </motion.div>
            );
          })}
        </div>
      </CardContent>
    </Card>
  );
}
