# IntelliFin Security Implementation Guide

## Overview

This guide provides comprehensive security implementation details for the IntelliFin platform, covering authentication, authorization, data protection, and compliance requirements.

## 1. Authentication & Authorization

### 1.1 JWT Configuration

```java
// SecurityConfig.java
@Configuration
@EnableWebSecurity
@EnableMethodSecurity
public class SecurityConfig {

    @Value("${jwt.secret}")
    private String jwtSecret;

    @Value("${jwt.expiration:86400}") // 24 hours default
    private int jwtExpirationInSeconds;

    @Bean
    public PasswordEncoder passwordEncoder() {
        return new BCryptPasswordEncoder(12); // Strong hashing
    }

    @Bean
    public JwtAuthenticationEntryPoint jwtAuthenticationEntryPoint() {
        return new JwtAuthenticationEntryPoint();
    }

    @Bean
    public JwtRequestFilter jwtRequestFilter() {
        return new JwtRequestFilter();
    }

    @Bean
    public SecurityFilterChain filterChain(HttpSecurity http) throws Exception {
        http.csrf(csrf -> csrf.disable())
            .sessionManagement(session -> 
                session.sessionCreationPolicy(SessionCreationPolicy.STATELESS))
            .authorizeHttpRequests(authz -> authz
                .requestMatchers("/api/v1/auth/**").permitAll()
                .requestMatchers("/actuator/health").permitAll()
                .requestMatchers("/api/v1/admin/**").hasRole("ADMIN")
                .anyRequest().authenticated())
            .exceptionHandling(ex -> 
                ex.authenticationEntryPoint(jwtAuthenticationEntryPoint()))
            .addFilterBefore(jwtRequestFilter(), UsernamePasswordAuthenticationFilter.class);

        return http.build();
    }
}
```

### 1.2 JWT Token Management

```java
// JwtTokenUtil.java
@Component
public class JwtTokenUtil {
    
    private static final String AUTHORITIES_KEY = "authorities";
    private static final String USER_ID_KEY = "userId";
    
    @Value("${jwt.secret}")
    private String secret;
    
    @Value("${jwt.expiration}")
    private Long expiration;
    
    public String generateToken(UserDetails userDetails, String userId) {
        Map<String, Object> claims = new HashMap<>();
        claims.put(USER_ID_KEY, userId);
        claims.put(AUTHORITIES_KEY, userDetails.getAuthorities().stream()
            .map(GrantedAuthority::getAuthority)
            .collect(Collectors.toList()));
        
        return createToken(claims, userDetails.getUsername());
    }
    
    private String createToken(Map<String, Object> claims, String subject) {
        return Jwts.builder()
            .setClaims(claims)
            .setSubject(subject)
            .setIssuedAt(new Date(System.currentTimeMillis()))
            .setExpiration(new Date(System.currentTimeMillis() + expiration * 1000))
            .signWith(SignatureAlgorithm.HS512, secret)
            .compact();
    }
    
    public Boolean validateToken(String token, UserDetails userDetails) {
        final String username = getUsernameFromToken(token);
        return (username.equals(userDetails.getUsername()) && !isTokenExpired(token));
    }
    
    // Additional methods for token parsing and validation
}
```

### 1.3 Role-Based Access Control

```java
// User roles and permissions
public enum Role {
    BUSINESS_OWNER("ROLE_BUSINESS_OWNER", Set.of(
        Permission.READ_OWN_DATA,
        Permission.WRITE_OWN_DATA,
        Permission.CREATE_INVOICES,
        Permission.MANAGE_CLIENTS,
        Permission.VIEW_REPORTS
    )),
    
    ACCOUNTANT("ROLE_ACCOUNTANT", Set.of(
        Permission.READ_OWN_DATA,
        Permission.WRITE_OWN_DATA,
        Permission.VIEW_REPORTS,
        Permission.CATEGORIZE_TRANSACTIONS
    )),
    
    READ_ONLY("ROLE_READ_ONLY", Set.of(
        Permission.READ_OWN_DATA,
        Permission.VIEW_REPORTS
    ));
    
    private final String authority;
    private final Set<Permission> permissions;
}

public enum Permission {
    READ_OWN_DATA,
    WRITE_OWN_DATA,
    CREATE_INVOICES,
    MANAGE_CLIENTS,
    VIEW_REPORTS,
    CATEGORIZE_TRANSACTIONS,
    ADMIN_ACCESS
}
```

## 2. Data Encryption

### 2.1 Database Encryption

```java
// EncryptionService.java
@Service
public class EncryptionService {
    
    private final AESUtil aesUtil;
    
    @Value("${encryption.key}")
    private String encryptionKey;
    
    public String encryptSensitiveData(String plainText) {
        if (plainText == null || plainText.isEmpty()) {
            return plainText;
        }
        try {
            return aesUtil.encrypt(plainText, encryptionKey);
        } catch (Exception e) {
            throw new SecurityException("Failed to encrypt sensitive data", e);
        }
    }
    
    public String decryptSensitiveData(String encryptedText) {
        if (encryptedText == null || encryptedText.isEmpty()) {
            return encryptedText;
        }
        try {
            return aesUtil.decrypt(encryptedText, encryptionKey);
        } catch (Exception e) {
            throw new SecurityException("Failed to decrypt sensitive data", e);
        }
    }
}

// JPA Attribute Converter for automatic encryption
@Converter
public class EncryptedStringConverter implements AttributeConverter<String, String> {
    
    @Autowired
    private EncryptionService encryptionService;
    
    @Override
    public String convertToDatabaseColumn(String attribute) {
        return encryptionService.encryptSensitiveData(attribute);
    }
    
    @Override
    public String convertToEntityAttribute(String dbData) {
        return encryptionService.decryptSensitiveData(dbData);
    }
}
```

### 2.2 API Communication Security

```java
// TLS Configuration
@Configuration
public class TlsConfig {
    
    @Bean
    public TomcatServletWebServerFactory servletContainer() {
        TomcatServletWebServerFactory tomcat = new TomcatServletWebServerFactory() {
            @Override
            protected void postProcessContext(Context context) {
                SecurityConstraint securityConstraint = new SecurityConstraint();
                securityConstraint.setUserConstraint("CONFIDENTIAL");
                SecurityCollection collection = new SecurityCollection();
                collection.addPattern("/*");
                securityConstraint.addCollection(collection);
                context.addConstraint(securityConstraint);
            }
        };
        
        tomcat.addAdditionalTomcatConnectors(redirectConnector());
        return tomcat;
    }
    
    private Connector redirectConnector() {
        Connector connector = new Connector("org.apache.coyote.http11.Http11NioProtocol");
        connector.setScheme("http");
        connector.setPort(8080);
        connector.setSecure(false);
        connector.setRedirectPort(8443);
        return connector;
    }
}
```

## 3. Input Validation & Sanitization

### 3.1 Request Validation

```java
// ValidationConfig.java
@Configuration
public class ValidationConfig {
    
    @Bean
    public Validator validator() {
        return Validation.buildDefaultValidatorFactory().getValidator();
    }
}

// Custom validation annotations
@Target({ElementType.FIELD})
@Retention(RetentionPolicy.RUNTIME)
@Constraint(validatedBy = ZambianPhoneValidator.class)
public @interface ValidZambianPhone {
    String message() default "Invalid Zambian phone number format";
    Class<?>[] groups() default {};
    Class<? extends Payload>[] payload() default {};
}

@Target({ElementType.FIELD})
@Retention(RetentionPolicy.RUNTIME)
@Constraint(validatedBy = TPINValidator.class)
public @interface ValidTPIN {
    String message() default "Invalid TPIN format";
    Class<?>[] groups() default {};
    Class<? extends Payload>[] payload() default {};
}

// Request DTOs with validation
public class CreateTransactionRequest {
    
    @NotBlank(message = "Description is required")
    @Size(max = 255, message = "Description must not exceed 255 characters")
    @Pattern(regexp = "^[a-zA-Z0-9\\s\\-_.,]+$", message = "Description contains invalid characters")
    private String description;
    
    @NotNull(message = "Amount is required")
    @DecimalMin(value = "0.01", message = "Amount must be greater than 0")
    @DecimalMax(value = "999999999.99", message = "Amount exceeds maximum limit")
    private BigDecimal amount;
    
    @NotNull(message = "Transaction type is required")
    @Pattern(regexp = "^(INCOME|EXPENSE)$", message = "Invalid transaction type")
    private String type;
    
    @NotNull(message = "Date is required")
    @PastOrPresent(message = "Transaction date cannot be in the future")
    private LocalDate date;
    
    // Getters and setters
}
```

### 3.2 SQL Injection Prevention

```java
// Repository with parameterized queries
@Repository
public class TransactionRepository {
    
    @PersistenceContext
    private EntityManager entityManager;
    
    public List<Transaction> findTransactionsByUserAndDateRange(
            String userId, LocalDate startDate, LocalDate endDate) {
        
        String jpql = """
            SELECT t FROM Transaction t 
            WHERE t.userId = :userId 
            AND t.date BETWEEN :startDate AND :endDate 
            ORDER BY t.date DESC
            """;
            
        return entityManager.createQuery(jpql, Transaction.class)
            .setParameter("userId", userId)
            .setParameter("startDate", startDate)
            .setParameter("endDate", endDate)
            .getResultList();
    }
}
```

## 4. Rate Limiting & DDoS Protection

### 4.1 Rate Limiting Configuration

```java
// RateLimitingConfig.java
@Configuration
@EnableRedisRepositories
public class RateLimitingConfig {
    
    @Bean
    public RedisTemplate<String, String> redisTemplate(RedisConnectionFactory connectionFactory) {
        RedisTemplate<String, String> template = new RedisTemplate<>();
        template.setConnectionFactory(connectionFactory);
        template.setDefaultSerializer(new StringRedisSerializer());
        return template;
    }
}

// Rate limiting interceptor
@Component
public class RateLimitInterceptor implements HandlerInterceptor {
    
    private final RedisTemplate<String, String> redisTemplate;
    
    private static final int MAX_REQUESTS_PER_MINUTE = 60;
    private static final int MAX_REQUESTS_PER_HOUR = 1000;
    
    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, 
                           Object handler) throws Exception {
        
        String clientIp = getClientIpAddress(request);
        String userAgent = request.getHeader("User-Agent");
        String endpoint = request.getRequestURI();
        
        // Check rate limits
        if (!checkRateLimit(clientIp, endpoint)) {
            response.setStatus(HttpStatus.TOO_MANY_REQUESTS.value());
            response.getWriter().write("{\"error\":\"Rate limit exceeded\"}");
            return false;
        }
        
        return true;
    }
    
    private boolean checkRateLimit(String clientIp, String endpoint) {
        String minuteKey = String.format("rate_limit:%s:%s:%d", 
            clientIp, endpoint, System.currentTimeMillis() / 60000);
        String hourKey = String.format("rate_limit:%s:%s:%d", 
            clientIp, endpoint, System.currentTimeMillis() / 3600000);
        
        Long minuteCount = redisTemplate.opsForValue().increment(minuteKey);
        redisTemplate.expire(minuteKey, Duration.ofMinutes(1));
        
        Long hourCount = redisTemplate.opsForValue().increment(hourKey);
        redisTemplate.expire(hourKey, Duration.ofHours(1));
        
        return minuteCount <= MAX_REQUESTS_PER_MINUTE && hourCount <= MAX_REQUESTS_PER_HOUR;
    }
}
```

## 5. Audit Logging

### 5.1 Security Event Logging

```java
// SecurityAuditService.java
@Service
public class SecurityAuditService {
    
    private final AuditEventRepository auditEventRepository;
    private final Logger securityLogger = LoggerFactory.getLogger("SECURITY");
    
    public void logSecurityEvent(SecurityEventType eventType, String userId, 
                                String details, HttpServletRequest request) {
        
        SecurityAuditEvent event = SecurityAuditEvent.builder()
            .eventType(eventType)
            .userId(userId)
            .ipAddress(getClientIpAddress(request))
            .userAgent(request.getHeader("User-Agent"))
            .details(details)
            .timestamp(Instant.now())
            .build();
        
        auditEventRepository.save(event);
        
        // Log to security log file
        securityLogger.info("Security Event: {} - User: {} - IP: {} - Details: {}", 
            eventType, userId, event.getIpAddress(), details);
    }
    
    public void logFailedLogin(String email, String ipAddress, String reason) {
        logSecurityEvent(SecurityEventType.FAILED_LOGIN, email, 
            "Failed login attempt: " + reason, null);
    }
    
    public void logSuccessfulLogin(String userId, String ipAddress) {
        logSecurityEvent(SecurityEventType.SUCCESSFUL_LOGIN, userId, 
            "User logged in successfully", null);
    }
}

public enum SecurityEventType {
    SUCCESSFUL_LOGIN,
    FAILED_LOGIN,
    PASSWORD_CHANGE,
    ACCOUNT_LOCKED,
    SUSPICIOUS_ACTIVITY,
    DATA_ACCESS,
    PERMISSION_DENIED,
    TOKEN_REFRESH,
    LOGOUT
}
```

## 6. Environment Configuration

### 6.1 Security Properties

```yaml
# application-security.yml
security:
  jwt:
    secret: ${JWT_SECRET:your-256-bit-secret-key-here}
    expiration: ${JWT_EXPIRATION:86400} # 24 hours
    refresh-expiration: ${JWT_REFRESH_EXPIRATION:604800} # 7 days
  
  encryption:
    key: ${ENCRYPTION_KEY:your-encryption-key-here}
    algorithm: AES/GCM/NoPadding
  
  rate-limiting:
    enabled: true
    requests-per-minute: 60
    requests-per-hour: 1000
    burst-capacity: 10
  
  cors:
    allowed-origins: ${ALLOWED_ORIGINS:http://localhost:3000}
    allowed-methods: GET,POST,PUT,DELETE,OPTIONS
    allowed-headers: "*"
    allow-credentials: true
  
  session:
    timeout: 1800 # 30 minutes
    max-concurrent-sessions: 3
```

### 6.2 Production Security Checklist

```markdown
## Production Security Checklist

### Infrastructure Security
- [ ] TLS 1.3 enabled for all communications
- [ ] Security headers configured (HSTS, CSP, X-Frame-Options)
- [ ] Database connections encrypted
- [ ] VPC/Network segmentation implemented
- [ ] Firewall rules configured (minimal access)
- [ ] Regular security updates applied

### Application Security
- [ ] JWT secrets rotated and stored securely
- [ ] Database credentials encrypted
- [ ] API keys stored in secure vault
- [ ] Input validation on all endpoints
- [ ] SQL injection protection verified
- [ ] XSS protection implemented
- [ ] CSRF protection enabled

### Monitoring & Logging
- [ ] Security event logging enabled
- [ ] Failed login attempt monitoring
- [ ] Anomaly detection configured
- [ ] Log aggregation and analysis setup
- [ ] Incident response procedures documented
- [ ] Regular security audits scheduled

### Compliance
- [ ] Data encryption at rest and in transit
- [ ] User consent mechanisms implemented
- [ ] Data retention policies enforced
- [ ] Right to deletion implemented
- [ ] Privacy policy updated
- [ ] Zambian Data Protection Act compliance verified
```

This security implementation guide provides a comprehensive foundation for securing the IntelliFin platform while maintaining usability and performance.
