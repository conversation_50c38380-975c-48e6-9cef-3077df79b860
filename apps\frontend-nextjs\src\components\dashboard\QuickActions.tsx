"use client"

import React from 'react';
import { useRouter } from 'next/navigation';
import { motion } from 'framer-motion';
import { 
  Plus, 
  Receipt, 
  CreditCard, 
  Users, 
  FileText, 
  BarChart3,
  Zap,
  ArrowRight
} from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { useConversationStore } from '@/stores';
import { ROUTES } from '@/utils/constants';

interface QuickAction {
  id: string;
  title: string;
  description: string;
  icon: React.ComponentType<{ className?: string }>;
  color: string;
  action: () => void;
  command?: string; // AI command to send
}

export function QuickActions() {
  const router = useRouter();
  const { addUserMessage, isConnected } = useConversationStore();

  const sendAICommand = (command: string) => {
    if (isConnected) {
      addUserMessage(command);
    } else {
      // Fallback: show a message or redirect
      console.log('AI not connected, command:', command);
    }
  };

  const quickActions: QuickAction[] = [
    {
      id: 'create-invoice',
      title: 'Create Invoice',
      description: 'Generate a new invoice for a client',
      icon: Receipt,
      color: 'bg-blue-500 hover:bg-blue-600',
      action: () => sendAICommand('Create a new invoice'),
      command: 'Create a new invoice',
    },
    {
      id: 'add-transaction',
      title: 'Add Transaction',
      description: 'Record a new income or expense',
      icon: CreditCard,
      color: 'bg-green-500 hover:bg-green-600',
      action: () => sendAICommand('Add a new transaction'),
      command: 'Add a new transaction',
    },
    {
      id: 'add-client',
      title: 'Add Client',
      description: 'Create a new client profile',
      icon: Users,
      color: 'bg-purple-500 hover:bg-purple-600',
      action: () => router.push(`${ROUTES.CLIENTS}?action=create`),
    },
    {
      id: 'view-reports',
      title: 'View Reports',
      description: 'Check your financial reports',
      icon: BarChart3,
      color: 'bg-orange-500 hover:bg-orange-600',
      action: () => router.push(ROUTES.REPORTS),
    },
    {
      id: 'categorize-expenses',
      title: 'Categorize Expenses',
      description: 'Review and categorize pending transactions',
      icon: FileText,
      color: 'bg-yellow-500 hover:bg-yellow-600',
      action: () => sendAICommand('Help me categorize my recent expenses'),
      command: 'Help me categorize my recent expenses',
    },
    {
      id: 'financial-summary',
      title: 'Financial Summary',
      description: 'Get an overview of your finances',
      icon: Zap,
      color: 'bg-indigo-500 hover:bg-indigo-600',
      action: () => sendAICommand('Show me my financial summary for this month'),
      command: 'Show me my financial summary for this month',
    },
  ];

  return (
    <Card>
      <CardHeader>
        <CardTitle className="text-lg flex items-center space-x-2">
          <Zap className="h-5 w-5 text-primary" />
          <span>Quick Actions</span>
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="grid grid-cols-2 gap-3">
          {quickActions.map((action, index) => (
            <motion.div
              key={action.id}
              initial={{ opacity: 0, scale: 0.9 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ duration: 0.3, delay: index * 0.1 }}
            >
              <Button
                variant="outline"
                className="w-full h-auto p-4 flex flex-col items-center space-y-2 hover:shadow-md transition-all duration-200 group"
                onClick={action.action}
              >
                <div className={`w-10 h-10 rounded-full ${action.color} flex items-center justify-center transition-transform group-hover:scale-110`}>
                  <action.icon className="h-5 w-5 text-white" />
                </div>
                <div className="text-center">
                  <p className="text-sm font-medium text-gray-900 group-hover:text-primary transition-colors">
                    {action.title}
                  </p>
                  <p className="text-xs text-gray-500 mt-1 leading-tight">
                    {action.description}
                  </p>
                </div>
                <ArrowRight className="h-3 w-3 text-gray-400 opacity-0 group-hover:opacity-100 transition-opacity" />
              </Button>
            </motion.div>
          ))}
        </div>

        {/* AI Status Indicator */}
        <div className="mt-4 pt-4 border-t border-gray-200">
          <div className="flex items-center justify-between text-xs">
            <div className="flex items-center space-x-2">
              <div className={`w-2 h-2 rounded-full ${isConnected ? 'bg-green-500' : 'bg-red-500'}`} />
              <span className="text-gray-600">
                AI Assistant {isConnected ? 'Online' : 'Offline'}
              </span>
            </div>
            {!isConnected && (
              <span className="text-red-500">Reconnecting...</span>
            )}
          </div>
          {isConnected && (
            <p className="text-xs text-gray-500 mt-1">
              Click actions with 🤖 to interact with AI
            </p>
          )}
        </div>
      </CardContent>
    </Card>
  );
}
