# IntelliFin Development Environment
# Version: 1.0
# Usage: docker-compose up -d

version: '3.8'

services:
  # PostgreSQL Database
  postgres:
    image: postgres:15-alpine
    container_name: intellifin-postgres
    environment:
      POSTGRES_DB: intellifin_dev
      POSTGRES_USER: intellifin_user
      POSTGRES_PASSWORD: intellifin_dev_password
      POSTGRES_INITDB_ARGS: "--encoding=UTF8 --locale=C"
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./docs/database/migrations:/docker-entrypoint-initdb.d
    networks:
      - intellifin-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U intellifin_user -d intellifin_dev"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Redis Cache
  redis:
    image: redis:7-alpine
    container_name: intellifin-redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - intellifin-network
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Ollama for local AI development
  ollama:
    image: ollama/ollama:latest
    container_name: intellifin-ollama
    ports:
      - "11434:11434"
    volumes:
      - ollama_data:/root/.ollama
    networks:
      - intellifin-network
    environment:
      - OLLAMA_HOST=0.0.0.0
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:11434/api/tags"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Core Backend Service (Java Spring Boot)
  backend-core:
    build:
      context: ./apps/backend-java-core
      dockerfile: Dockerfile
      target: runtime
    container_name: intellifin-backend-core
    ports:
      - "8080:8080"
      - "5005:5005" # Debug port
    environment:
      - SPRING_PROFILES_ACTIVE=development
      - DATABASE_URL=**********************************************
      - DATABASE_USERNAME=intellifin_user
      - DATABASE_PASSWORD=intellifin_dev_password
      - REDIS_URL=redis://redis:6379
      - AI_SERVICE_URL=http://ai-service:8000
      - ZRA_SERVICE_URL=http://zra-service:8081
      - JWT_SECRET=dev_jwt_secret_key_change_in_production
      - FRONTEND_URL=http://localhost:3000
      - EMAIL_ENABLED=false
      - EMAIL_FROM=<EMAIL>
      - GOOGLE_CLIENT_ID=${GOOGLE_CLIENT_ID:-}
      - GOOGLE_CLIENT_SECRET=${GOOGLE_CLIENT_SECRET:-}
      - APPLE_CLIENT_ID=${APPLE_CLIENT_ID:-}
      - APPLE_CLIENT_SECRET=${APPLE_CLIENT_SECRET:-}
      - JAVA_OPTS=-Xmx512m -Xms256m -agentlib:jdwp=transport=dt_socket,server=y,suspend=n,address=*:5005
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    networks:
      - intellifin-network
    volumes:
      - maven_cache:/root/.m2
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/actuator/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # AI Service (Python FastAPI)
  ai-service:
    build:
      context: ./apps/backend-python-ai
      dockerfile: Dockerfile
      target: development
    container_name: intellifin-ai-service
    ports:
      - "8000:8000"
    environment:
      - ENVIRONMENT=development
      - OLLAMA_BASE_URL=http://ollama:11434
      - GOOGLE_API_KEY=${GOOGLE_API_KEY:-}
      - REDIS_URL=redis://redis:6379
      - LOG_LEVEL=DEBUG
    depends_on:
      redis:
        condition: service_healthy
      ollama:
        condition: service_healthy
    networks:
      - intellifin-network
    volumes:
      - ./apps/backend-python-ai:/app
      - python_cache:/root/.cache
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # ZRA Compliance Service (Java)
  zra-service:
    build:
      context: ./apps/backend-java-zra
      dockerfile: Dockerfile
      target: runtime
    container_name: intellifin-zra-service
    ports:
      - "8081:8081"
      - "5006:5006" # Debug port
    environment:
      - SPRING_PROFILES_ACTIVE=development
      - DATABASE_URL=**********************************************
      - DATABASE_USERNAME=intellifin_user
      - DATABASE_PASSWORD=intellifin_dev_password
      - ZRA_API_BASE_URL=https://sandbox.zra.org.zm/vsdc
      - ZRA_API_KEY=${ZRA_API_KEY:-sandbox_key}
      - JAVA_OPTS=-Xmx256m -Xms128m -agentlib:jdwp=transport=dt_socket,server=y,suspend=n,address=*:5006
    depends_on:
      postgres:
        condition: service_healthy
    networks:
      - intellifin-network
    volumes:
      - maven_cache:/root/.m2
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8081/actuator/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Frontend (Next.js)
  frontend:
    build:
      context: ./apps/frontend-nextjs
      dockerfile: Dockerfile
      target: runner
    container_name: intellifin-frontend
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=development
      - NEXT_PUBLIC_API_BASE_URL=http://localhost:8080
      - NEXT_PUBLIC_WS_URL=ws://localhost:8080
      - NEXT_PUBLIC_AI_SERVICE_URL=http://localhost:8000
    depends_on:
      - backend-core
      - ai-service
    networks:
      - intellifin-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/api/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # pgAdmin for database management (optional)
  pgadmin:
    image: dpage/pgadmin4:latest
    container_name: intellifin-pgadmin
    environment:
      PGADMIN_DEFAULT_EMAIL: <EMAIL>
      PGADMIN_DEFAULT_PASSWORD: admin123
      PGADMIN_CONFIG_SERVER_MODE: 'False'
    ports:
      - "5050:80"
    depends_on:
      - postgres
    networks:
      - intellifin-network
    volumes:
      - pgadmin_data:/var/lib/pgadmin

volumes:
  postgres_data:
  redis_data:
  ollama_data:
  maven_cache:
  python_cache:
  node_modules_cache:
  pgadmin_data:

networks:
  intellifin-network:
    driver: bridge
