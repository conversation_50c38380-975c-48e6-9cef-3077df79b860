# IntelliFin AI Service Implementation Guide

## Overview

This guide provides detailed implementation specifications for the IntelliFin AI Service, including prompt engineering, model configuration, and integration patterns.

## 1. AI Service Architecture

### 1.1 Technology Stack
- **Framework:** Python FastAPI
- **AI Orchestration:** LangChain
- **Local Development:** Ollama with Llama 3.1 8B
- **Production:** Google Gemini Pro
- **Vector Database:** Azure AI Search (for RAG)
- **Caching:** Redis

### 1.2 Service Structure
```
apps/backend-python-ai/
├── src/
│   ├── main.py                 # FastAPI application entry point
│   ├── config/
│   │   ├── settings.py         # Environment configuration
│   │   └── ai_config.py        # AI model configurations
│   ├── services/
│   │   ├── categorization.py   # Transaction categorization
│   │   ├── intent_recognition.py # Natural language intent parsing
│   │   ├── entity_extraction.py  # Entity extraction from commands
│   │   └── explainability.py   # AI decision explanations
│   ├── models/
│   │   ├── prompts.py          # Prompt templates
│   │   └── schemas.py          # Pydantic models
│   ├── utils/
│   │   ├── langchain_setup.py  # LangChain configuration
│   │   └── cache.py            # Redis caching utilities
│   └── tests/
├── requirements.txt
├── Dockerfile.dev
└── README.md
```

## 2. Prompt Engineering

### 2.1 Transaction Categorization Prompts

```python
TRANSACTION_CATEGORIZATION_PROMPT = """
You are an expert financial assistant for Zambian small businesses. 
Your task is to categorize financial transactions based on their descriptions.

Available Categories:
INCOME: Sales Revenue, Service Revenue, Consulting Revenue, Commission Income, Rental Income, Interest Income, Refunds, Grants, Other Income
EXPENSE: Office Supplies, Transport, Utilities, Rent, Marketing, Professional Services, Equipment, Insurance, Bank Charges, Telecommunications, Fuel, Maintenance, Training, Legal Fees, Licenses, Raw Materials, Inventory Purchase, Packaging, Shipping, Owner Draw, Personal Expense, Tax Payment, VAT Payment, Miscellaneous

Context about Zambian businesses:
- ZESCO is the national electricity provider (Utilities)
- MTN, Airtel are mobile network operators (Telecommunications)
- NAPSA is social security (Professional Services)
- ZRA is tax authority (Tax Payment)
- Common transport: minibus, taxi, fuel (Transport)

Transaction to categorize:
Description: {description}
Amount: K{amount}
Type: {transaction_type}

Provide your response in this exact JSON format:
{{
    "category": "category_name",
    "confidence": 0.95,
    "explanation": "Brief explanation of why this category was chosen"
}}

Be specific and confident in your categorization. Consider Zambian business context.
"""

INTENT_RECOGNITION_PROMPT = """
You are IntelliFin's conversational AI assistant for Zambian small businesses.
Parse the user's command and identify their intent and extract relevant entities.

Available Intents:
- VIEW_TRANSACTIONS: User wants to see transaction history
- CREATE_INVOICE: User wants to create an invoice
- VIEW_FINANCIAL_SUMMARY: User wants profit/loss or financial overview
- CATEGORIZE_TRANSACTION: User wants to categorize a transaction
- CONNECT_ACCOUNT: User wants to connect a financial account
- GET_HELP: User needs assistance
- UNCLEAR: Intent is not clear

User Command: "{command}"

Extract entities like:
- amounts (e.g., "K500", "500 kwacha")
- dates (e.g., "this week", "last month", "July")
- client names (e.g., "Phiri Corp", "John Banda")
- categories (e.g., "utilities", "transport")

Provide response in this JSON format:
{{
    "intent": "intent_name",
    "confidence": 0.95,
    "entities": {{
        "amount": null,
        "date_range": null,
        "client_name": null,
        "category": null,
        "other": {{}}
    }},
    "needs_clarification": false,
    "clarification_questions": []
}}
"""

FINANCIAL_EXPLANATION_PROMPT = """
You are a friendly financial advisor explaining accounting concepts to Zambian small business owners.
Explain the financial concept or calculation in simple, clear terms.

Context: {context}
Question: {question}
Data: {financial_data}

Provide a clear, educational explanation that:
1. Uses simple language (avoid jargon)
2. Relates to Zambian business context
3. Gives actionable insights
4. Explains the "why" behind numbers

Keep response under 200 words and be encouraging.
"""
```

### 2.2 Confidence Thresholds

```python
CONFIDENCE_THRESHOLDS = {
    "categorization": {
        "high": 0.85,      # Auto-apply category
        "medium": 0.60,    # Suggest with explanation
        "low": 0.40        # Request manual categorization
    },
    "intent_recognition": {
        "high": 0.90,      # Execute intent
        "medium": 0.70,    # Ask for confirmation
        "low": 0.50        # Request clarification
    }
}
```

## 3. Model Configuration

### 3.1 Local Development (Ollama)

```python
# config/ai_config.py
from langchain_community.llms import Ollama

def get_local_llm():
    return Ollama(
        model="llama3.1:8b",
        base_url="http://ollama:11434",
        temperature=0.1,  # Low temperature for consistent categorization
        top_p=0.9,
        num_predict=512,
        stop=["</response>", "\n\n"]
    )

LOCAL_MODEL_CONFIG = {
    "model_name": "llama3.1:8b",
    "temperature": 0.1,
    "max_tokens": 512,
    "timeout": 30,
    "retry_attempts": 3
}
```

### 3.2 Production (Google Gemini)

```python
from langchain_google_genai import ChatGoogleGenerativeAI

def get_production_llm():
    return ChatGoogleGenerativeAI(
        model="gemini-pro",
        temperature=0.1,
        max_output_tokens=512,
        safety_settings={
            "HARM_CATEGORY_HARASSMENT": "BLOCK_NONE",
            "HARM_CATEGORY_HATE_SPEECH": "BLOCK_NONE",
            "HARM_CATEGORY_SEXUALLY_EXPLICIT": "BLOCK_NONE",
            "HARM_CATEGORY_DANGEROUS_CONTENT": "BLOCK_NONE"
        }
    )

PRODUCTION_MODEL_CONFIG = {
    "model_name": "gemini-pro",
    "temperature": 0.1,
    "max_tokens": 512,
    "timeout": 30,
    "retry_attempts": 3,
    "rate_limit": {
        "requests_per_minute": 60,
        "tokens_per_minute": 30000
    }
}
```

## 4. RAG Pipeline Configuration

### 4.1 Vector Database Setup

```python
# utils/rag_setup.py
from langchain_community.vectorstores import AzureSearch
from langchain_openai import OpenAIEmbeddings

def setup_vector_store():
    embeddings = OpenAIEmbeddings(
        model="text-embedding-ada-002",
        openai_api_key=settings.OPENAI_API_KEY
    )
    
    vector_store = AzureSearch(
        azure_search_endpoint=settings.AZURE_SEARCH_ENDPOINT,
        azure_search_key=settings.AZURE_SEARCH_KEY,
        index_name="intellifin-knowledge",
        embedding_function=embeddings.embed_query,
        fields={
            "content": "content",
            "metadata": "metadata",
            "vector": "content_vector"
        }
    )
    
    return vector_store

KNOWLEDGE_BASE_SOURCES = [
    "zambian_business_terms.json",
    "accounting_principles.json",
    "zra_regulations.json",
    "mobile_money_providers.json",
    "common_transactions.json"
]
```

### 4.2 Knowledge Base Content

```json
// zambian_business_terms.json
{
  "providers": {
    "ZESCO": {
      "type": "utility",
      "category": "Utilities",
      "description": "National electricity provider"
    },
    "MTN": {
      "type": "telecom",
      "category": "Telecommunications",
      "services": ["mobile_money", "airtime", "data"]
    },
    "Airtel": {
      "type": "telecom", 
      "category": "Telecommunications",
      "services": ["mobile_money", "airtime", "data"]
    }
  },
  "common_expenses": {
    "transport": ["taxi", "minibus", "fuel", "bus fare"],
    "utilities": ["ZESCO", "water", "electricity"],
    "telecommunications": ["MTN", "Airtel", "Zamtel", "airtime", "data"]
  }
}
```

## 5. Error Handling and Fallbacks

### 5.1 Service Resilience

```python
# services/categorization.py
import asyncio
from typing import Optional
from functools import wraps

def with_fallback(fallback_response):
    def decorator(func):
        @wraps(func)
        async def wrapper(*args, **kwargs):
            try:
                return await func(*args, **kwargs)
            except Exception as e:
                logger.error(f"AI service error: {e}")
                return fallback_response
        return wrapper
    return decorator

@with_fallback({
    "category": "Uncategorized",
    "confidence": 0.0,
    "explanation": "AI service temporarily unavailable. Please categorize manually."
})
async def categorize_transaction(description: str, amount: float, transaction_type: str):
    # Implementation here
    pass
```

### 5.2 Caching Strategy

```python
# utils/cache.py
import redis
import json
from typing import Optional

class AICache:
    def __init__(self, redis_client):
        self.redis = redis_client
        self.ttl = 3600  # 1 hour
    
    def get_categorization(self, description: str) -> Optional[dict]:
        key = f"categorization:{hash(description.lower())}"
        cached = self.redis.get(key)
        if cached:
            return json.loads(cached)
        return None
    
    def set_categorization(self, description: str, result: dict):
        key = f"categorization:{hash(description.lower())}"
        self.redis.setex(key, self.ttl, json.dumps(result))
```

## 6. Testing Strategy

### 6.1 Test Data

```python
# tests/test_data.py
ZAMBIAN_TRANSACTION_SAMPLES = [
    {
        "description": "Payment to ZESCO for electricity bill",
        "amount": 450.00,
        "type": "EXPENSE",
        "expected_category": "Utilities",
        "min_confidence": 0.90
    },
    {
        "description": "MTN airtime purchase",
        "amount": 50.00,
        "type": "EXPENSE", 
        "expected_category": "Telecommunications",
        "min_confidence": 0.85
    },
    {
        "description": "Catering service for wedding",
        "amount": 2500.00,
        "type": "INCOME",
        "expected_category": "Service Revenue",
        "min_confidence": 0.80
    }
]
```

### 6.2 Performance Benchmarks

```python
PERFORMANCE_REQUIREMENTS = {
    "categorization_latency": 2.0,  # seconds
    "intent_recognition_latency": 1.5,  # seconds
    "accuracy_threshold": 0.85,
    "availability": 0.99
}
```

This implementation guide provides the foundation for building a robust, Zambian-context-aware AI service that can handle the specific needs of IntelliFin users while maintaining high performance and reliability.
