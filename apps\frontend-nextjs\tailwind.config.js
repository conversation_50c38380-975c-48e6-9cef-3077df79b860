/** @type {import('tailwindcss').Config} */
module.exports = {
  darkMode: ["class"],
  content: [
    './pages/**/*.{ts,tsx}',
    './components/**/*.{ts,tsx}',
    './app/**/*.{ts,tsx}',
    './src/**/*.{ts,tsx}',
  ],
  prefix: "",
  theme: {
    container: {
      center: true,
      padding: "2rem",
      screens: {
        "2xl": "1400px",
      },
    },
    extend: {
      fontFamily: {
        sans: ['Inter', 'system-ui', 'sans-serif'],
      },
      colors: {
        border: "hsl(var(--border))",
        input: "hsl(var(--input))",
        ring: "hsl(var(--ring))",
        background: "hsl(var(--background))",
        foreground: "hsl(var(--foreground))",
        primary: {
          DEFAULT: "#007BFF",
          foreground: "#FFFFFF",
          50: "#E6F3FF",
          100: "#CCE7FF",
          500: "#007BFF",
          600: "#0056B3",
          700: "#004085",
        },
        secondary: {
          DEFAULT: "#28A745",
          foreground: "#FFFFFF",
          50: "#E8F5E8",
          100: "#D4EDDA",
          500: "#28A745",
          600: "#1E7E34",
          700: "#155724",
        },
        dark: {
          DEFAULT: "#2A4E8D",
          foreground: "#FFFFFF",
          50: "#F0F4F8",
          100: "#E1E9F0",
          500: "#2A4E8D",
          600: "#1E3A6F",
          700: "#152951",
        },
        destructive: {
          DEFAULT: "hsl(var(--destructive))",
          foreground: "hsl(var(--destructive-foreground))",
        },
        muted: {
          DEFAULT: "hsl(var(--muted))",
          foreground: "hsl(var(--muted-foreground))",
        },
        accent: {
          DEFAULT: "#00E0FF", // AI/Sparkle teal
          foreground: "#FFFFFF",
          50: "#E6FCFF",
          100: "#CCF9FF",
          500: "#00E0FF",
          600: "#00B8CC",
          700: "#008A99",
        },
        highlight: {
          DEFAULT: "#FFB74D", // Orange highlight
          foreground: "#FFFFFF",
          50: "#FFF3E0",
          100: "#FFE7C2",
          500: "#FFB74D",
          600: "#FF9800",
          700: "#F57C00",
        },
        popover: {
          DEFAULT: "hsl(var(--popover))",
          foreground: "hsl(var(--popover-foreground))",
        },
        card: {
          DEFAULT: "hsl(var(--card))",
          foreground: "hsl(var(--card-foreground))",
        },
      },
      borderRadius: {
        lg: "var(--radius)",
        md: "calc(var(--radius) - 2px)",
        sm: "calc(var(--radius) - 4px)",
      },
      keyframes: {
        "accordion-down": {
          from: { height: "0" },
          to: { height: "var(--radix-accordion-content-height)" },
        },
        "accordion-up": {
          from: { height: "var(--radix-accordion-content-height)" },
          to: { height: "0" },
        },
        "fade-in-up": {
          "0%": {
            opacity: "0",
            transform: "translateY(20px)",
          },
          "100%": {
            opacity: "1",
            transform: "translateY(0)",
          },
        },
      },
      animation: {
        "accordion-down": "accordion-down 0.2s ease-out",
        "accordion-up": "accordion-up 0.2s ease-out",
        "fade-in-up": "fade-in-up 0.6s ease-out",
      },
      backgroundImage: {
        'gradient-primary': 'linear-gradient(135deg, #007BFF 0%, #2A4E8D 100%)',
        'gradient-primary-light': 'linear-gradient(135deg, #007BFF 0%, #4A90E2 100%)',
        'gradient-secondary': 'linear-gradient(135deg, #28A745 0%, #00E0FF 100%)',
        'gradient-accent': 'linear-gradient(135deg, #00E0FF 0%, #007BFF 100%)',
        'gradient-highlight': 'linear-gradient(135deg, #FFB74D 0%, #FF9800 100%)',
        'gradient-hero': 'linear-gradient(135deg, #007BFF 0%, #2A4E8D 50%, #1E3A6F 100%)',
        'gradient-hero-alt': 'linear-gradient(135deg, #007BFF 0%, #00E0FF 30%, #2A4E8D 70%, #1E3A6F 100%)',
        'gradient-card': 'linear-gradient(135deg, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0.05) 100%)',
      },
      backgroundSize: {
        'grid': '40px 40px',
      },
    },
  },
  plugins: [require("tailwindcss-animate")],
}
