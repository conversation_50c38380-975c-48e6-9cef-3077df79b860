# Story 3.1: Transaction List Display with AI Categorization

**Epic:** Transaction Management & AI Categorization  
**Status:** Ready for Development  
**Priority:** High  
**Story Points:** 12

## User Story

**As a** user  
**I want** to see my transactions automatically categorized by AI with the ability to review and adjust  
**So that** I can quickly understand my financial activity without manual categorization

## Acceptance Criteria

- [ ] Transaction list displays all user transactions with AI-suggested categories
- [ ] AI categorization is applied automatically to new transactions
- [ ] Users can see confidence scores for AI categorizations
- [ ] Users can approve or reject AI suggestions with single-click actions
- [ ] Users can manually recategorize transactions from predefined category list
- [ ] Transaction list shows pending categorization status clearly
- [ ] AI explanations are provided for categorization decisions
- [ ] Bulk categorization actions are supported
- [ ] Error handling for AI service failures
- [ ] Graceful degradation when AI service is unavailable
- [ ] Transaction categorization events are processed through messaging abstraction layer
- [ ] Real-time updates work consistently across RabbitMQ (local) and Azure Service Bus (production)
- [ ] AI categorization events are published via messaging for asynchronous processing
- [ ] Failed categorization events are routed to dead-letter queues

## Technical Implementation

### Frontend Changes
- `src/components/financial/TransactionList.tsx` - Main transaction list component
- `src/components/financial/TransactionRow.tsx` - Individual transaction display
- `src/components/financial/CategorySelector.tsx` - Category selection dropdown
- `src/components/financial/AICategorizationBadge.tsx` - AI categorization indicator
- `src/hooks/useTransactions.ts` - Transaction data management
- `src/stores/financialStore.ts` - Transaction state management

### API Gateway Changes
- `src/main/java/com/intellifin/gateway/routes/TransactionController.java` - Transaction API routing
- `src/main/java/com/intellifin/gateway/middleware/TransactionCache.java` - Transaction caching

### Service Changes
- **Core Backend**: `src/main/java/com/intellifin/transactions/` - Transaction management with Spring Cloud Stream
- **Core Backend**: `src/main/java/com/intellifin/messaging/` - Transaction categorization event handling
- **AI Service**: `src/categorization.py` - AI-powered transaction categorization with MessagingService integration
- **AI Service**: `src/services/messaging_service.py` - Python messaging abstraction implementation
- **Database**: Transaction and category data models

### Database Changes
- Transaction table with AI categorization fields
- Category table with system and user-defined categories
- Categorization history for learning and audit trails

## API Contracts

```typescript
// Transaction management contracts
interface Transaction {
  id: string;
  userId: string;
  financialAccountId: string;
  date: string;
  description: string;
  amount: number;
  type: 'INCOME' | 'EXPENSE';
  categoryId?: string;
  aiCategoryId?: string;
  aiConfidence?: number;
  aiExplanation?: string;
  status: 'PENDING_CLASSIFICATION' | 'CLASSIFIED' | 'RECONCILED';
  source: string;
  createdAt: string;
  updatedAt: string;
}

interface CategorizationRequest {
  transactionId: string;
  categoryId: string;
  isAISuggestion: boolean;
  explanation?: string;
}

interface CategorizationResponse {
  success: boolean;
  transaction: Transaction;
  aiLearning?: {
    feedback: 'positive' | 'negative';
    confidence: number;
  };
}

interface TransactionAPI {
  GET /api/v1/transactions: {
    query: { page?: number; limit?: number; status?: string; };
    response: { transactions: Transaction[]; pagination: PaginationInfo; };
    errors: { 401: "Unauthorized", 500: "Server error" };
  };
  
  PUT /api/v1/transactions/{id}/categorize: {
    body: CategorizationRequest;
    response: CategorizationResponse;
    errors: { 400: "Invalid category", 404: "Transaction not found" };
  };
  
  POST /api/v1/transactions/bulk-categorize: {
    body: { transactions: CategorizationRequest[]; };
    response: { results: CategorizationResponse[]; };
    errors: { 400: "Invalid request", 500: "Processing error" };
  };
}

// Internal messaging events for transaction categorization (abstracted across RabbitMQ/Azure Service Bus)
interface TransactionCategorizationEvents {
  TransactionCategorizationRequested: {
    transactionId: string;
    description: string;
    amount: number;
    userId: string;
    timestamp: string;
  };
  TransactionCategorized: {
    transactionId: string;
    suggestedCategory: Category;
    confidence: number;
    explanation: string;
    processingTime: number;
  };
  CategorizationAccepted: {
    transactionId: string;
    categoryId: string;
    userAccepted: boolean;
    feedback?: string;
  };
  CategorizationRejected: {
    transactionId: string;
    rejectedCategoryId: string;
    newCategoryId?: string;
    reason?: string;
  };
  BulkCategorizationCompleted: {
    batchId: string;
    processedCount: number;
    successCount: number;
    failureCount: number;
  };
}
```

## Error Handling

- **AI Service Unavailable:** Show transactions without AI categorization via messaging dead-letter queues
- **Invalid Categories:** Clear error messages with valid category suggestions
- **Network Failures:** Retry mechanisms with user feedback
- **Bulk Operation Failures:** Partial success reporting with retry options via messaging events
- **Data Synchronization Issues:** Clear indicators for stale data
- **Messaging Broker Connectivity:** Issues handled by abstraction layer with automatic failover
- **Failed Categorization Events:** Routed to dead-letter queues (RabbitMQ DLX or Azure Service Bus DLQ)
- **Event Processing Failures:** Retry mechanisms with exponential backoff and manual intervention queues

## Definition of Done

- [ ] Transaction list displays with AI categorizations and confidence scores
- [ ] Users can approve/reject AI suggestions with single-click actions
- [ ] Manual categorization works with predefined category list
- [ ] AI explanations are displayed for categorization decisions
- [ ] Bulk categorization operations work correctly
- [ ] Error scenarios are handled gracefully with user-friendly messages
- [ ] Performance meets requirements (< 2 seconds for transaction list load)
- [ ] AI learning feedback is captured and processed
- [ ] Tests cover categorization workflows and error scenarios
- [ ] Service can be deployed independently
- [ ] No breaking changes to other services
- [ ] Real-time categorization updates function properly via messaging abstraction layer
- [ ] Categorization events work consistently across RabbitMQ (local) and Azure Service Bus (production)
- [ ] Dead-letter queue processing is implemented for failed categorization events
- [ ] Event-driven categorization workflows maintain business logic environment independence

## Dependencies

- [Story 2.1: Basic Conversational Command Processing](../epic-02/story-2.1-conversational-commands.md)
- [Story 2.2: Intent Recognition and Entity Extraction](../epic-02/story-2.2-intent-recognition.md)

## Notes

This story delivers the core "magic moment" of AI-powered transaction categorization. The AI must be accurate and the interface must be intuitive for users to trust and adopt the automated categorization feature.

---

**Related Stories:**
- [Story 3.2: Manual Transaction Entry with AI Assistance](story-3.2-manual-transaction-entry.md)

**Epic:** [Transaction Management & AI Categorization](../../epics-and-stories.md#epic-3-transaction-management--ai-categorization) 