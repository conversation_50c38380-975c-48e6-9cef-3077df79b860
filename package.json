{"name": "intellifin-monorepo", "version": "1.0.0", "description": "IntelliFin - Intelligent Financial Platform for Zambian SMEs", "private": true, "workspaces": ["apps/*", "libs/*"], "scripts": {"dev": "docker-compose up -d", "dev:logs": "docker-compose logs -f", "dev:down": "docker-compose down", "dev:clean": "docker-compose down -v && docker system prune -f", "build": "npm run build:libs && npm run build:apps", "build:libs": "npm run build --workspace=libs/data-models", "build:apps": "npm run build --workspace=apps/frontend-nextjs", "test": "npm run test:all", "test:all": "npm run test:frontend && npm run test:backend && npm run test:ai && npm run test:zra", "test:frontend": "npm run test --workspace=apps/frontend-nextjs", "test:backend": "cd apps/backend-java-core && ./mvnw test", "test:ai": "cd apps/backend-python-ai && poetry run pytest", "test:zra": "cd apps/backend-java-zra && ./mvnw test", "test:integration": "docker-compose -f docker-compose.test.yml up --abort-on-container-exit", "test:e2e": "npm run test:e2e --workspace=apps/frontend-nextjs", "lint": "npm run lint:frontend && npm run lint:ai", "lint:frontend": "npm run lint --workspace=apps/frontend-nextjs", "lint:ai": "cd apps/backend-python-ai && poetry run black --check src && poetry run isort --check-only src && poetry run flake8 src", "format": "npm run format:frontend && npm run format:ai", "format:frontend": "npm run lint:fix --workspace=apps/frontend-nextjs", "format:ai": "cd apps/backend-python-ai && poetry run black src && poetry run isort src", "health-check": "bash scripts/health-check.sh", "setup": "npm install && npm run build:libs", "clean": "npm run clean:deps && npm run clean:build", "clean:deps": "rm -rf node_modules apps/*/node_modules libs/*/node_modules", "clean:build": "rm -rf apps/*/dist apps/*/.next libs/*/dist", "docker:build": "docker-compose build", "docker:push": "docker-compose push", "release": "npm run test:all && npm run build && npm run docker:build"}, "devDependencies": {"@types/node": "^20.8.0", "concurrently": "^8.2.0", "cross-env": "^7.0.3", "husky": "^8.0.3", "lint-staged": "^15.0.0", "prettier": "^3.0.0", "typescript": "^5.2.0"}, "engines": {"node": ">=18.0.0", "npm": ">=9.0.0"}, "repository": {"type": "git", "url": "https://github.com/intellifin/intellifin.git"}, "keywords": ["fintech", "zambia", "sme", "ai", "accounting", "mobile-money", "zra", "invoicing"], "author": "IntelliFin Team", "license": "MIT", "husky": {"hooks": {"pre-commit": "lint-staged", "pre-push": "npm run test:all"}}, "lint-staged": {"apps/frontend-nextjs/**/*.{js,jsx,ts,tsx}": ["npm run lint:fix --workspace=apps/frontend-nextjs", "git add"], "apps/backend-python-ai/src/**/*.py": ["cd apps/backend-python-ai && poetry run black", "cd apps/backend-python-ai && poetry run isort", "git add"]}}