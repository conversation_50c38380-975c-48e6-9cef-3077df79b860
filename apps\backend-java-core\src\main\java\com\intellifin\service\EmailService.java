package com.intellifin.service;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
@Slf4j
public class EmailService {

    @Value("${app.frontend.url:http://localhost:3000}")
    private String frontendUrl;

    @Value("${app.email.from:<EMAIL>}")
    private String fromEmail;

    @Value("${app.email.enabled:false}")
    private boolean emailEnabled;

    public void sendEmailVerification(String email, String fullName, String token) {
        log.info("Sending email verification to: {}", email);
        
        if (!emailEnabled) {
            log.warn("Email service is disabled. Verification email not sent to: {}", email);
            logEmailContent("Email Verification", email, fullName, 
                "Please verify your email by clicking: " + frontendUrl + "/verify-email?token=" + token);
            return;
        }

        try {
            String subject = "Verify your IntelliFin account";
            String verificationUrl = frontendUrl + "/verify-email?token=" + token;
            
            String htmlContent = buildEmailVerificationTemplate(fullName, verificationUrl);
            
            // TODO: Implement actual email sending (SMTP, SendGrid, etc.)
            // For now, log the email content for development
            logEmailContent(subject, email, fullName, htmlContent);
            
            log.info("Email verification sent successfully to: {}", email);
            
        } catch (Exception e) {
            log.error("Failed to send email verification to: {}", email, e);
            // Don't throw exception to prevent registration failure
        }
    }

    public void sendPasswordReset(String email, String fullName, String token) {
        log.info("Sending password reset email to: {}", email);
        
        if (!emailEnabled) {
            log.warn("Email service is disabled. Password reset email not sent to: {}", email);
            logEmailContent("Password Reset", email, fullName, 
                "Reset your password by clicking: " + frontendUrl + "/reset-password?token=" + token);
            return;
        }

        try {
            String subject = "Reset your IntelliFin password";
            String resetUrl = frontendUrl + "/reset-password?token=" + token;
            
            String htmlContent = buildPasswordResetTemplate(fullName, resetUrl);
            
            // TODO: Implement actual email sending
            logEmailContent(subject, email, fullName, htmlContent);
            
            log.info("Password reset email sent successfully to: {}", email);
            
        } catch (Exception e) {
            log.error("Failed to send password reset email to: {}", email, e);
            // Don't throw exception
        }
    }

    public void sendWelcomeEmail(String email, String fullName) {
        log.info("Sending welcome email to: {}", email);
        
        if (!emailEnabled) {
            log.warn("Email service is disabled. Welcome email not sent to: {}", email);
            return;
        }

        try {
            String subject = "Welcome to IntelliFin!";
            String htmlContent = buildWelcomeTemplate(fullName);
            
            logEmailContent(subject, email, fullName, htmlContent);
            
            log.info("Welcome email sent successfully to: {}", email);
            
        } catch (Exception e) {
            log.error("Failed to send welcome email to: {}", email, e);
        }
    }

    private String buildEmailVerificationTemplate(String fullName, String verificationUrl) {
        return String.format("""
            <!DOCTYPE html>
            <html>
            <head>
                <meta charset="UTF-8">
                <title>Verify Your Email - IntelliFin</title>
            </head>
            <body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333;">
                <div style="max-width: 600px; margin: 0 auto; padding: 20px;">
                    <div style="text-align: center; margin-bottom: 30px;">
                        <h1 style="color: #2563eb;">IntelliFin</h1>
                        <p style="color: #666;">Intelligent Financial Platform for Zambian SMEs</p>
                    </div>
                    
                    <h2>Welcome to IntelliFin, %s!</h2>
                    
                    <p>Thank you for registering with IntelliFin. To complete your account setup, please verify your email address by clicking the button below:</p>
                    
                    <div style="text-align: center; margin: 30px 0;">
                        <a href="%s" style="background-color: #2563eb; color: white; padding: 12px 30px; text-decoration: none; border-radius: 5px; display: inline-block;">
                            Verify Email Address
                        </a>
                    </div>
                    
                    <p>If the button doesn't work, you can copy and paste this link into your browser:</p>
                    <p style="word-break: break-all; color: #2563eb;">%s</p>
                    
                    <p><strong>This link will expire in 24 hours.</strong></p>
                    
                    <hr style="margin: 30px 0; border: none; border-top: 1px solid #eee;">
                    
                    <p style="font-size: 14px; color: #666;">
                        If you didn't create an account with IntelliFin, please ignore this email.
                    </p>
                    
                    <p style="font-size: 14px; color: #666;">
                        Best regards,<br>
                        The IntelliFin Team
                    </p>
                </div>
            </body>
            </html>
            """, fullName, verificationUrl, verificationUrl);
    }

    private String buildPasswordResetTemplate(String fullName, String resetUrl) {
        return String.format("""
            <!DOCTYPE html>
            <html>
            <head>
                <meta charset="UTF-8">
                <title>Reset Your Password - IntelliFin</title>
            </head>
            <body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333;">
                <div style="max-width: 600px; margin: 0 auto; padding: 20px;">
                    <div style="text-align: center; margin-bottom: 30px;">
                        <h1 style="color: #2563eb;">IntelliFin</h1>
                    </div>
                    
                    <h2>Password Reset Request</h2>
                    
                    <p>Hello %s,</p>
                    
                    <p>We received a request to reset your password for your IntelliFin account. Click the button below to reset your password:</p>
                    
                    <div style="text-align: center; margin: 30px 0;">
                        <a href="%s" style="background-color: #dc2626; color: white; padding: 12px 30px; text-decoration: none; border-radius: 5px; display: inline-block;">
                            Reset Password
                        </a>
                    </div>
                    
                    <p>If the button doesn't work, you can copy and paste this link into your browser:</p>
                    <p style="word-break: break-all; color: #dc2626;">%s</p>
                    
                    <p><strong>This link will expire in 1 hour.</strong></p>
                    
                    <p>If you didn't request a password reset, please ignore this email. Your password will remain unchanged.</p>
                    
                    <hr style="margin: 30px 0; border: none; border-top: 1px solid #eee;">
                    
                    <p style="font-size: 14px; color: #666;">
                        Best regards,<br>
                        The IntelliFin Team
                    </p>
                </div>
            </body>
            </html>
            """, fullName, resetUrl, resetUrl);
    }

    private String buildWelcomeTemplate(String fullName) {
        return String.format("""
            <!DOCTYPE html>
            <html>
            <head>
                <meta charset="UTF-8">
                <title>Welcome to IntelliFin!</title>
            </head>
            <body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333;">
                <div style="max-width: 600px; margin: 0 auto; padding: 20px;">
                    <div style="text-align: center; margin-bottom: 30px;">
                        <h1 style="color: #2563eb;">IntelliFin</h1>
                        <p style="color: #666;">Intelligent Financial Platform for Zambian SMEs</p>
                    </div>
                    
                    <h2>Welcome to IntelliFin, %s! 🎉</h2>
                    
                    <p>Your email has been verified and your account is now active. You're ready to start managing your business finances with AI-powered insights!</p>
                    
                    <h3>What's Next?</h3>
                    <ul>
                        <li>Complete your business profile</li>
                        <li>Connect your MTN Mobile Money account</li>
                        <li>Start categorizing your transactions with AI</li>
                        <li>Create your first ZRA-compliant invoice</li>
                    </ul>
                    
                    <div style="text-align: center; margin: 30px 0;">
                        <a href="%s/dashboard" style="background-color: #16a34a; color: white; padding: 12px 30px; text-decoration: none; border-radius: 5px; display: inline-block;">
                            Get Started
                        </a>
                    </div>
                    
                    <p>If you have any questions, our support team is here to help!</p>
                    
                    <hr style="margin: 30px 0; border: none; border-top: 1px solid #eee;">
                    
                    <p style="font-size: 14px; color: #666;">
                        Best regards,<br>
                        The IntelliFin Team
                    </p>
                </div>
            </body>
            </html>
            """, fullName, frontendUrl);
    }

    private void logEmailContent(String subject, String email, String fullName, String content) {
        log.info("""
            
            =============== EMAIL CONTENT ===============
            To: {} ({})
            Subject: {}
            Content: {}
            =============================================
            """, email, fullName, subject, content);
    }
}
