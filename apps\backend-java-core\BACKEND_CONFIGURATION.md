# Backend Configuration Guide

## Overview
This guide covers the complete configuration of the IntelliFin backend for authentication integration with the frontend.

## ✅ Completed Backend Features

### 1. Authentication Endpoints
- **POST** `/api/v1/auth/register` - User registration
- **POST** `/api/v1/auth/login` - User login
- **POST** `/api/v1/auth/refresh` - Token refresh ✨ **NEW**
- **POST** `/api/v1/auth/logout` - User logout ✨ **NEW**
- **GET** `/api/v1/auth/validate-token` - Token validation
- **POST** `/api/v1/auth/verify-email` - Email verification
- **POST** `/api/v1/auth/resend-verification` - Resend verification email
- **POST** `/api/v1/auth/forgot-password` - Request password reset
- **POST** `/api/v1/auth/reset-password` - Reset password with token

### 2. OAuth Endpoints (Placeholder)
- **GET** `/api/v1/auth/oauth/google` - Initiate Google OAuth ✨ **NEW**
- **POST** `/api/v1/auth/oauth/google/callback` - Handle Google OAuth callback ✨ **NEW**
- **GET** `/api/v1/auth/oauth/apple` - Initiate Apple OAuth ✨ **NEW**
- **POST** `/api/v1/auth/oauth/apple/callback` - Handle Apple OAuth callback ✨ **NEW**

### 3. Security Configuration
- JWT token generation and validation
- CORS configuration for frontend integration
- Password encryption with BCrypt
- Account lockout protection
- Request/response logging

### 4. Database Schema
- User entity with authentication fields
- Email verification tokens
- Password reset tokens
- Account lockout management

## 🔧 Environment Configuration

### Required Environment Variables

Create a `.env` file in the project root with the following variables:

```env
# Database Configuration
DATABASE_URL=***********************************************
DATABASE_USERNAME=intellifin_user
DATABASE_PASSWORD=intellifin_dev_password

# Redis Configuration
REDIS_URL=redis://localhost:6379

# JWT Configuration
JWT_SECRET=your-secure-256-bit-jwt-secret-key-here
JWT_EXPIRATION=86400

# Frontend Configuration
FRONTEND_URL=http://localhost:3000

# Email Configuration
EMAIL_ENABLED=false
EMAIL_FROM=<EMAIL>
SMTP_HOST=localhost
SMTP_PORT=587
SMTP_USERNAME=
SMTP_PASSWORD=
SMTP_AUTH=true
SMTP_STARTTLS=true

# OAuth Configuration
GOOGLE_CLIENT_ID=your-google-client-id
GOOGLE_CLIENT_SECRET=your-google-client-secret
APPLE_CLIENT_ID=your-apple-client-id
APPLE_CLIENT_SECRET=your-apple-client-secret

# External Services
AI_SERVICE_URL=http://localhost:8000
ZRA_SERVICE_URL=http://localhost:8081
```

### Frontend Environment Variables

Update your frontend `.env.local` file:

```env
NEXT_PUBLIC_API_URL=http://localhost:8080
NEXT_PUBLIC_GOOGLE_CLIENT_ID=your-google-client-id
NEXT_PUBLIC_APPLE_CLIENT_ID=your-apple-client-id
```

## 🚀 Quick Start

### 1. Start the Backend Services

```bash
# Start all services with Docker Compose
docker-compose up -d

# Or start just the backend dependencies
docker-compose up -d postgres redis

# Then run the backend locally
cd apps/backend-java-core
./mvnw spring-boot:run
```

### 2. Verify Backend is Running

```bash
# Health check
curl http://localhost:8080/actuator/health

# API documentation
open http://localhost:8080/swagger-ui.html
```

### 3. Test Authentication Endpoints

```bash
# Test registration
curl -X POST http://localhost:8080/api/v1/auth/register \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "SecurePass123!",
    "confirmPassword": "SecurePass123!",
    "firstName": "Test",
    "lastName": "User",
    "organizationName": "Test Org"
  }'

# Test login
curl -X POST http://localhost:8080/api/v1/auth/login \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "SecurePass123!"
  }'
```

## 📧 Email Service Setup

### Development (Console Logging)
By default, emails are logged to the console for development:

```yaml
app:
  email:
    enabled: false  # Emails logged to console
```

### Production Email Setup

#### Option 1: SMTP Configuration
```yaml
app:
  email:
    enabled: true
    smtp:
      host: smtp.gmail.com
      port: 587
      username: <EMAIL>
      password: your-app-password
      auth: true
      starttls: true
```

#### Option 2: SendGrid Integration
```yaml
app:
  email:
    enabled: true
    provider: sendgrid
    sendgrid:
      api-key: your-sendgrid-api-key
```

## 🔐 OAuth Setup

### Google OAuth Setup

1. **Create Google Cloud Project**
   - Go to [Google Cloud Console](https://console.cloud.google.com/)
   - Create a new project or select existing one
   - Enable Google+ API

2. **Configure OAuth Consent Screen**
   - Go to APIs & Services > OAuth consent screen
   - Fill in application details
   - Add authorized domains

3. **Create OAuth Credentials**
   - Go to APIs & Services > Credentials
   - Create OAuth 2.0 Client ID
   - Add authorized redirect URIs:
     - `http://localhost:8080/api/v1/auth/oauth/google/callback` (development)
     - `https://yourdomain.com/api/v1/auth/oauth/google/callback` (production)

4. **Update Environment Variables**
   ```env
   GOOGLE_CLIENT_ID=your-google-client-id
   GOOGLE_CLIENT_SECRET=your-google-client-secret
   NEXT_PUBLIC_GOOGLE_CLIENT_ID=your-google-client-id
   ```

### Apple OAuth Setup

1. **Apple Developer Account**
   - Sign up for Apple Developer Program
   - Create App ID in Certificates, Identifiers & Profiles

2. **Configure Sign in with Apple**
   - Enable Sign in with Apple capability
   - Configure return URLs:
     - `http://localhost:8080/api/v1/auth/oauth/apple/callback` (development)
     - `https://yourdomain.com/api/v1/auth/oauth/apple/callback` (production)

3. **Update Environment Variables**
   ```env
   APPLE_CLIENT_ID=your-apple-client-id
   APPLE_CLIENT_SECRET=your-apple-client-secret
   NEXT_PUBLIC_APPLE_CLIENT_ID=your-apple-client-id
   ```

## 🧪 Testing the Integration

### 1. Run Integration Tests

```bash
cd apps/backend-java-core
./mvnw test
```

### 2. Test Frontend Integration

```bash
cd apps/frontend-nextjs
npm run dev

# Open browser and test:
# - Registration flow
# - Login flow
# - Forgot password flow
# - Token refresh (check network tab)
```

### 3. Manual API Testing

Use the provided Postman collection or test with curl:

```bash
# Test token refresh
curl -X POST http://localhost:8080/api/v1/auth/refresh \
  -H "Authorization: Bearer your-jwt-token"

# Test logout
curl -X POST http://localhost:8080/api/v1/auth/logout \
  -H "Authorization: Bearer your-jwt-token"
```

## 🔍 Troubleshooting

### Common Issues

1. **CORS Errors**
   - Verify frontend URL in CORS configuration
   - Check `FRONTEND_URL` environment variable

2. **JWT Token Issues**
   - Ensure `JWT_SECRET` is at least 256 bits
   - Check token expiration settings

3. **Database Connection**
   - Verify PostgreSQL is running
   - Check database credentials

4. **Email Not Sending**
   - Check `EMAIL_ENABLED` setting
   - Verify SMTP configuration
   - Check application logs

### Logs and Monitoring

```bash
# View application logs
docker-compose logs -f backend-core

# Check database logs
docker-compose logs -f postgres

# Monitor Redis
docker-compose logs -f redis
```

## 📝 Next Steps

1. **Implement OAuth Providers**
   - Complete Google OAuth implementation
   - Complete Apple OAuth implementation

2. **Email Service Integration**
   - Set up production email service
   - Create email templates

3. **Security Enhancements**
   - Implement rate limiting
   - Add CAPTCHA for registration
   - Set up monitoring and alerting

4. **Production Deployment**
   - Configure HTTPS
   - Set up load balancing
   - Configure monitoring

## 📞 Support

For backend configuration issues:
- Check application logs: `docker-compose logs backend-core`
- Review configuration: `apps/backend-java-core/src/main/resources/application.yml`
- Test endpoints: `http://localhost:8080/swagger-ui.html`
- Database issues: Check PostgreSQL logs and connection settings
