// Financial Dashboard Types
export interface DashboardData {
  summary: FinancialSummary;
  recentTransactions: Transaction[];
  pendingInvoices: Invoice[];
  accountBalances: AccountBalance[];
  alerts: FinancialAlert[];
  insights: BusinessInsight[];
}

export interface FinancialAlert {
  id: string;
  type: 'LOW_BALANCE' | 'OVERDUE_INVOICE' | 'LARGE_EXPENSE' | 'UNUSUAL_ACTIVITY' | 'COMPLIANCE_REMINDER';
  severity: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL';
  title: string;
  message: string;
  actionRequired: boolean;
  actionText?: string;
  actionUrl?: string;
  createdAt: string;
  dismissible: boolean;
}

export interface BusinessInsight {
  id: string;
  type: 'REVENUE_TREND' | 'EXPENSE_PATTERN' | 'PROFITABILITY' | 'CASH_FLOW' | 'GROWTH_OPPORTUNITY';
  title: string;
  description: string;
  impact: 'POSITIVE' | 'NEGATIVE' | 'NEUTRAL';
  confidence: number;
  data?: any;
  recommendations: string[];
  createdAt: string;
}

// Chart and Visualization Types
export interface ChartData {
  labels: string[];
  datasets: ChartDataset[];
}

export interface ChartDataset {
  label: string;
  data: number[];
  backgroundColor?: string | string[];
  borderColor?: string | string[];
  borderWidth?: number;
  fill?: boolean;
}

export interface TimeSeriesData {
  date: string;
  value: number;
  category?: string;
}

export interface CategoryBreakdownData {
  category: string;
  amount: number;
  percentage: number;
  color: string;
}

// Financial Metrics
export interface FinancialMetrics {
  revenue: {
    current: number;
    previous: number;
    change: number;
    changePercentage: number;
  };
  expenses: {
    current: number;
    previous: number;
    change: number;
    changePercentage: number;
  };
  profit: {
    current: number;
    previous: number;
    change: number;
    changePercentage: number;
  };
  cashFlow: {
    current: number;
    previous: number;
    change: number;
    changePercentage: number;
  };
}

// Transaction Analysis
export interface TransactionAnalysis {
  totalTransactions: number;
  averageTransactionAmount: number;
  largestTransaction: Transaction;
  mostFrequentCategory: Category;
  trends: {
    daily: TimeSeriesData[];
    weekly: TimeSeriesData[];
    monthly: TimeSeriesData[];
  };
  patterns: TransactionPattern[];
}

export interface TransactionPattern {
  type: 'RECURRING' | 'SEASONAL' | 'ANOMALY';
  description: string;
  frequency: string;
  amount: number;
  confidence: number;
  examples: Transaction[];
}

// Invoice Analytics
export interface InvoiceAnalytics {
  totalInvoices: number;
  totalValue: number;
  averageValue: number;
  paymentMetrics: {
    onTime: number;
    late: number;
    overdue: number;
    averageDaysToPayment: number;
  };
  clientMetrics: {
    topClients: ClientMetric[];
    newClients: number;
    repeatClients: number;
  };
}

export interface ClientMetric {
  client: Client;
  totalInvoices: number;
  totalValue: number;
  averagePaymentDays: number;
  paymentReliability: 'EXCELLENT' | 'GOOD' | 'FAIR' | 'POOR';
}

// Account Health
export interface AccountHealth {
  accountId: string;
  accountName: string;
  healthScore: number; // 0-100
  status: 'HEALTHY' | 'WARNING' | 'CRITICAL';
  issues: AccountIssue[];
  recommendations: string[];
  lastSync: string;
  syncFrequency: string;
}

export interface AccountIssue {
  type: 'SYNC_ERROR' | 'LOW_BALANCE' | 'UNUSUAL_ACTIVITY' | 'MISSING_TRANSACTIONS';
  severity: 'LOW' | 'MEDIUM' | 'HIGH';
  description: string;
  resolution?: string;
}

// Financial Goals and Budgets
export interface FinancialGoal {
  id: string;
  userId: string;
  type: 'REVENUE' | 'PROFIT' | 'SAVINGS' | 'EXPENSE_REDUCTION';
  title: string;
  description: string;
  targetAmount: number;
  currentAmount: number;
  targetDate: string;
  status: 'ON_TRACK' | 'BEHIND' | 'AHEAD' | 'COMPLETED';
  progress: number; // 0-100
  createdAt: string;
  updatedAt: string;
}

export interface Budget {
  id: string;
  userId: string;
  name: string;
  period: 'MONTHLY' | 'QUARTERLY' | 'YEARLY';
  startDate: string;
  endDate: string;
  categories: BudgetCategory[];
  totalBudget: number;
  totalSpent: number;
  status: 'ACTIVE' | 'EXCEEDED' | 'COMPLETED';
  createdAt: string;
  updatedAt: string;
}

export interface BudgetCategory {
  categoryId: string;
  categoryName: string;
  budgetAmount: number;
  spentAmount: number;
  remainingAmount: number;
  status: 'ON_TRACK' | 'WARNING' | 'EXCEEDED';
}

// Tax and Compliance
export interface TaxSummary {
  period: string;
  totalTaxableIncome: number;
  totalTaxableExpenses: number;
  estimatedTax: number;
  vatCollected: number;
  vatPaid: number;
  netVat: number;
  complianceStatus: 'COMPLIANT' | 'PENDING' | 'OVERDUE';
  nextDeadline: string;
  requiredActions: string[];
}

export interface ZRASubmission {
  id: string;
  invoiceId: string;
  submissionDate: string;
  status: 'PENDING' | 'SUBMITTED' | 'ACCEPTED' | 'REJECTED';
  zraInvoiceId?: string;
  errorMessage?: string;
  retryCount: number;
  nextRetry?: string;
}

// Mobile Money Integration
export interface MobileMoneyAccount {
  id: string;
  provider: 'MTN' | 'AIRTEL' | 'ZAMTEL';
  phoneNumber: string;
  accountName: string;
  balance: number;
  currency: string;
  status: 'ACTIVE' | 'INACTIVE' | 'SUSPENDED';
  lastSync: string;
  syncEnabled: boolean;
  transactionCount: number;
}

export interface MobileMoneyTransaction {
  id: string;
  accountId: string;
  externalId: string;
  type: 'SEND' | 'RECEIVE' | 'DEPOSIT' | 'WITHDRAWAL' | 'PAYMENT';
  amount: number;
  fee: number;
  netAmount: number;
  counterparty: string;
  description: string;
  timestamp: string;
  status: 'COMPLETED' | 'PENDING' | 'FAILED';
  reference: string;
}

// Re-export common types
export type {
  Transaction,
  Category,
  FinancialAccount,
  Invoice,
  Client,
  FinancialSummary,
  CategorySummary,
  AccountBalance
} from './api';
