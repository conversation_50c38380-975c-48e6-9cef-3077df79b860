# PowerShell script to validate the ConversationRepository test syntax
# This script performs basic validation of the test file structure

Write-Host "🔍 ConversationRepository Test Validation" -ForegroundColor Cyan
Write-Host "=========================================" -ForegroundColor Cyan

$TestFile = "apps/backend-java-core/src/test/java/com/intellifin/repository/ConversationRepositoryTest.java"
$ConfigFile = "apps/backend-java-core/src/test/resources/application-test.yml"

# Check if test file exists
if (-not (Test-Path $TestFile)) {
    Write-Host "❌ Test file not found: $TestFile" -ForegroundColor Red
    exit 1
}

Write-Host "✅ Test file found: $TestFile" -ForegroundColor Green

# Check if config file exists
if (-not (Test-Path $ConfigFile)) {
    Write-Host "❌ Config file not found: $ConfigFile" -ForegroundColor Red
    exit 1
}

Write-Host "✅ Config file found: $ConfigFile" -ForegroundColor Green

# Read and analyze test file
$content = Get-Content $TestFile -Raw

# Basic syntax checks
$checks = @(
    @{ Name = "Package declaration"; Pattern = "package com\.intellifin\.repository;" },
    @{ Name = "Class declaration"; Pattern = "class ConversationRepositoryTest" },
    @{ Name = "DataJpaTest annotation"; Pattern = "@DataJpaTest" },
    @{ Name = "ActiveProfiles annotation"; Pattern = "@ActiveProfiles" },
    @{ Name = "TestEntityManager injection"; Pattern = "@Autowired.*TestEntityManager" },
    @{ Name = "ConversationRepository injection"; Pattern = "@Autowired.*ConversationRepository" },
    @{ Name = "Setup method"; Pattern = "@BeforeEach.*void setUp" },
    @{ Name = "Nested test classes"; Pattern = "@Nested" },
    @{ Name = "Test methods"; Pattern = "@Test.*void" }
)

Write-Host ""
Write-Host "🔍 Performing syntax validation..." -ForegroundColor Yellow

$allPassed = $true

foreach ($check in $checks) {
    if ($content -match $check.Pattern) {
        Write-Host "✅ $($check.Name)" -ForegroundColor Green
    }
    else {
        Write-Host "❌ $($check.Name)" -ForegroundColor Red
        $allPassed = $false
    }
}

# Count test methods
$testMethods = ([regex]::Matches($content, "@Test")).Count
Write-Host ""
Write-Host "📊 Test Statistics:" -ForegroundColor Cyan
Write-Host "   • Total @Test methods: $testMethods" -ForegroundColor Gray

# Count nested classes
$nestedClasses = ([regex]::Matches($content, "@Nested")).Count
Write-Host "   • Nested test classes: $nestedClasses" -ForegroundColor Gray

# Check for common patterns
$patterns = @(
    @{ Name = "AssertJ assertions"; Pattern = "assertThat" },
    @{ Name = "Given-When-Then comments"; Pattern = "// Given" },
    @{ Name = "DisplayName annotations"; Pattern = "@DisplayName" },
    @{ Name = "Builder pattern usage"; Pattern = "\.builder" }
)

Write-Host ""
Write-Host "🎯 Pattern Analysis:" -ForegroundColor Cyan

foreach ($pattern in $patterns) {
    $count = ([regex]::Matches($content, $pattern.Pattern)).Count
    if ($count -gt 0) {
        Write-Host "✅ $($pattern.Name): $count occurrences" -ForegroundColor Green
    }
    else {
        Write-Host "⚠️  $($pattern.Name): Not found" -ForegroundColor Yellow
    }
}

# Validate config file
Write-Host ""
Write-Host "🔧 Validating test configuration..." -ForegroundColor Yellow

$configContent = Get-Content $ConfigFile -Raw

$configChecks = @(
    @{ Name = "H2 database configuration"; Pattern = "jdbc:h2:mem:testdb" },
    @{ Name = "Test profile configuration"; Pattern = "spring:" },
    @{ Name = "JPA configuration"; Pattern = "jpa:" },
    @{ Name = "Hibernate DDL auto"; Pattern = "ddl-auto: create-drop" },
    @{ Name = "Flyway disabled"; Pattern = "enabled: false" }
)

foreach ($check in $configChecks) {
    if ($configContent -match $check.Pattern) {
        Write-Host "✅ $($check.Name)" -ForegroundColor Green
    }
    else {
        Write-Host "❌ $($check.Name)" -ForegroundColor Red
        $allPassed = $false
    }
}

# Final result
Write-Host ""
if ($allPassed) {
    Write-Host "🎉 All validations passed! The test suite appears to be properly structured." -ForegroundColor Green
    Write-Host "📋 Ready to run tests with: .\scripts\run-conversation-repository-tests.ps1" -ForegroundColor Cyan
}
else {
    Write-Host "⚠️  Some validations failed. Please review the test file and configuration." -ForegroundColor Yellow
}

Write-Host ""
Write-Host "📚 For detailed information, see: docs/testing/conversation-repository-qa-summary.md" -ForegroundColor Cyan
