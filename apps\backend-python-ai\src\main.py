"""
IntelliFin AI Service
FastAPI application for natural language processing and transaction categorization
"""

import time
from contextlib import asynccontextmanager
from typing import Dict, Any

from fastapi import <PERSON><PERSON><PERSON>, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse

from src.config.settings import get_settings
from src.models.schemas import HealthResponse

# Global startup time
startup_time = time.time()

@asynccontextmanager
async def lifespan(app: FastAPI):
    """Application lifespan manager"""
    # Startup
    print("🚀 IntelliFin AI Service starting up...")
    
    # Initialize services here
    # await initialize_ai_models()
    # await initialize_redis_connection()
    
    print("✅ IntelliFin AI Service ready!")
    
    yield
    
    # Shutdown
    print("🛑 IntelliFin AI Service shutting down...")
    
    # Cleanup here
    # await cleanup_connections()
    
    print("✅ IntelliFin AI Service shutdown complete!")

# Create FastAPI application
app = FastAPI(
    title="IntelliFin AI Service",
    description="AI service for natural language processing and transaction categorization",
    version="1.0.0",
    lifespan=lifespan,
    docs_url="/docs",
    redoc_url="/redoc",
)

# Get settings
settings = get_settings()

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:3000", "http://localhost:8080"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

@app.get("/health", response_model=HealthResponse)
async def health_check() -> HealthResponse:
    """Health check endpoint"""
    try:
        # Check Redis connection
        # redis_status = await check_redis_connection()
        
        # Check AI model availability
        # ai_model_status = await check_ai_model()
        
        return HealthResponse(
            status="healthy",
            service="intellifin-ai-service",
            timestamp=time.time(),
            version="1.0.0",
            uptime=time.time() - startup_time,
            environment=settings.environment,
        )
    except Exception as e:
        raise HTTPException(
            status_code=503,
            detail={
                "status": "unhealthy",
                "service": "intellifin-ai-service",
                "timestamp": time.time(),
                "error": str(e),
                "environment": settings.environment,
            }
        )

@app.get("/info")
async def service_info() -> Dict[str, Any]:
    """Service information endpoint"""
    return {
        "service": "intellifin-ai-service",
        "version": "1.0.0",
        "description": "AI service for IntelliFin platform",
        "environment": settings.environment,
        "python_version": "3.10+",
        "framework": "FastAPI",
        "ai_models": {
            "local": "Ollama Llama 3.1 8B",
            "production": "Google Gemini Pro",
        },
        "capabilities": [
            "transaction_categorization",
            "intent_recognition",
            "entity_extraction",
            "financial_explanations",
        ],
    }

@app.get("/")
async def root():
    """Root endpoint"""
    return {
        "message": "IntelliFin AI Service",
        "version": "1.0.0",
        "docs": "/docs",
        "health": "/health",
        "info": "/info",
    }

# Error handlers
@app.exception_handler(404)
async def not_found_handler(request, exc):
    return JSONResponse(
        status_code=404,
        content={
            "error": "Not Found",
            "message": "The requested endpoint was not found",
            "timestamp": time.time(),
            "path": str(request.url.path),
        }
    )

@app.exception_handler(500)
async def internal_error_handler(request, exc):
    return JSONResponse(
        status_code=500,
        content={
            "error": "Internal Server Error",
            "message": "An unexpected error occurred",
            "timestamp": time.time(),
            "path": str(request.url.path),
        }
    )

if __name__ == "__main__":
    import uvicorn
    
    uvicorn.run(
        "src.main:app",
        host="0.0.0.0",
        port=8000,
        reload=True if settings.environment == "development" else False,
        log_level="debug" if settings.environment == "development" else "info",
    )
