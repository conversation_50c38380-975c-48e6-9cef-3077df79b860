import React from 'react';
import { render, screen, waitFor, act } from '@testing-library/react';
import { useRouter } from 'next/navigation';
import { AuthProvider, useAuth } from '../AuthContext';

// Mock Next.js router
jest.mock('next/navigation', () => ({
  useRouter: jest.fn(),
}));

// Mock fetch
global.fetch = jest.fn();

const mockPush = jest.fn();
(useRouter as jest.Mock).mockReturnValue({
  push: mockPush,
});

// Test component that uses auth context
const TestComponent = () => {
  const { user, isAuthenticated, isLoading, login, register, logout } = useAuth();
  
  return (
    <div>
      <div data-testid="loading">{isLoading ? 'loading' : 'not-loading'}</div>
      <div data-testid="authenticated">{isAuthenticated ? 'authenticated' : 'not-authenticated'}</div>
      <div data-testid="user-email">{user?.email || 'no-user'}</div>
      <button onClick={() => login('<EMAIL>', 'password')} data-testid="login-btn">
        Login
      </button>
      <button onClick={() => register({
        email: '<EMAIL>',
        password: 'password',
        confirmPassword: 'password',
        firstName: 'Test',
        lastName: 'User'
      })} data-testid="register-btn">
        Register
      </button>
      <button onClick={logout} data-testid="logout-btn">
        Logout
      </button>
    </div>
  );
};

const renderWithAuth = () => {
  return render(
    <AuthProvider>
      <TestComponent />
    </AuthProvider>
  );
};

describe('AuthContext', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    localStorage.clear();
    (fetch as jest.Mock).mockClear();
  });

  it('should initialize with loading state', () => {
    renderWithAuth();
    
    expect(screen.getByTestId('loading')).toHaveTextContent('loading');
    expect(screen.getByTestId('authenticated')).toHaveTextContent('not-authenticated');
    expect(screen.getByTestId('user-email')).toHaveTextContent('no-user');
  });

  it('should load stored user data on mount', async () => {
    // Setup localStorage with user data
    const mockUser = {
      id: '123',
      email: '<EMAIL>',
      firstName: 'Test',
      lastName: 'User',
      onboardingStatus: 'ALL_SET',
      emailVerified: true
    };
    
    localStorage.setItem('intellifin_token', 'mock-token');
    localStorage.setItem('intellifin_user', JSON.stringify(mockUser));
    
    // Mock token validation
    (fetch as jest.Mock).mockResolvedValueOnce({
      ok: true,
      json: async () => ({ valid: true })
    });

    renderWithAuth();

    await waitFor(() => {
      expect(screen.getByTestId('loading')).toHaveTextContent('not-loading');
    });

    expect(screen.getByTestId('authenticated')).toHaveTextContent('authenticated');
    expect(screen.getByTestId('user-email')).toHaveTextContent('<EMAIL>');
  });

  it('should handle successful login', async () => {
    const mockAuthResponse = {
      token: 'new-token',
      user: {
        id: '123',
        email: '<EMAIL>',
        firstName: 'Test',
        lastName: 'User',
        onboardingStatus: 'ALL_SET',
        emailVerified: true
      }
    };

    (fetch as jest.Mock).mockResolvedValueOnce({
      ok: true,
      json: async () => mockAuthResponse
    });

    renderWithAuth();

    await waitFor(() => {
      expect(screen.getByTestId('loading')).toHaveTextContent('not-loading');
    });

    await act(async () => {
      screen.getByTestId('login-btn').click();
    });

    await waitFor(() => {
      expect(screen.getByTestId('authenticated')).toHaveTextContent('authenticated');
    });

    expect(screen.getByTestId('user-email')).toHaveTextContent('<EMAIL>');
    expect(localStorage.getItem('intellifin_token')).toBe('new-token');
    expect(mockPush).toHaveBeenCalledWith('/dashboard');
  });

  it('should handle login failure', async () => {
    (fetch as jest.Mock).mockResolvedValueOnce({
      ok: false,
      json: async () => ({ message: 'Invalid credentials' })
    });

    renderWithAuth();

    await waitFor(() => {
      expect(screen.getByTestId('loading')).toHaveTextContent('not-loading');
    });

    await expect(async () => {
      await act(async () => {
        screen.getByTestId('login-btn').click();
      });
    }).rejects.toThrow('Invalid credentials');

    expect(screen.getByTestId('authenticated')).toHaveTextContent('not-authenticated');
  });

  it('should handle successful registration', async () => {
    const mockAuthResponse = {
      token: 'new-token',
      user: {
        id: '123',
        email: '<EMAIL>',
        firstName: 'Test',
        lastName: 'User',
        onboardingStatus: 'STARTED',
        emailVerified: false
      }
    };

    (fetch as jest.Mock).mockResolvedValueOnce({
      ok: true,
      json: async () => mockAuthResponse
    });

    renderWithAuth();

    await waitFor(() => {
      expect(screen.getByTestId('loading')).toHaveTextContent('not-loading');
    });

    await act(async () => {
      screen.getByTestId('register-btn').click();
    });

    await waitFor(() => {
      expect(screen.getByTestId('authenticated')).toHaveTextContent('authenticated');
    });

    expect(mockPush).toHaveBeenCalledWith('/verify-email');
  });

  it('should handle logout', async () => {
    // Setup authenticated state
    const mockUser = {
      id: '123',
      email: '<EMAIL>',
      firstName: 'Test',
      lastName: 'User',
      onboardingStatus: 'ALL_SET',
      emailVerified: true
    };
    
    localStorage.setItem('intellifin_token', 'mock-token');
    localStorage.setItem('intellifin_user', JSON.stringify(mockUser));
    
    (fetch as jest.Mock).mockResolvedValueOnce({
      ok: true,
      json: async () => ({ valid: true })
    });

    renderWithAuth();

    await waitFor(() => {
      expect(screen.getByTestId('authenticated')).toHaveTextContent('authenticated');
    });

    await act(async () => {
      screen.getByTestId('logout-btn').click();
    });

    expect(screen.getByTestId('authenticated')).toHaveTextContent('not-authenticated');
    expect(screen.getByTestId('user-email')).toHaveTextContent('no-user');
    expect(localStorage.getItem('intellifin_token')).toBeNull();
    expect(localStorage.getItem('intellifin_user')).toBeNull();
    expect(mockPush).toHaveBeenCalledWith('/login');
  });

  it('should clear invalid stored token', async () => {
    localStorage.setItem('intellifin_token', 'invalid-token');
    localStorage.setItem('intellifin_user', JSON.stringify({ email: '<EMAIL>' }));
    
    // Mock token validation failure
    (fetch as jest.Mock).mockResolvedValueOnce({
      ok: false
    });

    renderWithAuth();

    await waitFor(() => {
      expect(screen.getByTestId('loading')).toHaveTextContent('not-loading');
    });

    expect(screen.getByTestId('authenticated')).toHaveTextContent('not-authenticated');
    expect(localStorage.getItem('intellifin_token')).toBeNull();
    expect(localStorage.getItem('intellifin_user')).toBeNull();
  });

  it('should redirect based on onboarding status', async () => {
    const mockAuthResponse = {
      token: 'new-token',
      user: {
        id: '123',
        email: '<EMAIL>',
        firstName: 'Test',
        lastName: 'User',
        onboardingStatus: 'PROFILE_COMPLETE', // Not fully onboarded
        emailVerified: true
      }
    };

    (fetch as jest.Mock).mockResolvedValueOnce({
      ok: true,
      json: async () => mockAuthResponse
    });

    renderWithAuth();

    await waitFor(() => {
      expect(screen.getByTestId('loading')).toHaveTextContent('not-loading');
    });

    await act(async () => {
      screen.getByTestId('login-btn').click();
    });

    await waitFor(() => {
      expect(mockPush).toHaveBeenCalledWith('/onboarding');
    });
  });
});
